import express from 'express';
import Stripe from 'stripe';
import { authRequired } from '../middleware/authRequired.js';
import { User } from '../db/models/User.js';
import { Subscription } from '../db/models/Subscription.js';
const router = express.Router();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
    apiVersion: '2023-10-16',
});
// Define subscription plans
const SUBSCRIPTION_PLANS = {
    basic: {
        name: 'Basic Plan',
        description: 'Access to all basic features',
        price_monthly: process.env.STRIPE_BASIC_PRICE_MONTHLY,
        price_yearly: process.env.STRIPE_BASIC_PRICE_YEARLY
    },
    premium: {
        name: 'Premium Plan',
        description: 'Full access to all features',
        price_monthly: process.env.STRIPE_PREMIUM_PRICE_MONTHLY,
        price_yearly: process.env.STRIPE_PREMIUM_PRICE_YEARLY
    },
    enterprise: {
        name: 'Enterprise Plan',
        description: 'Custom solutions for large organizations',
        price_monthly: process.env.STRIPE_ENTERPRISE_PRICE_MONTHLY,
        price_yearly: process.env.STRIPE_ENTERPRISE_PRICE_YEARLY
    }
};
// Get subscription plans
router.get('/plans', async (req, res) => {
    try {
        res.json(SUBSCRIPTION_PLANS);
    }
    catch (error) {
        console.error('Error fetching subscription plans:', error);
        res.status(500).json({ error: 'Failed to fetch subscription plans' });
    }
});
// Create checkout session
router.post('/create-checkout-session', (req, res, next) => {
    authRequired(req, res, next);
}, async (req, res) => {
    try {
        if (!req.user || !req.user._id) {
            return res.status(401).json({ error: 'Not authenticated' });
        }
        const { plan, interval } = req.body;
        if (!plan || !['basic', 'premium', 'enterprise'].includes(plan)) {
            return res.status(400).json({ error: 'Invalid subscription plan' });
        }
        if (!interval || !['month', 'year'].includes(interval)) {
            return res.status(400).json({ error: 'Invalid billing interval' });
        }
        // Get price ID based on plan and interval
        const priceId = interval === 'month'
            ? SUBSCRIPTION_PLANS[plan].price_monthly
            : SUBSCRIPTION_PLANS[plan].price_yearly;
        if (!priceId) {
            return res.status(400).json({ error: 'Invalid price configuration' });
        }
        // Create or retrieve Stripe customer
        let customerId;
        const existingCustomers = await stripe.customers.list({
            email: req.user.email,
            limit: 1
        });
        if (existingCustomers.data.length > 0) {
            customerId = existingCustomers.data[0].id;
        }
        else {
            const customer = await stripe.customers.create({
                email: req.user.email,
                name: req.user.name,
                metadata: {
                    userId: req.user._id.toString()
                }
            });
            customerId = customer.id;
        }
        // Create checkout session
        const session = await stripe.checkout.sessions.create({
            customer: customerId,
            payment_method_types: ['card'],
            line_items: [
                {
                    price: priceId,
                    quantity: 1
                }
            ],
            mode: 'subscription',
            success_url: `${process.env.FRONTEND_URL}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
            cancel_url: `${process.env.FRONTEND_URL}/payment/cancel`,
            metadata: {
                userId: req.user._id.toString(),
                plan,
                interval
            }
        });
        res.json({ url: session.url });
    }
    catch (error) {
        console.error('Error creating checkout session:', error);
        res.status(500).json({ error: 'Failed to create checkout session' });
    }
});
// Get user's active subscription
router.get('/subscription', (req, res, next) => {
    authRequired(req, res, next);
}, async (req, res) => {
    try {
        if (!req.user || !req.user._id) {
            return res.status(401).json({ error: 'Not authenticated' });
        }
        const user = await User.findById(req.user._id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        const subscription = await Subscription.findOne({ userId: user._id });
        if (!subscription || !subscription.subscriptionId) {
            return res.json({ hasActiveSubscription: false });
        }
        const stripeSubscription = await stripe.subscriptions.retrieve(subscription.subscriptionId);
        res.json({
            hasActiveSubscription: true,
            subscription: stripeSubscription
        });
    }
    catch (error) {
        console.error('Error fetching subscription:', error);
        res.status(500).json({ error: 'Failed to fetch subscription' });
    }
});
// Cancel subscription
router.post('/cancel-subscription', (req, res, next) => {
    authRequired(req, res, next);
}, async (req, res) => {
    try {
        if (!req.user || !req.user._id) {
            return res.status(401).json({ error: 'Not authenticated' });
        }
        const user = await User.findById(req.user._id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        const subscription = await Subscription.findOne({ userId: user._id });
        if (!subscription || !subscription.subscriptionId) {
            return res.status(404).json({ error: 'No subscription found' });
        }
        // Cancel at period end
        await stripe.subscriptions.update(subscription.subscriptionId, {
            cancel_at_period_end: true
        });
        // Update the subscription in the database
        subscription.cancelAtPeriodEnd = true;
        await subscription.save();
        res.json({
            message: 'Subscription will be canceled at the end of the current billing period',
            subscription: subscription.subscriptionId
        });
    }
    catch (error) {
        console.error('Error canceling subscription:', error);
        res.status(500).json({ error: 'Failed to cancel subscription' });
    }
});
// Webhook to handle Stripe events
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
    const signature = req.headers['stripe-signature'];
    if (!signature) {
        return res.status(400).json({ error: 'Missing Stripe signature' });
    }
    let event;
    try {
        event = stripe.webhooks.constructEvent(req.body, signature, process.env.STRIPE_WEBHOOK_SECRET);
    }
    catch (error) {
        console.error('Webhook signature verification failed:', error);
        return res.status(400).json({ error: 'Invalid signature' });
    }
    // Handle the event
    try {
        switch (event.type) {
            case 'checkout.session.completed': {
                const session = event.data.object;
                await handleCheckoutSessionCompleted(session);
                break;
            }
            case 'invoice.payment_succeeded': {
                const invoice = event.data.object;
                await handleInvoicePaymentSucceeded(invoice);
                break;
            }
            case 'customer.subscription.updated': {
                const subscription = event.data.object;
                await handleSubscriptionUpdated(subscription);
                break;
            }
            case 'customer.subscription.deleted': {
                const subscription = event.data.object;
                await handleSubscriptionDeleted(subscription);
                break;
            }
            default:
                console.log(`Unhandled event type: ${event.type}`);
        }
        res.json({ received: true });
    }
    catch (error) {
        console.error('Error handling webhook event:', error);
        res.status(500).json({ error: 'Failed to process webhook' });
    }
});
// Helper functions for webhook handling
async function handleCheckoutSessionCompleted(session) {
    const user = await User.findOne({ email: session.customer_email });
    if (user) {
        // Create or update subscription record
        const subscription = await Subscription.findOneAndUpdate({ userId: user._id }, {
            userId: user._id,
            subscriptionId: session.subscription,
            status: 'active',
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // Rough estimate
        }, { upsert: true, new: true });
        // Update user role based on the product purchased
        const lineItems = await stripe.checkout.sessions.listLineItems(session.id);
        for (const item of lineItems.data) {
            if (item.price && item.price.product) {
                const product = await stripe.products.retrieve(item.price.product);
                if (product.metadata.role) {
                    user.role = product.metadata.role;
                    await user.save();
                    break;
                }
            }
        }
        console.log(`Updated subscription for user ${user.email} with subscription ID: ${subscription.subscriptionId}`);
    }
}
async function handleInvoicePaymentSucceeded(invoice) {
    if (!invoice.subscription)
        return;
    const subscriptionId = invoice.subscription;
    const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId);
    const subscription = await Subscription.findOneAndUpdate({ subscriptionId }, {
        status: stripeSubscription.status,
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end
    }, { new: true });
    if (subscription) {
        console.log(`Updated subscription status for subscription ID: ${subscriptionId} to ${stripeSubscription.status}`);
    }
}
async function handleSubscriptionUpdated(stripeSubscription) {
    const subscriptionId = stripeSubscription.id;
    const subscription = await Subscription.findOneAndUpdate({ subscriptionId }, {
        status: stripeSubscription.status,
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end
    }, { new: true });
    if (subscription) {
        console.log(`Updated subscription status for subscription ID: ${subscriptionId} to ${stripeSubscription.status}`);
    }
}
async function handleSubscriptionDeleted(stripeSubscription) {
    const subscriptionId = stripeSubscription.id;
    const subscription = await Subscription.findOneAndUpdate({ subscriptionId }, {
        status: 'canceled',
        canceledAt: new Date()
    }, { new: true });
    if (subscription) {
        const user = await User.findById(subscription.userId);
        if (user) {
            user.role = 'basic';
            await user.save();
            console.log(`Downgraded user ${user.email} to basic plan due to subscription cancellation`);
        }
        console.log(`Marked subscription ID: ${subscriptionId} as canceled`);
    }
}
export default router;
