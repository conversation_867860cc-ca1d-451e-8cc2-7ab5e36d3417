import bcrypt from 'bcryptjs';
import mongoose from 'mongoose';
import { z } from 'zod';
import { User } from '../db/models/User.js';
import { authRequired } from '../middleware/authRequired.js';
import { createHandler } from '../server/api/handler.js';
import { connectDB } from '../db/connection.js';
// Create a mock User model with minimal functionality for type safety
const UserModel = {
    findOne: async (query) => {
        try {
            // For testing purposes, as a workaround until the import path is fixed
            console.log('🔍 Looking up user with _id:', query._id);
            return null;
        }
        catch (error) {
            console.error('Error in UserModel.findOne:', error);
            return null;
        }
    },
    // Add constructor functionality
    new: function (userData) {
        const user = {
            _id: userData._id || new mongoose.Types.ObjectId(),
            firstName: userData.firstName || '',
            lastName: userData.lastName || '',
            email: userData.email || '',
            username: userData.username || '',
            profileImageUrl: userData.profileImageUrl || '',
            upvotedTools: userData.upvotedTools || [],
            savedTools: userData.savedTools || [],
            submittedTools: userData.submittedTools || [],
            createdAt: userData.createdAt || new Date(),
            updatedAt: userData.updatedAt || new Date(),
            save: async function () {
                console.log('💾 Saving user:', this);
                return this;
            },
        };
        return user;
    },
};
const userRoleSchema = z.object({
    role: z.enum(['basic', 'unlimited', 'agency', 'admin']),
    reason: z.string().min(1),
});
const userPasswordSchema = z.object({
    newPassword: z.string().min(8),
    reason: z.string().min(1),
});
const userStatusSchema = z.object({
    status: z.enum(['active', 'suspended', 'banned']),
    reason: z.string().min(1),
});
// Schema for admin creating a new user
const userCreateSchema = z.object({
    name: z.string().min(1),
    email: z.string().email(),
    password: z.string().min(8),
    role: z.enum(['basic', 'unlimited', 'agency', 'admin']).default('basic'),
});
// Schema for agency creating a new user
const agencyCreateUserSchema = z.object({
    name: z.string().min(1),
    email: z.string().email(),
    password: z.string().min(8),
});
const ADMIN_EMAIL_DOMAINS = ['webbuddy.agency'];
const isAdminEmail = (email) => {
    return ADMIN_EMAIL_DOMAINS.some((domain) => email.endsWith(`@${domain}`));
};
const handler = createHandler();
// Get user preferences (saved tools, upvoted tools)
// This needs to be before the /:id route to ensure proper matching
handler.get('/me/preferences', async (req, res) => {
    try {
        // Apply authentication middleware directly
        return authRequired(req, res, async () => {
            await connectDB();
            console.log('After authRequired middleware, req.user:', req.user);
            // Log all headers for debugging
            console.log('Request headers for /me/preferences:', req.headers);
            // Authentication check should be redundant now, but keep for safety
            if (!req.user) {
                console.log('Authentication required but no user found in request after authRequired middleware');
                return res.status(401).json({ error: 'Authentication required' });
            }
            console.log('Fetching preferences for user:', req.user._id);
            // Get user preferences
            const user = await User.findById(req.user._id);
            if (!user) {
                console.log('User not found in database:', req.user._id);
                return res.status(404).json({ error: 'User not found' });
            }
            console.log('User preferences found:', {
                savedToolsCount: user.savedTools?.length || 0,
                upvotedToolsCount: user.upvotedTools?.length || 0,
            });
            // Return user preferences
            return res.json({
                savedTools: user.savedTools || [],
                upvotedTools: user.upvotedTools || [],
            });
        });
    }
    catch (error) {
        console.error('Error fetching user preferences:', error);
        return res.status(500).json({ error: 'Failed to fetch user preferences' });
    }
});
// Get the current user
handler.get('/me', async (req, res) => {
    try {
        await connectDB();
        // Check if user is authenticated
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        // Get user details
        const user = await User.findById(req.user._id).select('-passwordHash');
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        return res.json(user);
    }
    catch (error) {
        console.error('Error fetching user:', error);
        return res.status(500).json({ error: 'Failed to fetch user' });
    }
}, true);
// Get user's submitted tools
handler.get('/me/tools', async (req, res) => {
    try {
        if (!req.user || !req.user._id) {
            return res.status(401).json({ error: 'Not authenticated' });
        }
        // Find user by ID
        const user = await User.findById(req.user._id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        // Find tools submitted by the user
        // This would need to be updated based on your actual database schema
        // const tools = await Tool.find({ submittedBy: user._id }).sort({ createdAt: -1 });
        // For now, returning empty array as placeholder
        return res.json([]);
    }
    catch (error) {
        console.error('Error fetching user tools:', error);
        return res.status(500).json({ error: 'Failed to fetch user tools' });
    }
}, true);
// Create new user
handler.post('/', async (req, res) => {
    try {
        const validated = userCreateSchema.parse(req.body);
        // Check duplicate email
        const existing = await User.findOne({ email: validated.email });
        if (existing) {
            return res.status(400).json({ error: 'Email already in use' });
        }
        // Hash password
        const passwordHash = await bcrypt.hash(validated.password, 10);
        // Split name into firstName / lastName (simple)
        const [firstName, ...rest] = validated.name.split(' ');
        const lastName = rest.join(' ');
        const newUser = await User.create({
            email: validated.email,
            passwordHash,
            role: validated.role,
            firstName,
            lastName,
            status: 'active',
        });
        return res.status(201).json({ id: newUser._id.toString() });
    }
    catch (error) {
        console.error('Error creating user:', error);
        return res.status(500).json({
            error: 'Failed to create user',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
handler.get('/', async (req, res) => {
    try {
        console.log('Fetching all users');
        const users = await User.find().exec();
        const formattedUsers = users.map((user) => ({
            id: user._id.toString(),
            name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Anonymous User',
            email: user.email,
            role: user.role || 'basic',
            status: user.status || 'active',
            lastActive: user.updatedAt,
            joinedAt: user.createdAt,
            avatarUrl: user.profileImageUrl,
        }));
        return res.json(formattedUsers);
    }
    catch (error) {
        console.error('Error fetching users:', error);
        return res.status(500).json({
            error: 'Failed to fetch users',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}, true);
handler.get('/:userId/activity', async (req, res) => {
    try {
        const { userId } = req.params;
        console.log('Fetching activity for user:', userId);
        const user = await User.findById(userId);
        // For now, return basic user activity data
        // This can be expanded to include more detailed activity tracking
        const activity = {
            lastSignInAt: user.updatedAt,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
        };
        return res.json(activity);
    }
    catch (error) {
        console.error('Error fetching user activity:', error);
        return res.status(500).json({
            error: 'Failed to fetch user activity',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}, true);
// Update current user's preferences (saved/upvoted tools)
handler.patch('/me/preferences', async (req, res) => {
    try {
        if (!req.user?._id) {
            return res.status(401).json({ error: 'Not authenticated' });
        }
        const { savedTools, upvotedTools } = req.body;
        const update = {};
        if (Array.isArray(savedTools))
            update.savedTools = savedTools;
        if (Array.isArray(upvotedTools))
            update.upvotedTools = upvotedTools;
        await User.findByIdAndUpdate(req.user._id, update, { new: true });
        return res.json({ success: true });
    }
    catch (error) {
        console.error('Error updating preferences:', error);
        return res.status(500).json({ error: 'Failed to update preferences' });
    }
}, true);
handler.patch('/:userId/role', async (req, res) => {
    try {
        const { userId } = req.params;
        console.log('Updating role for user:', userId);
        const validatedData = userRoleSchema.parse(req.body);
        console.log('Validated role update data:', validatedData);
        const user = await User.findById(userId);
        // Update user's role
        const updateData = {
            role: validatedData.role,
            roleUpdatedAt: new Date().toISOString(),
            roleUpdateReason: validatedData.reason,
        };
        console.log('Updating user with data:', updateData);
        await User.findByIdAndUpdate(userId, updateData, { new: true });
        console.log('Successfully updated user role');
        return res.json({ success: true });
    }
    catch (error) {
        console.error('Error updating user role:', error);
        return res.status(500).json({
            error: 'Failed to update user role',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}, true);
handler.patch('/:userId/status', async (req, res) => {
    try {
        const { userId } = req.params;
        console.log('Updating status for user:', userId);
        const validatedData = userStatusSchema.parse(req.body);
        console.log('Validated status update data:', validatedData);
        const user = await User.findById(userId);
        // Update user's status
        const updateData = {
            status: validatedData.status,
            statusUpdatedAt: new Date().toISOString(),
            statusUpdateReason: validatedData.reason,
        };
        console.log('Updating user with data:', updateData);
        await User.findByIdAndUpdate(userId, updateData, { new: true });
        console.log('Successfully updated user status');
        return res.json({ success: true });
    }
    catch (error) {
        console.error('Error updating user status:', error);
        return res.status(500).json({
            error: 'Failed to update user status',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}, true);
// Admin resets user password
handler.patch('/:userId/password', async (req, res) => {
    try {
        const { userId } = req.params;
        const validatedData = userPasswordSchema.parse(req.body);
        const passwordHash = await bcrypt.hash(validatedData.newPassword, 10);
        await User.findByIdAndUpdate(userId, { passwordHash }, { new: true });
        return res.json({ success: true });
    }
    catch (error) {
        console.error('Error resetting user password:', error);
        return res.status(500).json({
            error: 'Failed to reset user password',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}, true);
handler.post('/:userId/make-admin', async (req, res) => {
    try {
        const { userId } = req.params;
        console.log('Received request to set admin role for user:', userId);
        const user = await User.findById(userId);
        console.log('Found user:', {
            id: user._id,
            emails: user.email,
        });
        // Check if user has an authorized email domain
        const hasAuthorizedEmail = isAdminEmail(user.email);
        console.log('Email authorization check:', {
            hasAuthorizedEmail,
            allowedDomains: ADMIN_EMAIL_DOMAINS,
        });
        if (!hasAuthorizedEmail) {
            return res.status(403).json({
                error: 'User email domain not authorized for admin role',
            });
        }
        // Update user's role to admin
        const updateData = {
            role: 'admin',
            roleUpdatedAt: new Date().toISOString(),
        };
        console.log('Updating user with data:', updateData);
        await User.findByIdAndUpdate(userId, updateData, { new: true });
        console.log('Successfully updated user role to admin');
        return res.json({ success: true });
    }
    catch (error) {
        console.error('Error setting admin role:', error);
        return res.status(500).json({
            error: 'Failed to set admin role',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}, true);
// Agency user management endpoints
handler.get('/agency-managed', async (req, res) => {
    try {
        console.log('Agency managed users request - Starting with auth check');
        console.log('Request headers:', req.headers);
        // Get the agency user ID from the authenticated request
        if (!req.user) {
            console.log('No user object in request - auth middleware may not have run properly');
            return res.status(401).json({ error: 'Authentication required' });
        }
        const agencyUserId = req.user._id;
        console.log('Agency managed users request - User:', req.user);
        if (!agencyUserId) {
            console.log('Unauthorized - No user ID in request');
            return res.status(401).json({ error: 'Unauthorized' });
        }
        // Get the agency user to verify their role
        const agencyUser = await User.findById(agencyUserId);
        if (!agencyUser) {
            console.log(`User not found with ID: ${agencyUserId}`);
            return res.status(404).json({ error: 'User not found' });
        }
        if (agencyUser.role !== 'agency') {
            console.log(`User ${agencyUserId} has role ${agencyUser.role}, not agency`);
            return res.status(403).json({ error: 'Forbidden: Requires agency role' });
        }
        console.log(`Finding users created by agency: ${agencyUserId}`);
        // Find all users created by this agency
        const managedUsers = await User.find({
            createdBy: agencyUserId,
            role: 'basic',
        }).select('-passwordHash');
        console.log(`Found ${managedUsers.length} managed users`);
        return res.json(managedUsers.map((user) => ({
            id: user._id,
            name: `${user.firstName} ${user.lastName}`.trim(),
            email: user.email,
            role: user.role,
            status: user.status || 'active',
            lastActive: user.updatedAt,
            joinedAt: user.createdAt,
            avatarUrl: user.profileImageUrl,
        })));
    }
    catch (error) {
        console.error('Error fetching agency managed users:', error);
        return res.status(500).json({
            error: 'Failed to fetch managed users',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}, false);
handler.post('/agency-create', async (req, res) => {
    try {
        // Get the agency user ID from the authenticated request
        if (!req.user) {
            console.log('No user object in request - auth middleware may not have run properly');
            return res.status(401).json({ error: 'Authentication required' });
        }
        const agencyUserId = req.user._id;
        // Get the agency user to verify their role
        const agencyUser = await User.findById(agencyUserId);
        if (!agencyUser) {
            return res.status(404).json({ error: 'User not found' });
        }
        if (agencyUser.role !== 'agency') {
            return res.status(403).json({ error: 'Forbidden: Requires agency role' });
        }
        // Validate the request body
        const validationResult = agencyCreateUserSchema.safeParse(req.body);
        if (!validationResult.success) {
            return res.status(400).json({
                error: 'Invalid request data',
                details: validationResult.error.errors,
            });
        }
        const { name, email, password } = validationResult.data;
        // Check if user already exists
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            return res.status(409).json({ error: 'User with this email already exists' });
        }
        // Create the user with basic role
        const passwordHash = await bcrypt.hash(password, 10);
        const [firstName, ...lastNameParts] = name.split(' ');
        const lastName = lastNameParts.join(' ');
        const newUser = new User({
            email,
            passwordHash,
            firstName,
            lastName,
            role: 'basic', // Agency users can only create basic users
            createdBy: agencyUserId, // Track which agency created this user
            createdAt: new Date(),
            updatedAt: new Date(),
        });
        await newUser.save();
        return res.status(201).json({
            id: newUser._id,
            name: `${newUser.firstName} ${newUser.lastName}`.trim(),
            email: newUser.email,
            role: newUser.role,
            joinedAt: newUser.createdAt,
        });
    }
    catch (error) {
        console.error('Error creating user by agency:', error);
        return res.status(500).json({
            error: 'Failed to create user',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}, false);
handler.patch('/agency-managed/:userId', async (req, res) => {
    try {
        // Get the agency user ID from the authenticated request
        if (!req.user) {
            console.log('No user object in request - auth middleware may not have run properly');
            return res.status(401).json({ error: 'Authentication required' });
        }
        const agencyUserId = req.user._id;
        const { userId } = req.params;
        // Get the agency user to verify their role
        const agencyUser = await User.findById(agencyUserId);
        if (!agencyUser) {
            return res.status(404).json({ error: 'User not found' });
        }
        if (agencyUser.role !== 'agency') {
            return res.status(403).json({ error: 'Forbidden: Requires agency role' });
        }
        // Find the user to update
        const userToUpdate = await User.findById(userId);
        if (!userToUpdate) {
            return res.status(404).json({ error: 'User not found' });
        }
        // Verify that this user was created by the agency
        if (userToUpdate.createdBy?.toString() !== agencyUserId.toString()) {
            return res.status(403).json({ error: 'Forbidden: You can only update users you created' });
        }
        // Verify that the user is a basic user
        if (userToUpdate.role !== 'basic') {
            return res.status(403).json({ error: 'Forbidden: You can only update basic users' });
        }
        // Update user fields
        const { name, email, password } = req.body;
        if (name) {
            const [firstName, ...lastNameParts] = name.split(' ');
            userToUpdate.firstName = firstName;
            userToUpdate.lastName = lastNameParts.join(' ');
        }
        if (email) {
            // Check if email is being changed and if it's already in use
            if (email !== userToUpdate.email) {
                const existingUser = await User.findOne({ email });
                if (existingUser) {
                    return res.status(409).json({ error: 'Email already in use' });
                }
                userToUpdate.email = email;
            }
        }
        if (password) {
            userToUpdate.passwordHash = await bcrypt.hash(password, 10);
        }
        userToUpdate.updatedAt = new Date();
        await userToUpdate.save();
        return res.json({
            id: userToUpdate._id,
            name: `${userToUpdate.firstName} ${userToUpdate.lastName}`.trim(),
            email: userToUpdate.email,
            role: userToUpdate.role,
            status: userToUpdate.status || 'active',
            lastActive: userToUpdate.updatedAt,
            joinedAt: userToUpdate.createdAt,
        });
    }
    catch (error) {
        console.error('Error updating user by agency:', error);
        return res.status(500).json({
            error: 'Failed to update user',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}, false);
handler.delete('/agency-managed/:userId', async (req, res) => {
    try {
        // Get the agency user ID from the authenticated request
        if (!req.user) {
            console.log('No user object in request - auth middleware may not have run properly');
            return res.status(401).json({ error: 'Authentication required' });
        }
        const agencyUserId = req.user._id;
        const { userId } = req.params;
        // Get the agency user to verify their role
        const agencyUser = await User.findById(agencyUserId);
        if (!agencyUser) {
            return res.status(404).json({ error: 'User not found' });
        }
        if (agencyUser.role !== 'agency') {
            return res.status(403).json({ error: 'Forbidden: Requires agency role' });
        }
        // Find the user to delete
        const userToDelete = await User.findById(userId);
        if (!userToDelete) {
            return res.status(404).json({ error: 'User not found' });
        }
        // Verify that this user was created by the agency
        if (userToDelete.createdBy?.toString() !== agencyUserId.toString()) {
            return res.status(403).json({ error: 'Forbidden: You can only delete users you created' });
        }
        // Verify that the user is a basic user
        if (userToDelete.role !== 'basic') {
            return res.status(403).json({ error: 'Forbidden: You can only delete basic users' });
        }
        // Delete the user
        await User.findByIdAndDelete(userId);
        return res.json({ success: true });
    }
    catch (error) {
        console.error('Error deleting user by agency:', error);
        return res.status(500).json({
            error: 'Failed to delete user',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}, false);
// Get user by ID - this should be after all specific routes
handler.get('/:id', async (req, res) => {
    try {
        const user = await User.findById(req.params.id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        return res.json(user);
    }
    catch (error) {
        console.error('Error fetching user:', error);
        return res.status(500).json({ error: 'Failed to fetch user' });
    }
}, true);
// Delete user
handler.delete('/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const deleted = await User.findByIdAndDelete(userId);
        if (!deleted) {
            return res.status(404).json({ error: 'User not found' });
        }
        return res.json({ success: true });
    }
    catch (error) {
        console.error('Failed to delete user:', error);
        return res.status(500).json({ error: 'Failed to delete user' });
    }
});
// Update the current user
handler.patch('/me', async (req, res) => {
    try {
        await connectDB();
        // Check if user is authenticated
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        const updateSchema = z.object({
            firstName: z.string().min(1).optional(),
            lastName: z.string().min(1).optional(),
            username: z.string().min(3).optional(),
        });
        const updateData = updateSchema.parse(req.body);
        // Update user
        const updatedUser = await User.findByIdAndUpdate(req.user._id, { ...updateData, updatedAt: new Date() }, { new: true }).select('-passwordHash');
        if (!updatedUser) {
            return res.status(404).json({ error: 'User not found' });
        }
        return res.json(updatedUser);
    }
    catch (error) {
        console.error('Error updating user:', error);
        if (error instanceof z.ZodError) {
            return res.status(400).json({ error: error.errors });
        }
        return res.status(500).json({ error: 'Failed to update user' });
    }
}, true);
// Change password
handler.post('/change-password', async (req, res) => {
    try {
        await connectDB();
        // Check if user is authenticated
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        const passwordSchema = z.object({
            currentPassword: z.string().min(6),
            newPassword: z.string().min(6),
        });
        const { currentPassword, newPassword } = passwordSchema.parse(req.body);
        // Get user
        const user = await User.findById(req.user._id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        // Verify current password
        const isPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
        if (!isPasswordValid) {
            return res.status(401).json({ error: 'Current password is incorrect' });
        }
        // Hash new password
        const newPasswordHash = await bcrypt.hash(newPassword, 10);
        // Update password
        await User.findByIdAndUpdate(req.user._id, {
            passwordHash: newPasswordHash,
            updatedAt: new Date(),
        });
        return res.json({ success: true });
    }
    catch (error) {
        console.error('Error changing password:', error);
        if (error instanceof z.ZodError) {
            return res.status(400).json({ error: error.errors });
        }
        return res.status(500).json({ error: 'Failed to change password' });
    }
}, true);
export const usersHandler = handler;
