import mongoose from 'mongoose';
// Define the schema for the author subdocument
const authorSchema = new mongoose.Schema({
    name: { type: String, required: true },
    avatar: { type: String, required: true },
});
// Define the main blog post schema
const blogPostSchema = new mongoose.Schema({
    title: { type: String, required: true },
    slug: { type: String, required: true, unique: true },
    excerpt: { type: String, required: true },
    content: { type: String, required: true },
    date: { type: String, required: true },
    author: { type: authorSchema, required: true },
    category: { type: String, required: true },
    readTime: { type: String, required: true },
    imageUrl: { type: String, required: true },
    tags: [{ type: String }],
    status: {
        type: String,
        enum: ['draft', 'published'],
        default: 'draft',
    },
    likes: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
}, {
    timestamps: true,
});
// Create indexes
blogPostSchema.index({ title: 'text', excerpt: 'text', content: 'text' });
export const BlogPost = (mongoose.models.BlogPost || mongoose.model('BlogPost', blogPostSchema));
