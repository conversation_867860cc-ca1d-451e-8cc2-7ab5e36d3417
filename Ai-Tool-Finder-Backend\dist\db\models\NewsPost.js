import mongoose from 'mongoose';
// Define the schema for the author subdocument
const authorSchema = new mongoose.Schema({
    name: { type: String, required: true },
    avatar: { type: String, required: true },
});
// Define the main news post schema
const newsPostSchema = new mongoose.Schema({
    title: { type: String, required: true },
    slug: { type: String, required: true, unique: true },
    excerpt: { type: String, required: true },
    content: { type: String, required: true },
    date: { type: String, required: true },
    author: { type: authorSchema, required: true },
    category: { type: String, required: true },
    imageUrl: { type: String, required: true },
    tags: [{ type: String }],
    status: {
        type: String,
        enum: ['draft', 'published'],
        default: 'draft',
    },
    source: { type: String, required: true },
    sourceUrl: { type: String, required: true },
    views: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
}, {
    timestamps: true,
});
// Create indexes
newsPostSchema.index({ title: 'text', excerpt: 'text', content: 'text' });
export const NewsPost = (mongoose.models.NewsPost || mongoose.model('NewsPost', newsPostSchema));
