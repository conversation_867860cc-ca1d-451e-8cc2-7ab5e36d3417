import mongoose from 'mongoose';
const salesInquirySchema = new mongoose.Schema({
    fullName: { type: String, required: true },
    email: { type: String, required: true },
    companyName: { type: String, required: true },
    monthlyBudget: { type: String, required: true },
    message: { type: String, required: true },
    status: {
        type: String,
        enum: ['new', 'contacted', 'closed'],
        default: 'new',
    },
    submittedAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
});
// Create indexes
salesInquirySchema.index({ email: 1 });
salesInquirySchema.index({ status: 1 });
salesInquirySchema.index({ submittedAt: -1 });
// Update the updatedAt timestamp on save
salesInquirySchema.pre('save', function (next) {
    this.updatedAt = new Date();
    next();
});
export const SalesInquiry = (mongoose.models.SalesInquiry || mongoose.model('SalesInquiry', salesInquirySchema));
