import mongoose from 'mongoose';
const toolSubmissionSchema = new mongoose.Schema({
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    toolName: { type: String, required: true },
    description: { type: String, required: true },
    websiteUrl: { type: String, required: true },
    logoUrl: { type: String, required: true },
    category: { type: String, required: true },
    pricingType: { type: String, required: true },
    keyHighlights: [{ type: String }],
    twitterUrl: { type: String },
    githubUrl: { type: String },
    status: {
        type: String,
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending'
    },
}, {
    timestamps: {
        createdAt: 'submittedAt',
        updatedAt: 'updatedAt'
    }
});
export const ToolSubmission = (mongoose.models.ToolSubmission || mongoose.model('ToolSubmission', toolSubmissionSchema));
