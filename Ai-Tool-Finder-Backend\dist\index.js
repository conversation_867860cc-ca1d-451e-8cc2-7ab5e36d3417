import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import { toolsHandler } from './api/tools.js';
import { blogHandler } from './api/blog.js';
import { newsHandler } from './api/news.js';
import { salesInquiriesHandler } from './api/salesInquiries.js';
import { connectDB } from './db/connection.js';
import { usersHandler } from './api/users.js';
import reviewsRouter from './api/reviews.js';
import configRouter from './api/config.js';
import paymentsRouter from './api/payments.js';
import authRouter from './api/auth.js';
import sponsorshipsRouter from './api/sponsorships.js';
const app = express();
const port = process.env.PORT || 3005;
// Configure CORS with specific options
app.use(cors({
    origin: function (origin, callback) {
        const allowedOrigins = [
            'http://localhost:8080',
            'http://localhost:3000',
            'http://localhost:5173', // Vite default port
            process.env.CORS_ORIGIN
        ].filter(Boolean);
        // Allow requests with no origin (like mobile apps, curl, etc)
        if (!origin || allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        }
        else {
            console.log(`CORS blocked: ${origin} not allowed`);
            callback(null, false);
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'stripe-signature',
        'X-Requested-With',
        'Accept',
        'Origin',
        'Access-Control-Allow-Origin'
    ],
    exposedHeaders: ['Content-Range', 'X-Total-Count']
}));
// Enable pre-flight requests for all routes
app.options('*', cors({
    origin: function (origin, callback) {
        const allowedOrigins = [
            'http://localhost:8080',
            'http://localhost:3000',
            'http://localhost:5173', // Vite default port
            process.env.CORS_ORIGIN
        ].filter(Boolean);
        // Allow requests with no origin (like mobile apps, curl, etc)
        if (!origin || allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        }
        else {
            console.log(`CORS blocked preflight: ${origin} not allowed`);
            callback(null, false);
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'stripe-signature',
        'X-Requested-With',
        'Accept',
        'Origin',
        'Access-Control-Allow-Origin'
    ]
}));
// Serve static files from the public directory
app.use(express.static('public'));
console.log('Serving static files from:', process.cwd() + '/public');
// Parse JSON requests (except for Stripe webhook route)
app.use((req, res, next) => {
    if (req.path === '/api/payments/webhook' && req.method === 'POST') {
        next();
    }
    else {
        express.json()(req, res, next);
    }
});
// Debug middleware
app.use((req, res, next) => {
    // Add CORS headers to all responses
    const origin = req.headers.origin;
    const allowedOrigins = [
        'http://localhost:8080',
        'http://localhost:3000',
        'http://localhost:5173', // Vite default port
        process.env.CORS_ORIGIN
    ].filter(Boolean);
    if (origin && allowedOrigins.includes(origin)) {
        res.header('Access-Control-Allow-Origin', origin);
    }
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,PATCH,DELETE,OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, stripe-signature, X-Requested-With, Accept, Origin');
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
    // Log authorization header for debugging
    if (req.headers.authorization) {
        console.log('Authorization header present:', req.headers.authorization.substring(0, 20) + '...');
    }
    else {
        console.log('No Authorization header present');
    }
    // Check if this is a public route
    const isPublicRoute = (req.url.startsWith('/health') ||
        req.url.startsWith('/api/auth/login') ||
        req.url.startsWith('/api/auth/signup') ||
        (req.url === '/api/tools' && req.method === 'GET'));
    // This is just for logging, it doesn't affect route handling
    console.log('Is public route:', isPublicRoute, 'for', req.method, req.url);
    // Don't log bodies of file uploads as they're binary data
    if ((req.method === 'POST' || req.method === 'PATCH' || req.method === 'PUT') &&
        req.path !== '/api/payments/webhook' &&
        !req.path.includes('upload-') &&
        !req.headers['content-type']?.includes('multipart/form-data')) {
        console.log('Request body:', req.body);
    }
    next();
});
// Connect to MongoDB
connectDB().catch(console.error);
// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({ status: 'ok', message: 'Server is running' });
});
// API Routes
app.use('/api/tools', toolsHandler.middleware());
app.use('/api/blog', blogHandler.middleware());
app.use('/api/news', newsHandler.middleware());
app.use('/api/sales-inquiries', salesInquiriesHandler.middleware());
app.use('/api/users', usersHandler.middleware());
// New API Routes for v1.0.4
app.use('/api/reviews', reviewsRouter);
app.use('/api/config', configRouter);
app.use('/api/payments', paymentsRouter);
app.use('/api/sponsorships', sponsorshipsRouter);
// Auth routes
app.use('/api/auth', authRouter);
// Add better error handling for Express
app.use((err, req, res, next) => {
    console.error('Express error caught:', {
        message: err.message,
        stack: err.stack,
        name: err.name,
        code: err.code,
        status: err.status
    });
    // Send detailed error in development, simplified in production
    if (process.env.NODE_ENV === 'development') {
        res.status(err.status || 500).json({
            error: err.message,
            stack: err.stack,
            name: err.name,
            code: err.code
        });
    }
    else {
        res.status(err.status || 500).json({
            error: 'Server error occurred'
        });
    }
});
// Start server
app.listen(port, () => {
    console.log(`Server is running on port ${port}`);
    console.log(`Server started successfully`);
});
