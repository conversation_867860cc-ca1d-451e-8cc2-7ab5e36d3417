import jwt from 'jsonwebtoken';
const JWT_SECRET = process.env.JWT_SECRET || 'change_this_secret';
export function authRequired(req, res, next) {
    console.log('Auth middleware running, checking for token');
    const authHeader = req.headers.authorization;
    if (!authHeader) {
        console.log('No authorization header found');
        return res.status(401).json({ error: 'Missing authorization header' });
    }
    if (!authHeader.startsWith('Bearer ')) {
        console.log('Authorization header format invalid, must start with "Bearer "');
        return res.status(401).json({ error: 'Invalid token format' });
    }
    const token = authHeader.split(' ')[1];
    if (!token) {
        console.log('No token found in authorization header');
        return res.status(401).json({ error: 'Missing token' });
    }
    try {
        console.log('Verifying token:', token.substring(0, 20) + '...');
        console.log('Using JWT_SECRET:', JWT_SECRET ? 'Secret exists (not showing for security)' : 'No secret found');
        const payload = jwt.verify(token, JWT_SECRET);
        console.log('Token verified successfully, payload:', {
            id: payload.id,
            email: payload.email || 'not provided',
            role: payload.role || 'not provided'
        });
        req.user = {
            _id: payload.id,
            email: payload.email || '<EMAIL>', // Provide a default if email is missing
            role: payload.role,
            name: payload.name
        };
        console.log('User object set on request:', req.user);
        next();
    }
    catch (err) {
        console.error('Token verification failed:', err);
        return res.status(401).json({ error: 'Invalid token' });
    }
}
export function roleRequired(...allowed) {
    return (req, res, next) => {
        if (!req.user)
            return res.status(401).json({ error: 'Unauthorized' });
        if (!allowed.includes(req.user.role)) {
            return res.status(403).json({ error: 'Forbidden' });
        }
        next();
    };
}
