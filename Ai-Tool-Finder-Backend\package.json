{"name": "backend", "version": "1.0.4", "description": "Backend server for Ai-Hunt application", "main": "src/index.ts", "type": "module", "scripts": {"start": "node dist/index.js", "dev": "tsx watch src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.9", "@types/node": "^20.11.17", "@types/supertest": "^6.0.2", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cloudinary": "^1.41.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.4", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "form-data": "^4.0.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.1.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "stripe": "^14.18.0", "typescript": "^5.3.3", "zod": "^3.22.4"}, "devDependencies": {"@types/multer": "^1.4.9", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.3", "prettier": "^3.2.5", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsx": "^4.19.2"}, "engines": {"node": ">=18.0.0"}}