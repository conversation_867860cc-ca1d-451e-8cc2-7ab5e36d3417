import { Router } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { User } from '../db/models/User.js';
import { authRequired, AuthRequest } from '../middleware/authRequired.js';

const router = Router();
const JWT_SECRET = process.env.JWT_SECRET || 'change_this_secret';
const JWT_EXPIRES_IN = '7d';

// Helper to generate JWT
function signToken(user: { _id: any; role: string }) {
  return jwt.sign({ id: user._id.toString(), role: user.role }, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
  });
}

// POST /api/auth/signup
router.post('/signup', async (req, res) => {
  try {
    const { email, password, role } = req.body;
    if (!email || !password) return res.status(400).json({ error: 'Email and password required' });

    const existing = await User.findOne({ email });
    if (existing) return res.status(409).json({ error: 'Email already in use' });

    const hash = await bcrypt.hash(password, 10);
    const user = await User.create({ email, passwordHash: hash, role: role || 'basic' });
    const token = signToken(user);
    return res.status(201).json({ token, user: { id: user._id, email: user.email, role: user.role } });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ error: 'Signup failed' });
  }
});

// POST /api/auth/login
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    if (!email || !password) return res.status(400).json({ error: 'Email and password required' });

    const user = await User.findOne({ email });
    if (!user) return res.status(401).json({ error: 'Invalid credentials' });

    const ok = await bcrypt.compare(password, user.passwordHash);
    if (!ok) return res.status(401).json({ error: 'Invalid credentials' });

    const token = signToken(user);
    return res.status(200).json({ token, user: { id: user._id, email: user.email, role: user.role } });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ error: 'Login failed' });
  }
});

// POST /api/auth/change-password
router.post('/change-password', authRequired, async (req: AuthRequest, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: 'Current and new password required' });
    }
    const user = await User.findById(req.user!._id);
    if (!user) return res.status(404).json({ error: 'User not found' });
    const ok = await bcrypt.compare(currentPassword, user.passwordHash);
    if (!ok) return res.status(401).json({ error: 'Current password incorrect' });
    user.passwordHash = await bcrypt.hash(newPassword, 10);
    await user.save();
    return res.json({ message: 'Password updated' });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ error: 'Failed to change password' });
  }
});

// PUT /api/auth/me (update profile)
router.put('/me', authRequired, async (req: AuthRequest, res) => {
  try {
    const updates: any = {};
    const { firstName, lastName, username, profileImageUrl } = req.body;
    if (firstName !== undefined) updates.firstName = firstName;
    if (lastName !== undefined) updates.lastName = lastName;
    if (username !== undefined) updates.username = username;
    if (profileImageUrl !== undefined) updates.profileImageUrl = profileImageUrl;

    const user = await User.findByIdAndUpdate(req.user!._id, updates, { new: true }).select('-passwordHash');
    return res.json({ user });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ error: 'Failed to update profile' });
  }
});

// GET /api/auth/me
router.get('/me', authRequired, async (req: AuthRequest, res) => {
  try {
    console.log('GET /api/auth/me - Authenticated user ID:', req.user?._id);
    
    if (!req.user || !req.user._id) {
      console.log('GET /api/auth/me - No user in request after auth middleware');
      return res.status(401).json({ error: 'Authentication required' });
    }
    
    const user = await User.findById(req.user._id).select('-passwordHash');
    
    if (!user) {
      console.log(`GET /api/auth/me - User not found with ID: ${req.user._id}`);
      return res.status(404).json({ error: 'User not found' });
    }
    
    console.log(`GET /api/auth/me - Successfully retrieved user: ${user.email}`);
    return res.json({ user });
  } catch (error) {
    console.error('GET /api/auth/me - Error:', error);
    return res.status(500).json({ error: 'Failed to retrieve user information' });
  }
});

export default router;
