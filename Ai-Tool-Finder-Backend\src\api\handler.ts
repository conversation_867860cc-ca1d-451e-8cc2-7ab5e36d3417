import { Request, Response } from 'express';
import { authRequired, AuthRequest } from '../middleware/authRequired.js';

// Extend the Request type to include auth
declare global {
    namespace Express {
        interface Request {
            auth?: any;
            user?: {
                _id: string;
                email: string;
                role: string;
                name?: string;
            };
        }
    }
}

type Handler = {
    get: (path: string, handler: (req: Request, res: Response) => Promise<any>, requiresAuth?: boolean) => void;
    post: (path: string, handler: (req: Request, res: Response) => Promise<any>, requiresAuth?: boolean) => void;
    patch: (path: string, handler: (req: Request, res: Response) => Promise<any>, requiresAuth?: boolean) => void;
    put: (path: string, handler: (req: Request, res: Response) => Promise<any>, requiresAuth?: boolean) => void;
    delete: (path: string, handler: (req: Request, res: Response) => Promise<any>, requiresAuth?: boolean) => void;
    middleware: () => any;
};

export function createHandler(): Handler {
    const routes = new Map<string, Map<string, { 
        handler: (req: Request, res: Response) => Promise<void>,
        requiresAuth: boolean
    }>>();

    const handler: Handler = {
        get(path, routeHandler, requiresAuth = false) {
            if (!routes.has('GET')) routes.set('GET', new Map());
            routes.get('GET')!.set(path, { handler: routeHandler, requiresAuth });
        },
        post(path, routeHandler, requiresAuth = false) {
            if (!routes.has('POST')) routes.set('POST', new Map());
            routes.get('POST')!.set(path, { handler: routeHandler, requiresAuth });
        },
        patch(path, routeHandler, requiresAuth = false) {
            if (!routes.has('PATCH')) routes.set('PATCH', new Map());
            routes.get('PATCH')!.set(path, { handler: routeHandler, requiresAuth });
        },
        put(path, routeHandler, requiresAuth = false) {
            if (!routes.has('PUT')) routes.set('PUT', new Map());
            routes.get('PUT')!.set(path, { handler: routeHandler, requiresAuth });
        },
        delete(path, routeHandler, requiresAuth = false) {
            if (!routes.has('DELETE')) routes.set('DELETE', new Map());
            routes.get('DELETE')!.set(path, { handler: routeHandler, requiresAuth });
        },
        middleware() {
            return async (req: Request, res: Response, next: any) => {
                try {
                    // Convert route patterns to regex
                    const method = req.method;
                    const methodRoutes = routes.get(method);

                    if (!methodRoutes) {
                        return res.status(405).json({ error: 'Method not allowed' });
                    }

                    console.log('Processing request:', { method, path: req.path });
                    console.log('Available routes:', Array.from(methodRoutes.keys()));

                    let matchedRoute: { 
                        handler: (req: Request, res: Response) => Promise<void>,
                        requiresAuth: boolean
                    } | undefined;
                    let matchedParams: { [key: string]: string } = {};

                    // First, sort routes to prioritize more specific ones
                    // (routes with more segments and fewer parameters)
                    const sortedRoutes = Array.from(methodRoutes.entries()).sort((a, b) => {
                        const aSegments = a[0].split('/').length;
                        const bSegments = b[0].split('/').length;
                        
                        // More segments = more specific
                        if (aSegments !== bSegments) {
                            return bSegments - aSegments;
                        }
                        
                        // More static path parts = more specific
                        const aParams = (a[0].match(/:[a-zA-Z]+/g) || []).length;
                        const bParams = (b[0].match(/:[a-zA-Z]+/g) || []).length;
                        return aParams - bParams;
                    });

                    console.log('Sorted routes for method', method, sortedRoutes.map(r => ({ path: r[0], auth: r[1].requiresAuth })));

                    console.log('Sorted routes:', sortedRoutes.map(r => r[0]));

                    for (const [pattern, route] of sortedRoutes) {
                        // Special case for exact matches
                        if (pattern === req.path) {
                            matchedRoute = route;
                            matchedParams = {};
                            break;
                        }

                        // Prepare the regex pattern by replacing parameter placeholders
                        const patternSegments = pattern.split('/');
                        const pathSegments = req.path.split('/');
                        
                        // Check if the segments count matches, disregarding empty segments
                        const filteredPatternSegments = patternSegments.filter(s => s !== '');
                        const filteredPathSegments = pathSegments.filter(s => s !== '');
                        
                        console.log(`Comparing route "${pattern}" with path "${req.path}":`, {
                            patternSegments: filteredPatternSegments,
                            pathSegments: filteredPathSegments,
                            segmentsMatch: filteredPatternSegments.length === filteredPathSegments.length
                        });
                        
                        if (filteredPatternSegments.length !== filteredPathSegments.length) {
                            continue;
                        }
                        
                        // Convert Express-style route pattern to a regex pattern
                        const regexPattern = pattern
                            .replace(/:[a-zA-Z]+/g, '([^/]+)')
                            .replace(/\//g, '\\/');
                        const regex = new RegExp(`^${regexPattern}\/?$`);
                        
                        console.log('[DEBUG] Matching...', {
                            path: req.path,
                            pattern: pattern,
                            requiresAuth: route.requiresAuth
                        });

                        console.log('Route matching:', { 
                            pattern, 
                            regexPattern,
                            requestPath: req.path, 
                            matches: regex.test(req.path) 
                        });

                        if (regex.test(req.path)) {
                            // Extract parameter names from the pattern
                            const paramNames = (pattern.match(/:[a-zA-Z]+/g) || [])
                                .map(param => param.substring(1));
                            
                            // Extract parameter values from the path
                            const paramValues = req.path.match(regex)?.slice(1) || [];
                            
                            // Create key-value pairs of parameter names and values
                            const params = Object.fromEntries(
                                paramNames.map((name, i) => [name, paramValues[i]])
                            );
                            
                            matchedRoute = route;
                            matchedParams = params;
                            break;
                        }
                    }

                    if (!matchedRoute) {
                        return res.status(404).json({ error: 'Not found' });
                    }

                    console.log('Found matching handler:', req.path);
                    console.log('Extracted params:', matchedParams);
                    console.log('Route requires auth:', matchedRoute.requiresAuth);

                    // Add params to request object
                    req.params = { ...req.params, ...matchedParams };

                    // Handle authentication if required
                    if (matchedRoute.requiresAuth) {
                        console.log(`Route ${req.path} requires auth, applying authRequired middleware`);
                        
                        // Apply authentication middleware and handle the response properly
                        return authRequired(req as AuthRequest, res, async () => {
                            try {
                                // This will only be called if authentication passes
                                console.log(`Auth passed for ${req.path}, calling handler with user:`, req.user);
                                await matchedRoute!.handler(req, res);
                            } catch (error) {
                                console.error(`Error in handler after auth for ${req.path}:`, error);
                                if (!res.headersSent) {
                                    res.status(500).json({ error: 'Internal server error in handler' });
                                }
                            }
                        });
                    } else {
                        // No authentication required, proceed with handler
                        console.log(`Route ${req.path} does not require auth, proceeding directly`);
                        await matchedRoute.handler(req, res);
                    }
                } catch (error) {
                    console.error('Handler error:', error);
                    res.status(500).json({ error: 'Internal server error' });
                }
            };
        },
    };

    return handler;
} 

function handlerFn(handler: (req: Request, res: Response) => Promise<void>) {
    return async (req: Request, res: Response, next: () => void) => {
        try {
            await handler(req, res);
        } catch (error) {
            next();
        }
    };
}