import express from 'express';
import { authRequired } from '../middleware/authRequired.js';
import { Review } from '../db/models/Review.js';
import { Tool } from '../db/models/Tool.js';
import { User } from '@/db/models/User.js';

const router = express.Router();

// Get all reviews (paginated)
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    
    const status = req.query.status || 'approved'; // Default to approved reviews
    const toolId = req.query.toolId;
    
    const query: any = {};
    
    if (status === 'all' && req.query.isAdmin === 'true') {
      // Admin can see all reviews
    } else if (status !== 'all') {
      query.status = status;
    }
    
    if (toolId) {
      query.toolId = toolId;
    }
    
    const reviews = await Review.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    const total = await Review.countDocuments(query);
    
    res.json({
      reviews,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    res.status(500).json({ error: 'Failed to fetch reviews' });
  }
});

// Get review by ID
router.get('/:id', async (req, res) => {
  try {
    const review = await Review.findById(req.params.id);
    
    if (!review) {
      return res.status(404).json({ error: 'Review not found' });
    }
    
    res.json(review);
  } catch (error) {
    console.error('Error fetching review:', error);
    res.status(500).json({ error: 'Failed to fetch review' });
  }
});

// Create a new review
router.post('/', (req, res, next) => {
  authRequired(req, res, next);
}, async (req, res) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ error: 'Not authenticated' });
    }
    
    const { toolId, rating, comment } = req.body;

    // Fetch user info for name/avatar (optional)
    let userName = '';
    let userAvatar = '';
    try {
      const dbUser = await User.findById(req.user._id).select('firstName lastName username profileImageUrl');
      if (dbUser) {
        userName = dbUser.username || `${dbUser.firstName || ''} ${dbUser.lastName || ''}`.trim();
        userAvatar = dbUser.profileImageUrl || '';
      }
    } catch (_) {
      /* Ignore lookup errors – name will stay empty */
    }
    
    if (!toolId || !rating) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    const review = new Review({
      userId: req.user._id,
      toolId,
      rating,
      comment,
      userName,
      userAvatar,
      status: 'pending',
      createdAt: new Date()
    });
    
    await review.save();
    
    res.status(201).json(review);
  } catch (error) {
    console.error('Error creating review:', error);
    res.status(500).json({ error: 'Failed to create review' });
  }
});

// Update a review
router.put('/:id', (req, res, next) => {
  authRequired(req, res, next);
}, async (req, res) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ error: 'Not authenticated' });
    }
    
    const review = await Review.findById(req.params.id);
    
    if (!review) {
      return res.status(404).json({ error: 'Review not found' });
    }
    
    // Check if user owns the review
    if (review.userId.toString() !== req.user._id) {
      return res.status(403).json({ error: 'Not authorized to update this review' });
    }
    
    const { rating, comment } = req.body;
    
    if (rating) review.rating = rating;
    if (comment) review.comment = comment;
    review.updatedAt = new Date();
    
    await review.save();
    
    res.json(review);
  } catch (error) {
    console.error('Error updating review:', error);
    res.status(500).json({ error: 'Failed to update review' });
  }
});

// Delete a review
router.delete('/:id', (req, res, next) => {
  authRequired(req, res, next);
}, async (req, res) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ error: 'Not authenticated' });
    }
    
    const review = await Review.findById(req.params.id);
    
    if (!review) {
      return res.status(404).json({ error: 'Review not found' });
    }
    
    // Check if user owns the review or is admin
    if (review.userId.toString() !== req.user._id && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Not authorized to delete this review' });
    }
    
    await Review.deleteOne({ _id: req.params.id });
    
    res.json({ message: 'Review deleted successfully' });
  } catch (error) {
    console.error('Error deleting review:', error);
    res.status(500).json({ error: 'Failed to delete review' });
  }
});

// Moderate a review (admin only)
router.put('/:id/moderate', (req, res, next) => {
  authRequired(req, res, next);
}, async (req, res) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({ error: 'Not authenticated' });
    }
    
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Not authorized to moderate reviews' });
    }
    
    const { status, adminResponse } = req.body;
    const review = await Review.findById(req.params.id);
    
    if (!review) {
      return res.status(404).json({ error: 'Review not found' });
    }
    
    const prevStatus = review.status; // Save previous status
    
    // Update moderation fields
    if (status) review.status = status;
    if (adminResponse) review.adminResponse = adminResponse;
    review.updatedAt = new Date();
    
    await review.save();
    
    // Update tool's rating if review status changed to/from approved
    if (status === 'approved' || prevStatus === 'approved') {
      await updateToolRating(review.toolId);
    }
    
    res.json(review);
  } catch (error) {
    console.error('Error moderating review:', error);
    res.status(500).json({ error: 'Failed to moderate review' });
  }
});

// Helper function to update tool rating
async function updateToolRating(toolId) {
  try {
    const approvedReviews = await Review.find({ 
      toolId, 
      status: 'approved' 
    });
    
    if (approvedReviews.length > 0) {
      const totalRating = approvedReviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = totalRating / approvedReviews.length;
      
      await Tool.findByIdAndUpdate(toolId, { 
        rating: averageRating,
        reviews: approvedReviews.length
      });
    }
  } catch (error) {
    console.error('Error updating tool rating:', error);
  }
}

// Special endpoint to force update a tool's review count and rating
router.get('/refresh-tool-ratings/:toolId', async (req, res) => {
  try {
    const toolId = req.params.toolId;
    
    // Validate toolId
    if (!toolId) {
      return res.status(400).json({ error: 'Tool ID is required' });
    }
    
    await updateToolRating(toolId);
    
    // Get the updated tool
    const tool = await Tool.findById(toolId);
    
    if (!tool) {
      return res.status(404).json({ error: 'Tool not found' });
    }
    
    res.json({ 
      message: 'Tool ratings refreshed successfully',
      toolId: tool._id,
      rating: tool.rating,
      reviews: tool.reviews 
    });
  } catch (error) {
    console.error('Error refreshing tool ratings:', error);
    res.status(500).json({ error: 'Failed to refresh tool ratings' });
  }
});

export default router;