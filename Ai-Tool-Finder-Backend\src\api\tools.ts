import { create<PERSON><PERSON><PERSON> } from "./handler.js";
import { z } from "zod";
import { Tool } from '../db/models/Tool.js';
import { connectDB } from '../db/connection.js';
import { User } from '../db/models/User.js';
import mongoose from 'mongoose';
import { Request, Response } from 'express';
import { AuthRequest } from '../middleware/authRequired.js';

const toolSchema = z.object({
    name: z.string().min(2, "Name must be at least 2 characters"),
    description: z.string().min(10, "Description must be at least 10 characters"),
    websiteUrl: z.string().url("Please enter a valid URL"),
    category: z.string().min(1, "Please select a category"),
    tags: z.array(z.string()).min(1, "Add at least one tag"),
    pricing: z.object({
        type: z.enum(["free", "freemium", "paid", "enterprise"], {
            required_error: "Please select a pricing type",
        }),
        startingPrice: z.union([
            z.string().transform((val) => {
                const parsed = parseFloat(val);
                return isNaN(parsed) ? undefined : parsed;
            }),
            z.number(),
            z.undefined()
        ]),
    }),
    features: z.array(z.string()).min(1, "Add at least one feature"),
    logo: z.string().optional(),
    status: z.enum(["draft", "published", "archived", "pending", "approved", "rejected"]).default("draft"),
    isTrending: z.boolean().optional(),
    isNew: z.boolean().optional(),
    isUpcoming: z.boolean().optional(),
    isTopRated: z.boolean().optional(),
    views: z.number().default(0),
    votes: z.number().default(0),
    rating: z.number().default(0),
    reviews: z.number().default(0),
    slug: z.string().optional(),
});

const handler = createHandler();

// Toggle save endpoint
handler.post('/:id/save', async (req: AuthRequest, res: Response) => {
    try {
        await connectDB();
        
        console.log('Save handler: Processing save request for tool ID:', req.params.id);
        console.log('Save handler: Request user object:', req.user);
        
        if (!req.user) {
            console.log('Save handler: Authentication required but no user found in request');
            return res.status(401).json({ error: 'Authentication required' });
        }
        
        console.log(`Save handler: Authenticated user ID: ${req.user._id}`);
        
        const { id } = req.params;
        
        // Allow slug or ID
        console.log(`Save handler: Looking for tool by slug '${id}'`);
        let tool = await Tool.findOne({ slug: id });
        console.log('Save handler: Result slug lookup:', tool ? 'found' : 'not found');
        
        if (!tool && mongoose.Types.ObjectId.isValid(id)) {
            console.log(`Save handler: Looking for tool by ID ${id}`);
            tool = await Tool.findById(id);
            console.log('Save handler: Result ID lookup:', tool ? 'found' : 'not found');
        }
        
        if (!tool) {
            console.log(`Save handler: Tool not found with slug or ID ${id}`);
            return res.status(404).json({ error: 'Tool not found' });
        }
        
        console.log(`Save handler: Tool found: ${tool.name} (${tool._id})`);
        
        console.log(`Save handler: Looking up user with ID ${req.user._id}`);
        const userDoc = await User.findById(req.user._id);
        if (!userDoc) {
            console.log(`Save handler: User not found with ID ${req.user._id}`);
            return res.status(404).json({ error: 'User not found' });
        }
        
        console.log(`Save handler: User found: ${userDoc.email}`);

        const idStr = tool._id.toString();
        const alreadySaved = userDoc.savedTools.includes(idStr);
        console.log(`Save handler: Tool is already saved: ${alreadySaved}`);
        
        if (alreadySaved) {
            console.log(`Save handler: Removing tool ${idStr} from user's saved tools`);
            userDoc.savedTools = userDoc.savedTools.filter(tid => tid !== idStr);
        } else {
            console.log(`Save handler: Adding tool ${idStr} to user's saved tools`);
            userDoc.savedTools.push(idStr);
        }
        
        console.log('Save handler: Saving changes to user document');
        await userDoc.save();
        console.log('Save handler: Changes saved successfully');
        
        return res.json({ 
            success: true, 
            saved: !alreadySaved,
            savedTools: userDoc.savedTools
        });
    } catch (error) {
        console.error('Error toggling save:', error);
        return res.status(500).json({ error: 'Failed to toggle save' });
    }
}, true);

// Add a function to generate slug from name
const generateSlug = (name: string): string => {
    return name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-') // Replace any non-alphanumeric character with a dash
        .replace(/^-+|-+$/g, '') // Remove leading and trailing dashes
        .replace(/-+/g, '-'); // Replace multiple consecutive dashes with a single dash
};

// Get dashboard statistics
handler.get('/stats', async (req: Request, res: Response) => {
    try {
        console.log('GET /api/tools/stats - Fetching dashboard statistics');
        await connectDB();

        const stats = await Promise.all([
            // Total tools count
            Tool.countDocuments({ status: 'published' }),
            // Pending submissions count
            Tool.countDocuments({ status: 'pending' }),
            // Tools by category
            Tool.aggregate([
                { $match: { status: 'published' } },
                { $group: { _id: '$category', count: { $sum: 1 } } },
                { $sort: { count: -1 } }
            ]),
            // Recent tools
            Tool.find({ status: 'published' })
                .sort({ createdAt: -1 })
                .limit(5)
                .select('name category createdAt'),
            // Popular tools (by views)
            Tool.find({ status: 'published' })
                .sort({ views: -1 })
                .limit(5)
                .select('name views'),
            // Tools by status
            Tool.aggregate([
                { $group: { _id: '$status', count: { $sum: 1 } } },
                { $sort: { count: -1 } }
            ]),
            // Tools by pricing type
            Tool.aggregate([
                { $match: { status: 'published' } },
                { $group: { _id: '$pricing.type', count: { $sum: 1 } } },
                { $sort: { count: -1 } }
            ])
        ]);

        return res.json({
            totalTools: stats[0],
            pendingSubmissions: stats[1],
            categoryCounts: stats[2],
            recentTools: stats[3],
            popularTools: stats[4],
            statusCounts: stats[5],
            pricingCounts: stats[6]
        });
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        return res.status(500).json({ error: 'Failed to fetch dashboard statistics' });
    }
});

// Get all tool submissions (pending tools)
handler.get('/submissions', async (req: Request, res: Response) => {
    try {
        console.log('GET /api/tools/submissions - Fetching tool submissions');
        await connectDB();

        const submissions = await Tool.find({ status: 'pending' }).sort({ createdAt: -1 });
        console.log('GET /api/tools/submissions - Found', submissions.length, 'submissions');

        // Format the submissions with properly formatted dates
        const formattedSubmissions = submissions.map(submission => ({
            ...submission.toObject(),
            _id: submission._id.toString(),
            id: submission._id.toString(),
            createdAt: submission.createdAt,
            updatedAt: submission.updatedAt,
            submittedDate: submission.createdAt ? new Date(submission.createdAt).toISOString() : null
        }));

        return res.json(formattedSubmissions);
    } catch (error) {
        console.error('Error fetching tool submissions:', error);
        return res.status(500).json({ error: 'Failed to fetch tool submissions' });
    }
});

// Get all tools
handler.get('/', async (req: Request, res: Response) => {
    try {
        console.log('GET /api/tools - Fetching tools');
        await connectDB();

        const tools = await Tool.find({ status: 'published' }).sort({ createdAt: -1 });
        console.log('GET /api/tools - Found', tools.length, 'tools');
        if (tools.length > 0) {
            console.log('Sample tool:', JSON.stringify(tools[0], null, 2));
        }

        // Transform the data to match the expected format
        const formattedTools = tools.map(tool => ({
            _id: tool._id.toString(),
            id: tool._id.toString(), // Keep id for backward compatibility
            name: tool.name,
            slug: tool.slug,
            description: tool.description,
            websiteUrl: tool.websiteUrl,
            url: tool.websiteUrl, // Add url field for frontend compatibility
            website: new URL(tool.websiteUrl).hostname, // Add website field for frontend
            category: tool.category,
            tags: tool.tags,
            pricing: {
                type: tool.pricing?.type || 'free',
                startingPrice: tool.pricing?.startingPrice || undefined,
            },
            features: tool.features,
            status: tool.status,
            isTrending: tool.isTrending || false,
            isNew: tool.isNewTool || false,
            isUpcoming: tool.isUpcoming || false,
            isTopRated: tool.isTopRated || false,
            views: tool.views || 0,
            votes: tool.votes || 0,
            rating: tool.rating || 0,
            reviews: tool.reviews || 0,
            createdAt: tool.createdAt,
            updatedAt: tool.updatedAt,
            logo: tool.logo // Include the logo field
        }));

        console.log('Formatted sample:', JSON.stringify(formattedTools[0], null, 2));
        return res.json(formattedTools);
    } catch (error) {
        console.error('Error fetching tools:', error);
        return res.status(500).json({ error: 'Failed to fetch tools' });
    }
});

// Get single tool by ID or slug
handler.get('/:idOrSlug', async (req, res) => {
    try {
        console.log('GET /api/tools/:idOrSlug - Fetching tool:', req.params.idOrSlug);
        await connectDB();

        // Try to find by slug first
        let tool = await Tool.findOne({ slug: req.params.idOrSlug });

        // If not found by slug, try to find by ID
        if (!tool && mongoose.Types.ObjectId.isValid(req.params.idOrSlug)) {
            tool = await Tool.findById(req.params.idOrSlug);
        }

        if (!tool) {
            return res.status(404).json({ error: 'Tool not found' });
        }

        // Increment views
        tool.views += 1;
        await tool.save();

        // Format the response
        const formattedTool = {
            _id: tool._id.toString(),
            id: tool._id.toString(),
            name: tool.name,
            slug: tool.slug,
            description: tool.description,
            websiteUrl: tool.websiteUrl,
            url: tool.websiteUrl,
            website: new URL(tool.websiteUrl).hostname,
            category: tool.category,
            tags: tool.tags,
            pricing: {
                type: tool.pricing?.type || 'free',
                startingPrice: tool.pricing?.startingPrice || undefined,
            },
            features: tool.features,
            status: tool.status,
            isTrending: tool.isTrending || false,
            isNew: tool.isNewTool || false,
            isUpcoming: tool.isUpcoming || false,
            isTopRated: tool.isTopRated || false,
            views: tool.views || 0,
            votes: tool.votes || 0,
            rating: tool.rating || 0,
            reviews: tool.reviews || 0,
            createdAt: tool.createdAt,
            updatedAt: tool.updatedAt,
            logo: tool.logo
        };

        return res.json(formattedTool);
    } catch (error) {
        console.error('Error fetching tool:', error);
        return res.status(500).json({ error: 'Failed to fetch tool' });
    }
});

// Create new tool
handler.post('/', async (req, res) => {
    try {
        await connectDB();
        const toolData = toolSchema.parse(req.body);

        // Generate slug from name if not provided
        const slug = toolData.slug || generateSlug(toolData.name);

        // Check if slug already exists
        const existingTool = await Tool.findOne({ slug });
        if (existingTool) {
            // If slug exists, append a random number
            const randomSuffix = Math.floor(Math.random() * 1000);
            toolData.slug = `${slug}-${randomSuffix}`;
        } else {
            toolData.slug = slug;
        }

        // Map isNew field from frontend to isNewTool in database
        const dbToolData = {
            ...toolData,
            isNewTool: toolData.isNew,
            status: 'pending'
        };

        const newTool = new Tool(dbToolData);

        await newTool.save();
        return res.status(201).json(newTool);
    } catch (error) {
        console.error('Error creating tool:', error);
        if (error instanceof z.ZodError) {
            return res.status(400).json({ error: error.errors });
        }
        return res.status(500).json({ error: 'Failed to create tool' });
    }
});

// Update tool
handler.patch('/:id', async (req, res) => {
    try {
        await connectDB();
        const { id } = req.params;
        const updateData = toolSchema.partial().parse(req.body);
        
        // Map frontend fields to database fields
        const dbUpdateData = {
            ...updateData,
            isNewTool: updateData.isNew,
            updatedAt: new Date()
        };

        const updatedTool = await Tool.findByIdAndUpdate(
            id,
            dbUpdateData,
            { new: true, runValidators: true }
        );

        if (!updatedTool) {
            return res.status(404).json({ error: 'Tool not found' });
        }

        // Format the response
        const formattedTool = {
            ...updatedTool.toObject(),
            _id: updatedTool._id.toString(),
            id: updatedTool._id.toString(),
            isNew: updatedTool.isNewTool
        };

        return res.json(formattedTool);
    } catch (error) {
        console.error('Error updating tool:', error);
        if (error instanceof z.ZodError) {
            return res.status(400).json({ error: error.errors });
        }
        return res.status(500).json({ error: 'Failed to update tool' });
    }
});

// Delete tool
handler.delete('/:id', async (req, res) => {
    try {
        await connectDB();
        const { id } = req.params;

        const deletedTool = await Tool.findByIdAndDelete(id);
        if (!deletedTool) {
            return res.status(404).json({ error: 'Tool not found' });
        }

        return res.json({ success: true });
    } catch (error) {
        console.error('Error deleting tool:', error);
        return res.status(500).json({ error: 'Failed to delete tool' });
    }
});

// Update tool status
handler.patch('/:id/status', async (req, res) => {
    try {
        await connectDB();
        const { id } = req.params;
        const { status } = z.object({
            status: z.enum(["draft", "published", "archived", "pending", "approved", "rejected"])
        }).parse(req.body);

        // If status is 'approved', we'll actually set it to 'published' to make it appear in the main listing
        const finalStatus = status === 'approved' ? 'published' : status;

        const updatedTool = await Tool.findByIdAndUpdate(
            id,
            { status: finalStatus, updatedAt: new Date() },
            { new: true, runValidators: true }
        );

        if (!updatedTool) {
            return res.status(404).json({ error: 'Tool not found' });
        }

        return res.json(updatedTool);
    } catch (error) {
        console.error('Error updating tool status:', error);
        if (error instanceof z.ZodError) {
            return res.status(400).json({ error: error.errors });
        }
        return res.status(500).json({ error: 'Failed to update tool status' });
    }
});

// Update tool votes (POST method)
handler.post('/:id/vote', async (req: AuthRequest, res: Response) => {
    try {
        await connectDB();
        const { id } = req.params;
        const { action } = z.object({
            action: z.enum(["upvote", "downvote"])
        }).parse(req.body);

        console.log(`Vote handler: Processing ${action} for tool ${id}`);
        console.log('Vote handler: Request user object:', req.user);

        // Authentication check
        if (!req.user) {
            console.log('Vote handler: Authentication required but no user found in request');
            return res.status(401).json({ error: 'Authentication required' });
        }

        console.log(`Vote handler: Authenticated user ID: ${req.user._id}`);

        // Try to find by slug first
        console.log(`Vote handler: looking for tool by slug '${id}'`);
        let tool = await Tool.findOne({ slug: id });
        console.log('Vote handler: Result slug lookup:', tool ? 'found' : 'not found');

        // If not found by slug, try to find by ID
        if (!tool && mongoose.Types.ObjectId.isValid(id)) {
            console.log(`Vote handler: Looking for tool by ID ${id}`);
            tool = await Tool.findById(id);
            console.log('Vote handler: Result ID lookup:', tool ? 'found' : 'not found');
        }

        if (!tool) {
            console.log(`Vote handler: Tool not found with slug or ID ${id}`);
            return res.status(404).json({ error: 'Tool not found' });
        }

        console.log(`Vote handler: Tool found: ${tool.name} (${tool._id})`);

        // Get the user to update their upvoted tools
        console.log(`Vote handler: Looking up user with ID ${req.user._id}`);
        const user = await User.findById(req.user._id);
        if (!user) {
            console.log(`Vote handler: User not found with ID ${req.user._id}`);
            return res.status(404).json({ error: 'User not found' });
        }

        console.log(`Vote handler: User found: ${user.email}`);

        const toolId = tool._id.toString();
        const userHasUpvoted = user.upvotedTools.includes(toolId);
        console.log(`Vote handler: User has previously upvoted this tool: ${userHasUpvoted}`);

        // Handle upvote/downvote action
        if (action === "upvote") {
            if (!userHasUpvoted) {
                // Add to user's upvoted tools if not already there
                user.upvotedTools.push(toolId);
                tool.votes = (tool.votes || 0) + 1;
                console.log(`Vote handler: Adding upvote, new vote count: ${tool.votes}`);
            } else {
                console.log('Vote handler: Tool already upvoted by user, no change');
            }
        } else if (action === "downvote") {
            if (userHasUpvoted) {
                // Remove from user's upvoted tools
                user.upvotedTools = user.upvotedTools.filter(id => id !== toolId);
                tool.votes = Math.max(0, (tool.votes || 0) - 1); // Ensure votes don't go below 0
                console.log(`Vote handler: Removing upvote, new vote count: ${tool.votes}`);
            } else {
                console.log('Vote handler: Tool not previously upvoted by user, no change');
            }
        }

        // Save changes
        console.log('Vote handler: Saving changes to user and tool');
        await Promise.all([user.save(), tool.save()]);
        console.log('Vote handler: Changes saved successfully');

        return res.json({ 
            success: true, 
            votes: tool.votes,
            isUpvoted: user.upvotedTools.includes(toolId)
        });
    } catch (error) {
        console.error('Error updating tool votes (POST):', error);
        if (error instanceof z.ZodError) {
            return res.status(400).json({ error: error.errors });
        }
        return res.status(500).json({ error: 'Failed to update votes' });
    }
}, true);

// Update tool votes (GET method - for maximum compatibility with restrictive environments)
handler.get('/:id/vote', async (req, res) => {
  try {
    await connectDB();
    const { id } = req.params;
    const { action } = z.object({
      action: z.enum(["upvote", "downvote"])
    }).parse(req.query);  // Note: using query parameters instead of body

    if (!mongoose.Types.ObjectId.isValid(id)) {
        console.error(`GET /vote: Invalid ObjectId format for ID: ${id}`);
        return res.status(400).json({ error: 'Invalid ID format for voting' });
    }

    // Find the tool
    const tool = await Tool.findById(id);
    
    if (!tool) {
      return res.status(404).json({ error: 'Tool not found' });
    }

    // Update votes based on action
    if (action === "upvote") {
      tool.votes = (tool.votes || 0) + 1;
    } else if (action === "downvote") {
      tool.votes = Math.max(0, (tool.votes || 0) - 1); // Ensure votes don't go below 0
    }

    await tool.save();

    return res.json({ 
      success: true, 
      votes: tool.votes 
    });
  } catch (error) {
    console.error('Error updating tool votes via GET:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    return res.status(500).json({ error: 'Failed to update votes' });
  }
}, true);

export const toolsHandler = handler; 