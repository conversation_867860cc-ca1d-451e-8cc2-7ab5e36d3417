import { createHandler } from "../server/api/handler.js";

const ADMIN_EMAIL_DOMAINS = ["webbuddy.agency"];

const handler = createHandler();

// Handle user creation webhook - Adjust for JWT authentication
handler.post('/clerk', async (req, res) => {
  
  try {
    // Adjust logic for JWT authentication if necessary
    // For now, we'll just return a success response
    return res.json({ success: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return res.status(500).json({ error: 'Webhook handler failed' });
  }
});

export const webhooksHandler = handler;