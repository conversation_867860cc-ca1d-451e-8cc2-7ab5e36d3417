import mongoose from 'mongoose';

const userSchema = new mongoose.Schema({
  // Clerk ID kept for backward-compat but no longer required
  clerkId: { type: String, unique: true, sparse: true },
  // Local auth fields
  email: { type: String, required: true, unique: true },
  passwordHash: { type: String, required: true },
  role: { type: String, enum: ['basic', 'unlimited', 'agency', 'admin'], default: 'basic' },
  firstName: { type: String, default: '' },
  lastName: { type: String, default: '' },

  username: { type: String, default: '' },
  profileImageUrl: { type: String, default: '' },
  publicMetadata: { type: mongoose.Schema.Types.Mixed, default: {} },
  // Fields to track tool interactions
  upvotedTools: [{ type: String }],
  savedTools: [{ type: String }],
  submittedTools: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Tool' }],
  // Reference to the agency user who created this user (if applicable)
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', default: null },
  // User status
  status: { type: String, enum: ['active', 'suspended', 'banned'], default: 'active' },
  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Add indexes for better query performance
userSchema.index({ clerkId: 1 });
userSchema.index({ email: 1 });  // already unique
userSchema.index({ role: 1 });
userSchema.index({ username: 1 });
userSchema.index({ createdBy: 1 });

export const User = mongoose.model('User', userSchema); 