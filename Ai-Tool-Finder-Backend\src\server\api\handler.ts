import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';

type AuthRequest = Request & { user?: { _id: string; email: string; role: string; name?: string } };

type Handler = {
    get: (path: string, handler: (req: AuthRequest, res: Response) => Promise<any>, isPublic?: boolean) => void;
    post: (path: string, handler: (req: AuthRequest, res: Response) => Promise<any>, isPublic?: boolean) => void;
    patch: (path: string, handler: (req: AuthRequest, res: Response) => Promise<any>, isPublic?: boolean) => void;
    delete: (path: string, handler: (req: AuthRequest, res: Response) => Promise<any>, isPublic?: boolean) => void;
    middleware: () => any;
};

const routes = new Map<string, Map<string, (req: AuthRequest, res: Response) => Promise<void>>>();
const publicRoutes = new Set<string>();

function createHandler(): Handler {
    const handler: Handler = {
        get(path, routeHandler, isPublic = false) {
            if (!routes.has('GET')) routes.set('GET', new Map());
            routes.get('GET')!.set(path, routeHandler);
            if (isPublic) publicRoutes.add(`GET ${path}`);
        },
        post(path, routeHandler, isPublic = false) {
            if (!routes.has('POST')) routes.set('POST', new Map());
            routes.get('POST')!.set(path, routeHandler);
            if (isPublic) publicRoutes.add(`POST ${path}`);
        },
        patch(path, routeHandler, isPublic = false) {
            if (!routes.has('PATCH')) routes.set('PATCH', new Map());
            routes.get('PATCH')!.set(path, routeHandler);
            if (isPublic) publicRoutes.add(`PATCH ${path}`);
        },
        delete(path, routeHandler, isPublic = false) {
            if (!routes.has('DELETE')) routes.set('DELETE', new Map());
            routes.get('DELETE')!.set(path, routeHandler);
            if (isPublic) publicRoutes.add(`DELETE ${path}`);
        },
        middleware() {
            return async (req: Request, res: Response) => {
                try {
                    const method = req.method;
                    const methodRoutes = routes.get(method);

                    if (!methodRoutes) {
                        return res.status(405).json({ error: 'Method not allowed' });
                    }

                    // Extract the path without the API prefix
                    // This fixes the issue with route matching
                    const fullPath = req.path;
                    const pathWithoutPrefix = req.path.replace(/^\/api\/[^\/]+/, '');
                    
                    console.log('Processing request:', { method, path: fullPath, pathWithoutPrefix });

                    let matchedHandler: ((req: AuthRequest, res: Response) => Promise<void>) | undefined;
                    let matchedParams: { [key: string]: string } = {};

                    for (const [path, handler] of methodRoutes.entries()) {
                        const pathSegments = path.split('/').filter(Boolean);
                        const urlSegments = pathWithoutPrefix.split('/').filter(Boolean);

                        if (pathSegments.length !== urlSegments.length) continue;

                        const params: { [key: string]: string } = {};
                        let isMatch = true;

                        for (let i = 0; i < pathSegments.length; i++) {
                            if (pathSegments[i].startsWith(':')) {
                                params[pathSegments[i].slice(1)] = urlSegments[i];
                            } else if (pathSegments[i] !== urlSegments[i]) {
                                isMatch = false;
                                break;
                            }
                        }

                        if (isMatch) {
                            matchedHandler = handler;
                            matchedParams = params;
                            break;
                        }
                    }

                    if (!matchedHandler) {
                        return res.status(404).json({ error: 'Route not found' });
                    }

                    req.params = { ...req.params, ...matchedParams };

                    // Check if the route is public using the path without the API prefix
                    const isPublicRoute = publicRoutes.has(`${method} ${pathWithoutPrefix}`);
                    console.log('Is public route:', isPublicRoute, 'for', `${method} ${pathWithoutPrefix}`);

                    res.header('Access-Control-Allow-Origin', '*');
                    res.header('Access-Control-Allow-Methods', 'GET, POST, PATCH, DELETE, OPTIONS');
                    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
                    res.header('Access-Control-Max-Age', '86400');

                    if (req.method === 'OPTIONS') {
                        return res.status(204).send();
                    }

                    // Verify JWT for all non-public routes regardless of HTTP method
                    if (!isPublicRoute) {
                        const authHeader = req.headers.authorization;
                        if (!authHeader || !authHeader.startsWith('Bearer ')) {
                            return res.status(401).json({ error: 'Missing token' });
                        }

                        const token = authHeader.split(' ')[1];
                        try {
                            const JWT_SECRET = process.env.JWT_SECRET || 'change_this_secret';
                            const payload = jwt.verify(token, JWT_SECRET) as { id: string; email: string; role: string; name?: string };
                            (req as AuthRequest).user = { _id: payload.id, email: payload.email, role: payload.role, name: payload.name };
                        } catch (error) {
                            console.error('Auth error:', error);
                            return res.status(401).json({ error: 'Unauthorized' });
                        }
                    }

                    await matchedHandler(req as AuthRequest, res);
                } catch (error) {
                    console.error('Handler error:', error);
                    res.status(500).json({ error: 'Internal server error' });
                }
            };
        },
    };

    return handler;
}

export { createHandler };