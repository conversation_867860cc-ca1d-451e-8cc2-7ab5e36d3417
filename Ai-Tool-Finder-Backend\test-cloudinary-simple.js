import { v2 as cloudinary } from 'cloudinary';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure Cloudinary
console.log('Configuring Cloudinary with:', {
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'MISSING',
  api_key: process.env.CLOUDINARY_API_KEY ? 'PRESENT' : 'MISSING',
  api_secret: process.env.CLOUDINARY_API_SECRET ? 'PRESENT' : 'MISSING'
});

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

// Test connection
async function main() {
  try {
    // Test the Cloudinary connection
    const pingResult = await cloudinary.api.ping();
    console.log('Cloudinary ping successful:', pingResult);
    
    // Create a simple SVG file to test with
    const svgContent = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
  <rect width="100" height="100" fill="#ff0000" />
  <circle cx="50" cy="50" r="30" fill="#ffffff" />
</svg>`;
    
    const svgPath = path.join(__dirname, 'test-simple.svg');
    fs.writeFileSync(svgPath, svgContent);
    console.log(`Created test SVG file at: ${svgPath}`);
    
    // Upload the SVG file
    console.log('Attempting to upload SVG...');
    const svgUploadResult = await cloudinary.uploader.upload(svgPath, {
      folder: 'ai-tool-finder/site/logos',
      resource_type: 'auto',
    });
    console.log('SVG upload successful:', svgUploadResult);
    
    // Create a simple PNG file using node-canvas or another library
    // For this simple test, we'll use a very basic PNG file
    const pngPath = path.join(__dirname, 'test-rect.png');
    if (!fs.existsSync(pngPath)) {
      console.error(`PNG file not found at ${pngPath}. Please create a PNG file manually for testing.`);
    } else {
      // Upload the PNG file
      console.log('Attempting to upload PNG...');
      const pngUploadResult = await cloudinary.uploader.upload(pngPath, {
        folder: 'ai-tool-finder/site/logos',
        resource_type: 'auto',
      });
      console.log('PNG upload successful:', pngUploadResult);
    }
    
    console.log('All tests completed successfully!');
  } catch (error) {
    console.error('Error during Cloudinary test:', error);
  }
}

main(); 