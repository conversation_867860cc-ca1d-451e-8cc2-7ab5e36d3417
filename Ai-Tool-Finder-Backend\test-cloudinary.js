import { v2 as cloudinary } from 'cloudinary';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure Cloudinary
console.log('Configuring Cloudinary with:', {
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'MISSING',
  api_key: process.env.CLOUDINARY_API_KEY ? 'PRESENT' : 'MISSING',
  api_secret: process.env.CLOUDINARY_API_SECRET ? 'PRESENT' : 'MISSING'
});

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

// Test basic connectivity
async function testConnection() {
  try {
    const result = await cloudinary.api.ping();
    console.log('✅ Cloudinary connection successful:', result);
    return true;
  } catch (error) {
    console.error('❌ Cloudinary connection failed:', error);
    return false;
  }
}

// Test uploading a file
async function testUpload(filePath, options) {
  try {
    if (!fs.existsSync(filePath)) {
      console.error(`❌ File not found: ${filePath}`);
      return null;
    }

    console.log(`Uploading file: ${filePath}`);
    console.log('Upload options:', options);

    const result = await cloudinary.uploader.upload(filePath, options);
    console.log('✅ Upload successful:', result);
    return result;
  } catch (error) {
    console.error('❌ Upload failed:', error);
    return null;
  }
}

// Main function
async function main() {
  // Test connection
  const connected = await testConnection();
  if (!connected) {
    console.error('Exiting due to connection failure');
    return;
  }

  // Create test files for logo and favicon if they don't exist
  const testLogoPath = path.join(__dirname, 'test-logo.png');
  const testFaviconPath = path.join(__dirname, 'test-favicon.png');
  
  // Test logo upload
  console.log('\n===== Testing Logo Upload =====');
  const logoOptions = {
    folder: 'ai-tool-finder/site/logos',
    resource_type: 'auto',
    transformation: [{ width: 500, crop: 'limit' }]
  };
  const logoResult = await testUpload(testLogoPath, logoOptions);

  // Test favicon upload
  console.log('\n===== Testing Favicon Upload =====');
  const faviconOptions = {
    folder: 'ai-tool-finder/site/logos',
    resource_type: 'auto',
    transformation: [{ width: 32, height: 32, crop: 'limit' }]
  };
  const faviconResult = await testUpload(testFaviconPath, faviconOptions);

  // Test SVG upload
  const testSvgPath = path.join(__dirname, 'test-logo.svg');
  if (fs.existsSync(testSvgPath)) {
    console.log('\n===== Testing SVG Upload =====');
    const svgOptions = {
      folder: 'ai-tool-finder/site/logos',
      resource_type: 'auto'
    };
    await testUpload(testSvgPath, svgOptions);
  } else {
    console.log('No test SVG file found, skipping SVG test');
  }
}

main().catch(console.error); 