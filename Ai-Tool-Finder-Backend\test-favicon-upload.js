import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { fileURLToPath } from 'url';
import FormData from 'form-data';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create base 64 PNG
const createTestPNG = async () => {
  const pngData = `iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAGJlWElmTU0A
KgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACe
YbDsAAAArklEQVRYCe2XwQ6AIAiGcdbe/4HbehntBvNPEzww47B9DvEm8nEDIuoi8pxJZjcAAQQQQDBpQP+lh4+G
5t77Y2ZWP/efJgGYBGASgP+NgHOud9Z3FFDT7HrNYDIrbQ6H6IJvl0RRqRCvi6sKZF4h0CEuKzAkLikwJi4pMCYu
K3BFXFLgiriswJC4pMCQuKTA3LjxbzoSWnYw/NU7lv7XDAQQQAABBBBAAIFRA68XLAz/TN6/cAAAAABJRU5ErkJg
gg==`;
  
  // Save as PNG file
  const testPngPath = path.join(__dirname, 'test-favicon.png');
  fs.writeFileSync(testPngPath, Buffer.from(pngData, 'base64'));
  
  console.log(`Created test PNG file at ${testPngPath}`);
  return testPngPath;
};

// Test the API endpoint
const testEndpoint = async (pngFilePath) => {
  try {
    // The API URL
    const apiBaseUrl = process.env.BASE_URL || 'http://localhost:3005';
    const endpoint = `${apiBaseUrl}/api/config/test-upload-favicon`;
    
    console.log(`Testing endpoint: ${endpoint}`);
    console.log(`Using file: ${pngFilePath}`);
    
    // Check that file exists
    if (!fs.existsSync(pngFilePath)) {
      throw new Error(`File not found: ${pngFilePath}`);
    }
    
    // Create form data
    const formData = new FormData();
    formData.append('favicon', fs.createReadStream(pngFilePath));
    
    // Make the request
    const response = await axios.post(endpoint, formData, {
      headers: {
        ...formData.getHeaders(),
      }
    });
    
    console.log('API Response:', response.data);
    
    if (response.data.success) {
      console.log(`✅ Success! Favicon URL: ${response.data.faviconUrl}`);
      console.log(`Storage type: ${response.data.storage}`);
    } else {
      console.error('❌ Upload failed.');
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ Error during test:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    throw error;
  }
};

// Main function
async function main() {
  try {
    // Create test files
    const pngFilePath = await createTestPNG();
    
    // Test the endpoint
    console.log('\n===== Testing Favicon Upload =====');
    await testEndpoint(pngFilePath);
    
    console.log('\n✅ Test completed successfully');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  }
}

main().catch(console.error); 