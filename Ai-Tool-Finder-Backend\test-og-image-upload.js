import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { fileURLToPath } from 'url';
import FormData from 'form-data';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create a simple test OG image (1200x630 PNG)
const createTestOGImage = async () => {
  // Create a very simple PNG file
  const pngData = `iVBORw0KGgoAAAANSUhEUgAABLAAAAJ2CAYAAABXKFRaAAAACXBIWXMAAC4jAAAuIwF4pT92AAAA
GXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA2FJREFUeNrs2mENgEAMRFFUgAPs
YAcETMAE1jCBOFyRCxVd3pugnZbfVYFZ0wAAAAAAAABQarMEAAAAAAAAALUZWAAAAAAAAADlGVgA
AAAAAAAA5RlYAAAAAAAAAOUZWAAAAAAAAADlGVgAAAAAAAAA5RlYAAAAAAAAAOUZWAAAAAAAAADl
GVgAAAAAAAAA5RlYAAAAAAAAAOUZWAAAAAAAAADlGVgAAAAAAAAA5RlYAAAAAAAAAOUZWAAAAAAA
AADlGVgAAAAAAAAA5RlYAAAAAAAAAOUZWAAAAAAAAADlGVgAAAAAAAAA5RlYAAAAAAAAAOUZWAAA
AAAAAADlGVgAAAAAAAAA5RlYAAAAAAAAAOUZWAAAAAAAAADlGVgAAAAAAAAA5RlYAAAAAAAAACx4
AQAAAAAAAACIsTnGUJc1AAAAAAAAgDLcwAIAAAAAAAAoz8ACAAAAAAAAKM/AAAAAAAAAAig/sLrO
uwEAAAAAAADK6PoZAAAAAAAAQJz+HQAA1NXeJwAAAACAWINLCAAAAAAAAADKcwMLAAAAAAAAoDwD
CwAAAAAAAKA8AwsAAAAAAACgPAMLAAAAAAAAoDwDCwAAAAAAAKA8AwsAAAAAAACgPAMLAAAAAAAA
oDwDCwAAAAAAAKA8AwsAAAAAAACgPAMLAAAAAAAAoDwDCwAAAAAAAKA8AwsAAAAAAACgPAMLAAAA
AAAAoDwDCwAAAAAAAKA8AwsAAAAAAACgPAMLAAAAAAAAoDwDCwAAAAAAAKA8AwsAAAAAAACgPAML
AAAAAAAAoDwDCwAAAAAAAKA8AwsAAAAAAACgPAMLAAAAAAAAoDwDCwAAAAAAAKA8AwsAAAAAAAAA
FrwAAAAAAAAAAMQYXEIAAAAAAAAAUJ6BBQAAAAAAAFCegQUAAAAAAABQnoEFAAAAAAAAUJ6BBQAA
AAAAAFCegQUAAAAAAABQnoEFAAAAAAAAUJ6BBQAAAAAAAFCegQUAAAAAAABQnoEFAAAAAAAAUJ6B
BQAAAAAAAFCegQUAAAAAAABQnoEFAAAAAAAAUJ6BBQAAAAAAAFCegQUAAAAAAABQnoEFAAAAAAAA
UJ6BBQAAAAAAAFCegQUAAAAAAABQnoEFAAAAAAAAUJ6BBQAAAAAAAFCegQUAAAAAAABQnoEFAAAA
AAAAUA4AAP//AwALbA09fZQwRgAAAABJRU5ErkJggg==`;
  
  // Save as PNG file
  const testOgPath = path.join(__dirname, 'test-og-image.png');
  fs.writeFileSync(testOgPath, Buffer.from(pngData, 'base64'));
  
  console.log(`Created test OG image at ${testOgPath}`);
  return testOgPath;
};

// Test the API endpoint
const testEndpoint = async (pngFilePath) => {
  try {
    // The API URL
    const apiBaseUrl = process.env.BASE_URL || 'http://localhost:3005';
    const endpoint = `${apiBaseUrl}/api/config/test-upload-og-image`;
    
    console.log(`Testing endpoint: ${endpoint}`);
    console.log(`Using file: ${pngFilePath}`);
    
    // Check that file exists
    if (!fs.existsSync(pngFilePath)) {
      throw new Error(`File not found: ${pngFilePath}`);
    }
    
    // Create form data
    const formData = new FormData();
    formData.append('ogImage', fs.createReadStream(pngFilePath));
    
    // Make the request
    const response = await axios.post(endpoint, formData, {
      headers: {
        ...formData.getHeaders(),
      }
    });
    
    console.log('API Response:', response.data);
    
    if (response.data.success) {
      console.log(`✅ Success! OG Image URL: ${response.data.ogImageUrl}`);
      console.log(`Storage type: ${response.data.storage}`);
    } else {
      console.error('❌ Upload failed.');
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ Error during test:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    throw error;
  }
};

// Main function
async function main() {
  try {
    // Create test file
    const pngFilePath = await createTestOGImage();
    
    // Test the endpoint
    console.log('\n===== Testing OG Image Upload =====');
    await testEndpoint(pngFilePath);
    
    console.log('\n✅ Test completed successfully');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  }
}

main().catch(console.error); 