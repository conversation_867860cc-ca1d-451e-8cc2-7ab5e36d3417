{"compilerOptions": {"target": "ESNext", "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "noImplicitAny": false, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "ts-node": {"esm": true, "experimentalSpecifiers": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}