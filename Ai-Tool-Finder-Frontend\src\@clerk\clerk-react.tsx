import { ReactNode } from 'react';
import { useAuth as useCustomAuth } from '@/contexts/AuthContext';

// Legacy compatibility layer to keep existing Clerk imports working after migrating
// to our custom AuthContext. Provides only the subset of functionality used in the codebase.

/* ------------------------------------------------------------------
 * Hooks
 * ------------------------------------------------------------------*/
export const useAuth = () => {
  const { user, login, signup, signOut, isAuthenticated } = useCustomAuth();
  return {
    isLoaded: true,
    isSignedIn: isAuthenticated,
    userId: user?.id,
    sessionId: null,
    signOut,
    // expose the custom methods for convenience
    login,
    signup,
  };
};

export const useUser = () => {
  const { user, isAuthenticated } = useCustomAuth();
  return {
    isLoaded: true,
    isSignedIn: isAuthenticated,
    user,
  };
};

export const useClerk = () => {
  const { signOut } = useCustomAuth();
  return {
    signOut,
  };
};

/* ------------------------------------------------------------------
 * Dummy UI Components
 * ------------------------------------------------------------------*/
// Some legacy components may still import <SignIn> / <SignUp> from Clerk.
// We render nothing but allow the JSX to compile, or redirect to our pages.
export const SignIn = ({ redirectUrl }: { redirectUrl?: string }) => {
  if (typeof window !== 'undefined') {
    window.location.href = redirectUrl || '/login';
  }
  return null;
};

export const SignUp = ({ redirectUrl }: { redirectUrl?: string }) => {
  if (typeof window !== 'undefined') {
    window.location.href = redirectUrl || '/signup';
  }
  return null;
};

/* ------------------------------------------------------------------
 * Provider shim – renders children directly
 * ------------------------------------------------------------------*/
export const ClerkProvider = ({ children }: { children: ReactNode }) => {
  return <>{children}</>;
};

export default {
  useAuth,
  useUser,
  useClerk,
  SignIn,
  SignUp,
  ClerkProvider,
};
