import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Sparkles, 
  Twitter, 
  Github, 
  Linkedin, 
  Send,
  ArrowRight,
  Globe,
  MessageSquare,
  Heart,
  Star,
  Zap,
  Shield,
  ChevronRight,
  ArrowUpRight,
  Wand2,
  Sparkle,
  Rocket,
  Gift,
  Lightbulb,
  Flame,
  Mail,
  Facebook,
  Instagram
} from "lucide-react";
import { useSiteConfig } from '@/contexts/SiteConfigContext';

export const Footer = () => {
  const { config } = useSiteConfig();
  const currentYear = new Date().getFullYear();
  
  // Use social links from config, if available
  const socialLinks = config?.socialLinks || {
    twitter: '',
    facebook: '',
    instagram: '',
    linkedin: '',
    github: ''
  };
  
  // Use footer text from config or default
  const footerText = config?.footerText || `© ${currentYear} Mindrix. All rights reserved.`;
  
  // Use site description from config or default
  const siteDescription = config?.siteDescription || 'Discover and compare the best AI tools for your needs.';
  
  // Use contact email from config or default
  const contactEmail = config?.contactEmail || '<EMAIL>';

  return (
    <footer className="border-t border-gray-200 bg-green-50">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Column 1 - About */}
          <div>
            <div className="flex items-center gap-2 mb-4">
              {config?.logo ? (
                <img 
                  src={config.logo} 
                  alt={config?.siteName || 'Mindrix'} 
                  className="max-h-8 max-w-[160px] object-contain"
                />
              ) : (
                <div className="bg-green-500 rounded-lg w-8 h-8 flex items-center justify-center">
                  <Sparkles className="h-4 w-4 text-white" />
                </div>
              )}
              {/* Only show the site name text if logo is not wide or if display name setting is enabled */}
              {(!config?.logo || (config?.showSiteNameWithLogo !== false)) && (
                <span className="font-medium">{config?.siteName || 'Mindrix'}</span>
              )}
            </div>
            <p className="text-sm text-gray-600 mb-4">
              {siteDescription}
            </p>
            <div className="flex items-center gap-4">
              {socialLinks.twitter && (
                <a 
                  href={socialLinks.twitter} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-green-500 transition-colors"
                >
                  <Twitter className="h-5 w-5" />
                </a>
              )}
              {socialLinks.facebook && (
                <a 
                  href={socialLinks.facebook} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-green-500 transition-colors"
                >
                  <Facebook className="h-5 w-5" />
                </a>
              )}
              {socialLinks.instagram && (
                <a 
                  href={socialLinks.instagram} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-green-500 transition-colors"
                >
                  <Instagram className="h-5 w-5" />
                </a>
              )}
              {socialLinks.linkedin && (
                <a 
                  href={socialLinks.linkedin} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-green-500 transition-colors"
                >
                  <Linkedin className="h-5 w-5" />
                </a>
              )}
              {socialLinks.github && (
                <a 
                  href={socialLinks.github} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-green-500 transition-colors"
                >
                  <Github className="h-5 w-5" />
                </a>
              )}
              <a 
                href={`mailto:${contactEmail}`}
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-green-500 transition-colors"
              >
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Column 2 - Products */}
          <div>
            <h3 className="font-medium text-gray-900 mb-4">Products</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/latest-launches" className="text-sm text-gray-600 hover:text-green-500 transition-colors">
                  Latest Launches
                </Link>
              </li>
              <li>
                <Link to="/top-products" className="text-sm text-gray-600 hover:text-green-500 transition-colors">
                  Top Products
                </Link>
              </li>
              <li>
                <Link to="/upcoming" className="text-sm text-gray-600 hover:text-green-500 transition-colors">
                  Upcoming
                </Link>
              </li>
              <li>
                <Link to="/categories" className="text-sm text-gray-600 hover:text-green-500 transition-colors">
                  Categories
                </Link>
              </li>
              <li>
                <Link to="/trending" className="text-sm text-gray-600 hover:text-green-500 transition-colors">
                  Trending
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 3 - Resources */}
          <div>
            <h3 className="font-medium text-gray-900 mb-4">Resources</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/blog" className="text-sm text-gray-600 hover:text-green-500 transition-colors">
                  Blog
                </Link>
              </li>
              <li>
                <Link to="/latest-news" className="text-sm text-gray-600 hover:text-green-500 transition-colors">
                  Latest News
                </Link>
              </li>
              <li>
                <Link to="/guides" className="text-sm text-gray-600 hover:text-green-500 transition-colors">
                  Guides
                </Link>
              </li>
              <li>
                <Link to="/advertise" className="text-sm text-gray-600 hover:text-green-500 transition-colors">
                  Advertise
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 4 - Newsletter */}
          <div>
            <h3 className="font-medium text-gray-900 mb-4">Stay updated</h3>
            <p className="text-sm text-gray-600 mb-4">
              Subscribe to our newsletter for the latest AI tools and news.
            </p>
            <div className="flex gap-2">
              <Input 
                type="email" 
                placeholder="Enter your email" 
                className="h-10 border-gray-200 focus:border-green-500"
              />
              <Button className="bg-green-500 hover:bg-green-600 h-10">
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between gap-4">
            <p className="text-sm text-gray-500">
              {footerText}
            </p>
            <div className="flex gap-6">
              <Link to="/terms" className="text-sm font-medium text-gray-600 hover:text-green-500 transition-colors">
                Terms
              </Link>
              <Link to="/privacy" className="text-sm font-medium text-gray-600 hover:text-green-500 transition-colors">
                Privacy
              </Link>
              <Link to="/about" className="text-sm font-medium text-gray-600 hover:text-green-500 transition-colors">
                About
              </Link>
              <Link to="/faq" className="text-sm font-medium text-gray-600 hover:text-green-500 transition-colors">
                FAQ
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}; 