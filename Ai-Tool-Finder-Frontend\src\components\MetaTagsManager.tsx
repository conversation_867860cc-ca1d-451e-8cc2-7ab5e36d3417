import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useSiteConfig } from '@/contexts/SiteConfigContext';

export const MetaTagsManager: React.FC = () => {
  const { config } = useSiteConfig();

  useEffect(() => {
    // Log when meta tags are updated
    if (config?.metaTags) {
      console.log('MetaTagsManager: Updating meta tags with:', config.metaTags);
    }
  }, [config?.metaTags]);

  if (!config || !config.metaTags) {
    return null;
  }

  return (
    <Helmet>
      {/* Primary meta tags */}
      <title>{config.metaTags.title}</title>
      <meta name="description" content={config.metaTags.description} />
      <meta name="keywords" content={config.metaTags.keywords} />
      
      {/* OpenGraph meta tags */}
      <meta property="og:title" content={config.metaTags.title} />
      <meta property="og:description" content={config.metaTags.description} />
      {config.metaTags.ogImage && <meta property="og:image" content={config.metaTags.ogImage} />}
      <meta property="og:type" content="website" />
      
      {/* Favicon */}
      {config.favicon && <link rel="icon" href={config.favicon} />}
      
      {/* Apply custom CSS if available */}
      {config.customCss && (
        <style type="text/css">
          {config.customCss}
        </style>
      )}
      
      {/* Apply custom JS if available */}
      {config.customJs && (
        <script type="text/javascript">
          {config.customJs}
        </script>
      )}
    </Helmet>
  );
}; 