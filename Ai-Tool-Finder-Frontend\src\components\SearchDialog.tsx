import { useState, useEffect, useRef, useMemo } from "react";
import { X, Search } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useTools } from "@/lib/api/tools";
import { Tool } from "@/types/tool";

interface SearchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectTool?: (tool: Tool) => void;
}

export const SearchDialog = ({ 
  open, 
  onOpenChange,
  onSelectTool
}: SearchDialogProps) => {
  const [query, setQuery] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const { data: tools = [] } = useTools();

  // Focus input when dialog opens
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    } else {
      setQuery("");
    }
  }, [open]);

  // Filter tools based on search query
  const filteredTools = useMemo(() => {
    if (!query.trim()) {
      return [];
    }

    const lowerQuery = query.toLowerCase();
    const filtered = tools.filter(
      (tool) =>
        tool.name.toLowerCase().includes(lowerQuery) ||
        tool.description.toLowerCase().includes(lowerQuery)
    );

    return filtered.slice(0, 5); // Limit to 5 results
  }, [query, tools]);

  const handleSelectTool = (tool: Tool) => {
    if (onSelectTool) {
      onSelectTool(tool);
    }
    setQuery("");
    onOpenChange(false);
  };

  const handleClearSearch = () => {
    setQuery("");
    inputRef.current?.focus();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="p-0 gap-0 overflow-hidden w-full max-w-2xl bg-white border border-gray-200 rounded-xl shadow-2xl">
        {/* Accessible title & description for screen readers */}
        <DialogHeader className="sr-only">
          <DialogTitle>Search Tools</DialogTitle>
          <DialogDescription>
            Search for any tool or feature by typing in the input below.
          </DialogDescription>
        </DialogHeader>
        <div className="flex items-center p-4 border-b border-gray-100">
          <div className="relative flex items-center w-full">
            <Search className="absolute left-4 h-5 w-5 text-gray-400" />
            <Input
              ref={inputRef}
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search for any tool or feature..."
              className="pl-12 h-12 w-full border border-gray-200 text-base rounded-full bg-gray-50 focus-visible:ring-1 focus-visible:ring-green-500 focus-visible:border-green-500 pr-10"
            />
            {query && (
              <button 
                onClick={handleClearSearch}
                className="absolute right-3 p-1.5 rounded-full hover:bg-gray-200"
              >
                <X className="w-4 h-4 text-gray-500" />
              </button>
            )}
          </div>
        </div>

        {filteredTools.length > 0 && (
          <div className="py-4 max-h-[70vh] overflow-y-auto divide-y divide-gray-100">
            {filteredTools.map((tool) => (
              <div 
                key={tool.id || tool._id}
                className="px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleSelectTool(tool)}
              >
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
                    {tool.logo ? (
                      <img 
                        src={tool.logo} 
                        alt={tool.name} 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null;
                          target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(tool.name)}&background=10b981&color=fff&format=svg`;
                        }}
                      />
                    ) : (
                      <div className="w-full h-full bg-green-100 text-green-800 flex items-center justify-center font-bold">
                        {tool.name.charAt(0)}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-medium text-gray-900">{tool.name}</h3>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">{tool.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {query && filteredTools.length === 0 && (
          <div className="py-16 text-center text-gray-500">
            <p>No results found for "{query}"</p>
          </div>
        )}

        {!query && (
          <div className="py-16 text-center text-gray-500">
            <p>Type to search for AI tools</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}; 