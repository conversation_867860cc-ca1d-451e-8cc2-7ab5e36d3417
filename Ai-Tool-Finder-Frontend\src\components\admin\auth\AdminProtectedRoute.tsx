import { useAuth } from "../../../contexts/AuthContext";
import { Navigate, Outlet } from "react-router-dom";

// For testing purposes, add more domains or set to empty array to allow any email
// const ADMIN_EMAIL_DOMAINS = ["webbuddy.agency"]; // Only webbuddy.agency for production

export function AdminProtectedRoute() {
  const { isAuthenticated, user } = useAuth();

  console.log('Admin protection state:', { isAuthenticated, user });

  if (!user) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    console.log('User not signed in');
    return <Navigate to="/" replace />;
  }

  // Check both email domain and role
  // const hasAdminEmail = ADMIN_EMAIL_DOMAINS.length === 0 || // Allow any email if array is empty
  //   user.emailAddresses.some(email => 
  //     ADMIN_EMAIL_DOMAINS.some(domain => 
  //       email.emailAddress.endsWith(`@${domain}`)
  //     )
  //   );
  
  const hasAdminRole = user.role === 'admin';

  const isAdmin = hasAdminRole;

  console.log('Admin check:', { 
    hasAdminRole,
    isAdmin,
    userEmail: user.email,
    userRole: user.role,
    // allowedDomains: ADMIN_EMAIL_DOMAINS 
  });

  if (!isAdmin) {
    console.log('User is not an admin');
    return <Navigate to="/" replace />;
  }

  return <Outlet />;
} 