import { useState, useEffect, useRef } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Wrench, 
  FileText, 
  Newspaper, 
  Users, 
  Menu,
  X,
  Send,
  MessageSquare,
  LucideIcon,
  Star,
  User,
  Home,
  LogOut,
  ChevronLeft,
  ChevronRight,
  MessageCircle,
  Settings
} from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAuth } from "../../../contexts/AuthContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface NavigationItem {
  name: string;
  href: string;
  icon: LucideIcon;
}

const navigation: NavigationItem[] = [
  { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },
  { name: 'Tools', href: '/admin/tools', icon: Wrench },
  { name: 'Sponsored Listings', href: '/admin/sponsorships', icon: Star },
  // Temporarily commented out
  // { name: 'Software Pages', href: '/admin/software', icon: Layout },
  { name: 'Blog', href: '/admin/blog', icon: FileText },
  { name: 'News', href: '/admin/news', icon: Newspaper },
  { name: 'Users', href: '/admin/users', icon: Users },
  { name: 'Tool Submissions', href: '/admin/submissions', icon: Send },
  { name: 'Reviews', href: '/admin/reviews', icon: MessageCircle },
  { name: 'Sales Inquiries', href: '/admin/inquiries', icon: MessageSquare },
  { name: 'Site Settings', href: '/admin/settings', icon: Settings },
];

export function AdminLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isPermanentlyCollapsed, setIsPermanentlyCollapsed] = useState(false);
  const sidebarRef = useRef<HTMLElement>(null);
  const location = useLocation();
  const navigate = useNavigate();
  const { signOut, user } = useAuth();

  // Load sidebar state from localStorage on initial render
  useEffect(() => {
    const savedState = localStorage.getItem('adminSidebarCollapsed');
    if (savedState !== null) {
      setIsPermanentlyCollapsed(savedState === 'true');
    }
  }, []);

  // Handle responsive behavior
  useEffect(() => {
    const checkScreenSize = () => {
      const isMobileView = window.innerWidth < 768;
      setIsMobile(isMobileView);
      setSidebarOpen(window.innerWidth >= 768 && !isPermanentlyCollapsed);
    };
    
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    
    return () => window.removeEventListener('resize', checkScreenSize);
  }, [isPermanentlyCollapsed]);

  // Close sidebar when clicking outside of it on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && sidebarOpen && sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        setSidebarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, sidebarOpen]);

  const handleSignOut = async () => {
    await signOut();
    navigate("/");
  };

  // Toggle permanent collapsed state (for desktop)
  const togglePermanentCollapse = () => {
    const newState = !isPermanentlyCollapsed;
    setIsPermanentlyCollapsed(newState);
    localStorage.setItem('adminSidebarCollapsed', String(newState));
    
    // If we're on desktop, also update the open state
    if (!isMobile) {
      setSidebarOpen(!newState);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin Header - now fixed */}
      <header className="fixed top-0 left-0 right-0 z-[999] border-b bg-white/90 backdrop-blur-md">
        <div className="flex h-16 items-center justify-between pl-0 pr-4 md:pr-8">
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-2 pl-4 md:pl-6">
              <div className="p-1.5 bg-green-500 rounded-md">
                <svg className="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                  <polyline points="9 22 9 12 15 12 15 22"/>
                </svg>
              </div>
              <div className="text-xl font-bold text-gray-900">
              Mindrix
              </div>
            </Link>
            <span className="hidden md:inline-block ml-2 text-sm font-medium text-gray-500">
              Admin Portal
            </span>
          </div>

          <div className="flex items-center gap-2">
            {/* Sidebar toggle for desktop */}
            <Button 
              variant="ghost" 
              size="icon"
              className="hidden md:flex mr-2"
              onClick={togglePermanentCollapse}
              title={isPermanentlyCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              {isPermanentlyCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
            </Button>
            
            <Button 
              variant="ghost" 
              size="sm" 
              className="hidden md:flex items-center gap-2"
              onClick={() => navigate("/")}
            >
              <Home className="h-4 w-4" />
              <span>Main Site</span>
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      {user?.imageUrl ? (
                        <img 
                          src={user.imageUrl} 
                          alt={`${user?.firstName || ''} ${user?.lastName || ''}`.trim() || "User"} 
                          className="w-8 h-8 rounded-full" 
                        />
                      ) : (
                        <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                          <User className="h-4 w-4 text-green-600" />
                        </div>
                      )}
                    </div>
                    <span className="hidden md:block text-sm font-medium">
                      {`${user?.firstName || ''} ${user?.lastName || ''}`.trim() || user?.email || "Admin"}
                    </span>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem onClick={() => navigate("/")}> 
                  <Home className="mr-2 h-4 w-4" />
                  <span>Return to Main Site</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Sign out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>
      {/* Spacer to prevent content from being hidden behind the fixed navbar */}
      <div className="h-16"></div>

      <div className="flex relative">
        {/* Sidebar */}
        <aside 
          ref={sidebarRef}
          className={cn(
            "fixed left-0 top-[64px] z-30 h-[calc(100vh-64px)] transform transition-all duration-200 ease-in-out bg-white/95 backdrop-blur-lg shadow-lg shadow-green-500/20 border border-gray-200/50",
            isPermanentlyCollapsed 
              ? "md:w-[72px]" 
              : "w-full xs:w-[280px] sm:w-72 md:top-[84px] md:h-[calc(100vh-100px)]",
            "rounded-none md:rounded-r-2xl",
            sidebarOpen ? "translate-x-0" : "-translate-x-full",
            "md:translate-x-0"
          )}
        >
          <div className={cn(
            "flex items-center justify-between border-b border-gray-200/50",
            isPermanentlyCollapsed 
              ? "h-16 md:h-16 px-2" 
              : "h-16 md:h-20 px-4 md:px-6"
          )}>
            {!isPermanentlyCollapsed && (
              <h1 className="text-lg md:text-xl font-semibold">
                <span className="bg-gradient-to-r from-green-500 to-green-600 bg-clip-text text-transparent">
                  Admin Dashboard
                </span>
              </h1>
            )}
            <div className={cn(
              "flex items-center",
              isPermanentlyCollapsed ? "w-full justify-center" : ""
            )}>
              {/* Toggle button for desktop */}
              <Button
                variant="ghost"
                size="icon"
                className="hover:bg-green-50 rounded-xl"
                onClick={togglePermanentCollapse}
                title={isPermanentlyCollapsed ? "Expand sidebar" : "Collapse sidebar"}
              >
                {isPermanentlyCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
              </Button>
              
              {/* Close button for mobile */}
              {!isPermanentlyCollapsed && (
                <Button
                  variant="ghost"
                  className="md:hidden hover:bg-green-50 rounded-xl ml-2"
                  onClick={() => setSidebarOpen(false)}
                >
                  <X size={20} />
                </Button>
              )}
            </div>
          </div>
          
          <ScrollArea className={cn(
            "py-4",
            isPermanentlyCollapsed 
              ? "h-[calc(100vh-72px-64px)] px-1" 
              : "h-[calc(100vh-72px-64px)] md:h-[calc(100vh-100px-80px)] px-4 md:py-6"
          )}>
            <nav className="space-y-1 md:space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.href}
                  to={item.href}
                  title={isPermanentlyCollapsed ? item.name : ""}
                  className={cn(
                    "flex items-center transition-all rounded-xl hover:scale-[1.02]",
                    location.pathname === item.href 
                      ? "bg-green-50 text-green-600 font-medium shadow-sm" 
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50",
                    isPermanentlyCollapsed 
                      ? "justify-center py-3 px-2" 
                      : "gap-3 md:gap-4 px-3 md:px-4 py-2 md:py-3 text-sm md:text-base"
                  )}
                  onClick={() => isMobile && setSidebarOpen(false)}
                >
                  <div className={cn(
                    "rounded-lg transition-colors",
                    isPermanentlyCollapsed ? "p-1.5" : "p-1.5 md:p-2",
                    location.pathname === item.href
                      ? "bg-green-100"
                      : "group-hover:bg-gray-100"
                  )}>
                    <item.icon className={cn(
                      isPermanentlyCollapsed ? "h-5 w-5" : "h-4 w-4 md:h-5 md:w-5"
                    )} />
                  </div>
                  {!isPermanentlyCollapsed && <span className="font-medium">{item.name}</span>}
                </Link>
              ))}
            </nav>
          </ScrollArea>
        </aside>

        {/* Mobile Sidebar Toggle */}
        <Button
          variant="ghost"
          className="fixed top-[84px] left-4 z-40 md:hidden hover:bg-green-50 rounded-xl"
          onClick={() => setSidebarOpen(true)}
          aria-label="Open Menu"
        >
          <Menu size={20} />
        </Button>

        {/* Main Content */}
        <main className={cn(
          "flex-1 min-h-[calc(100vh-72px)] transition-all duration-200 ease-in-out bg-transparent w-full",
          isMobile 
            ? "" 
            : isPermanentlyCollapsed 
              ? "md:ml-[72px]" 
              : "md:ml-[280px]"
        )}>
          <div className="w-full pt-14 md:pt-6 px-2 sm:px-4 md:px-6 lg:px-8 pb-6 overflow-y-auto">
            <div className="bg-white/95 backdrop-blur-lg border border-gray-200/50 rounded-xl md:rounded-2xl shadow-lg shadow-green-500/20 w-full max-w-7xl mx-auto h-full">
              <div className="px-3 sm:px-4 md:px-6 py-4 h-full overflow-x-auto">
                <Outlet />
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
} 