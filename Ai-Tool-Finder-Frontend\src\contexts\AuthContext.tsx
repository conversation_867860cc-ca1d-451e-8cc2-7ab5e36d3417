import React, { createContext, useContext, useEffect, useState } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '../config/constants';

type ModalType = 'login' | 'signup' | 'verify-email' | 'forgot-password' | 'reset-password' | 'otp' | null;

interface AuthContextType {
  isAuthenticated: boolean;
  user: any;
  currentModal: ModalType;
  userEmail: string;
  openModal: (type: ModalType, email?: string) => void;
  closeModal: () => void;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, role?: string) => Promise<void>;
  signOut: () => void;
  refreshUser: () => Promise<void>;
  getToken: () => Promise<string | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [currentModal, setCurrentModal] = useState<ModalType>(null);
  const [userEmail, setUserEmail] = useState('');
  const [token, setToken] = useState<string | null>(() => localStorage.getItem('token'));
  const [user, setUser] = useState<any>(null);

  // Configure axios with the token whenever it changes
  useEffect(() => {
    if (token) {
      console.log('Setting authorization token in axios defaults');
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // fetch user info
      axios.get(`${API_BASE_URL}/api/auth/me`)
        .then(r => {
          console.log('User info fetched successfully');
          const u = r.data.user;
          setUser({ ...u, imageUrl: u.profileImageUrl });
        })
        .catch(err => {
          console.error('Error fetching user info:', err);
          // If token is invalid, clear it
          if (err.response && (err.response.status === 401 || err.response.status === 403)) {
            console.log('Invalid token detected, clearing authentication');
            localStorage.removeItem('token');
            setToken(null);
          }
        });
    } else {
      console.log('No token available, clearing axios authorization header');
      delete axios.defaults.headers.common['Authorization'];
      setUser(null);
    }
  }, [token]);

  const openModal = (type: ModalType, email?: string) => {
    setCurrentModal(type);
    if (email) {
      setUserEmail(email);
    }
  };

  const closeModal = () => {
    setCurrentModal(null);
    setUserEmail('');
  };

  const login = async (email: string, password: string) => {
    const res = await axios.post(`${API_BASE_URL}/api/auth/login`, { email, password });
    if (res.data.token) {
      localStorage.setItem('token', res.data.token);
      setToken(res.data.token);
      closeModal();
    } else {
      throw new Error('No token received from server');
    }
  };

  const signup = async (email: string, password: string, role = 'basic') => {
    const res = await axios.post(`${API_BASE_URL}/api/auth/signup`, { email, password, role });
    if (res.data.token) {
      localStorage.setItem('token', res.data.token);
      setToken(res.data.token);
      closeModal();
    } else {
      throw new Error('No token received from server');
    }
  };

  const signOut = () => {
    localStorage.removeItem('token');
    setToken(null);
    closeModal();
  };

  const getToken = async (): Promise<string | null> => {
    // First check state
    if (token) {
      return token;
    }
    
    // If not in state, check localStorage
    const storedToken = localStorage.getItem('token');
    if (storedToken) {
      // Update state if found in localStorage
      setToken(storedToken);
      return storedToken;
    }
    
    return null;
  };

  const refreshUser = async () => {
    const currentToken = await getToken();
    if (!currentToken) return;
    
    try {
      const res = await axios.get(`${API_BASE_URL}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${currentToken}`
        }
      });
      const u = res.data.user; 
      setUser({ ...u, imageUrl: u.profileImageUrl });
    } catch (e) {
      console.error('Error refreshing user:', e);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated: !!token,
        user,
        currentModal,
        userEmail,
        openModal,
        closeModal,
        login,
        signup,
        signOut,
        refreshUser,
        getToken,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 