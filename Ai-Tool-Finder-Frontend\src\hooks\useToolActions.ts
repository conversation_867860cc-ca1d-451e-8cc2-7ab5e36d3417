import { useCallback, useEffect, useState } from 'react';
import { toast } from "sonner";
import { API_BASE_URL } from "../config/constants";
import { useAuth } from "../contexts/AuthContext";
import axios from 'axios';

// DEFAULT_VOTE_COUNT can be imported if needed for display fallbacks elsewhere
// import { DEFAULT_VOTE_COUNT } from '@/utils/voteUtils';

interface UserMetadata {
  upvotedTools?: string[];
  savedTools?: string[];
}

interface ToolActions {
  upvotedTools: string[];
  savedTools: string[];
  isUpvoted: (dbToolId: string) => boolean;
  isSaved: (dbToolId: string) => boolean;
  toggleUpvote: (actualDbId: string, currentVoteCount?: number) => Promise<void>;
  toggleSave: (dbToolId: string) => Promise<void>;
  isUpvoteLoading: boolean;
  isSaveLoading: boolean;
  isLoading: boolean;
  loadUserPreferences: () => Promise<void>;
  // getVoteCount is removed, vote counts come from the tool object directly.
}

export function useToolActions(): ToolActions {
  const { isAuthenticated, user, getToken } = useAuth();
  const [upvotedTools, setUpvotedTools] = useState<string[]>([]);
  const [savedTools, setSavedTools] = useState<string[]>([]);
  const [isPreferencesLoading, setIsPreferencesLoading] = useState(false);
  const [isUpvoteLoading, setIsUpvoteLoading] = useState(false);
  const [isSaveLoading, setIsSaveLoading] = useState(false);

  // Combined loading state for backward compatibility
  const isLoading = isPreferencesLoading || isUpvoteLoading || isSaveLoading;

  const loadUserPreferences = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setUpvotedTools([]);
      setSavedTools([]);
      return;
    }

    try {
      setIsPreferencesLoading(true);
      
      // Get the authentication token directly from localStorage for debugging
      const token = localStorage.getItem('token');

      if (!token) {
        console.warn('No auth token found in localStorage');
        return;
      }
      
      // Create a custom instance of axios for this request to ensure headers are set correctly
      const axiosInstance = axios.create({
        baseURL: API_BASE_URL,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
        
      try {
        // Fetch preferences directly from the server
        const response = await axiosInstance.get('/api/users/me/preferences');
        
        // Update state with server data
        if (response.data) {
          console.log('Preferences loaded successfully:', response.data);
          setUpvotedTools(response.data.upvotedTools || []);
          setSavedTools(response.data.savedTools || []);
          console.log('Loaded user preferences from server:', {
            upvotedCount: response.data.upvotedTools?.length || 0,
            savedCount: response.data.savedTools?.length || 0
          });
        }
      } catch (apiError) {
        console.error('API Error details:', {
          status: apiError.response?.status,
          statusText: apiError.response?.statusText,
          data: apiError.response?.data,
          headers: apiError.response?.headers
        });
        
        // Try a direct fetch as a fallback
        console.log('Trying fallback with fetch API...');
        try {
          const fetchResponse = await fetch(`${API_BASE_URL}/api/users/me/preferences`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (fetchResponse.ok) {
            const data = await fetchResponse.json();
            console.log('Fetch API succeeded:', data);
            setUpvotedTools(data.upvotedTools || []);
            setSavedTools(data.savedTools || []);
          } else {
            console.error('Fetch API failed:', fetchResponse.status, fetchResponse.statusText);
            throw new Error(`Fetch API failed: ${fetchResponse.status}`);
          }
        } catch (fetchError) {
          console.error('Fetch fallback failed:', fetchError);
          throw fetchError;
        }
      }
    } catch (error) {
      console.error('Error fetching user preferences:', error);
      // Reset to empty arrays on error
      setUpvotedTools([]);
      setSavedTools([]);
    } finally {
      setIsPreferencesLoading(false);
    }
  }, [isAuthenticated, user]);

  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserPreferences();
    }
  }, [loadUserPreferences, isAuthenticated, user]);

  // Listen to custom events to keep state in sync across pages
  useEffect(() => {
    const handleSync = () => loadUserPreferences();
    window.addEventListener('toolVotesUpdated', handleSync as EventListener);
    window.addEventListener('savedToolsUpdated', handleSync as EventListener);
    return () => {
      window.removeEventListener('toolVotesUpdated', handleSync as EventListener);
      window.removeEventListener('savedToolsUpdated', handleSync as EventListener);
    };
  }, [loadUserPreferences]);

  const isUpvoted = (dbToolId: string): boolean => {
    if (!isAuthenticated || !dbToolId) return false;
    return upvotedTools.includes(dbToolId);
  };

  const isSaved = (dbToolId: string): boolean => {
    if (!isAuthenticated || !dbToolId) return false;
    return savedTools.includes(dbToolId);
  };

  const toggleUpvote = async (actualDbId: string, currentVoteCount?: number) => {
    if (!isAuthenticated) {
      toast.error("Please sign in to upvote tools.");
      return;
    }
    if (!actualDbId || actualDbId.startsWith('temp-')) {
      toast.error("This tool cannot be upvoted (invalid or temporary ID).");
      console.warn("toggleUpvote called with invalid or temporary ID:", actualDbId);
      return;
    }

    setIsUpvoteLoading(true);
    const alreadyUpvoted = upvotedTools.includes(actualDbId);
    const action = alreadyUpvoted ? "downvote" : "upvote";

    try {
      console.log(`Attempting to ${action} tool with DB ID: ${actualDbId}`);
      console.log('Current upvoted tools before action:', upvotedTools);
      
      // Get the authentication token directly from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }
      
      console.log('Using token for upvote:', token ? `${token.substring(0, 20)}...` : 'null');
      console.log('Full request URL:', `${API_BASE_URL}/api/tools/${actualDbId}/vote`);
      
      let response;
      
      try {
        // Create a custom instance of axios for this request
        const axiosInstance = axios.create({
          baseURL: API_BASE_URL,
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        // Call backend API first
        response = await axiosInstance.post(`/api/tools/${actualDbId}/vote`, { action });
      } catch (axiosError) {
        console.error('Axios error details:', {
          status: axiosError.response?.status,
          statusText: axiosError.response?.statusText,
          data: axiosError.response?.data
        });
        
        // Try with fetch API as fallback
        console.log('Trying fallback with fetch API...');
        const fetchResponse = await fetch(`${API_BASE_URL}/api/tools/${actualDbId}/vote`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ action })
        });
        
        if (!fetchResponse.ok) {
          throw new Error(`Fetch API failed: ${fetchResponse.status}`);
        }
        
        response = { data: await fetchResponse.json(), status: fetchResponse.status };
      }

      if (!response.data || response.status !== 200) {
        throw new Error("Failed to update vote on server.");
      }

      // Update local state based on server response
      const isNowUpvoted = response.data.isUpvoted;
      console.log(`Server response: Tool is now upvoted: ${isNowUpvoted}`);
      
      // Update local state to match server state
      const newUpvotedTools = isNowUpvoted
        ? [...upvotedTools.filter(id => id !== actualDbId), actualDbId]
        : upvotedTools.filter(id => id !== actualDbId);
      
      console.log('Updating upvoted tools to:', newUpvotedTools);
      setUpvotedTools(newUpvotedTools);
      
      // Update UI vote count for immediate feedback
      const newVoteCount = response.data.votes ?? 
        (action === "upvote" 
          ? (currentVoteCount || 0) + 1
          : Math.max(0, (currentVoteCount || 0) - 1));
      
      // Dispatch event for UI updates elsewhere
      window.dispatchEvent(new CustomEvent('toolVotesUpdated', {
        detail: { 
          toolId: actualDbId, 
          votes: newVoteCount, 
          isUpvoted: isNowUpvoted 
        }
      }));
      
      toast.success(action === "upvote" ? "Tool upvoted!" : "Upvote removed.");
      
      // Refresh user preferences to ensure consistency
      loadUserPreferences();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
      console.error(`Error during toggleUpvote for ${actualDbId}:`, errorMessage, error);
      toast.error("Failed to update vote. Please try again later.");
      
      // Refresh preferences to ensure UI is in sync with server
      loadUserPreferences();
    } finally {
      setIsUpvoteLoading(false);
    }
  };

  const toggleSave = async (dbToolId: string) => {
    if (!isAuthenticated) {
      toast.error("Please sign in to save tools.");
      return;
    }
    if (!dbToolId || typeof dbToolId !== 'string' || dbToolId.trim() === '') {
      toast.error("Invalid tool identifier for saving.");
      return;
    }
    
    setIsSaveLoading(true);
    const alreadySaved = savedTools.includes(dbToolId);
    
    try {
      // Get the authentication token directly from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }
      
      console.log('Using token for save:', token ? `${token.substring(0, 20)}...` : 'null');
      console.log('Full request URL:', `${API_BASE_URL}/api/tools/${dbToolId}/save`);
      
      let response;
      
      try {
        // Create a custom instance of axios for this request
        const axiosInstance = axios.create({
          baseURL: API_BASE_URL,
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        // Call backend API first
        response = await axiosInstance.post(`/api/tools/${dbToolId}/save`, {});
      } catch (axiosError) {
        console.error('Axios error details:', {
          status: axiosError.response?.status,
          statusText: axiosError.response?.statusText,
          data: axiosError.response?.data
        });
        
        // Try with fetch API as fallback
        console.log('Trying fallback with fetch API...');
        const fetchResponse = await fetch(`${API_BASE_URL}/api/tools/${dbToolId}/save`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({})
        });
        
        if (!fetchResponse.ok) {
          throw new Error(`Fetch API failed: ${fetchResponse.status}`);
        }
        
        response = { data: await fetchResponse.json(), status: fetchResponse.status };
      }

      if (!response.data || response.status !== 200) {
        throw new Error("Failed to update saved tools on server.");
      }
      
      // Update local state after successful server update
      const newSavedTools = response.data.savedTools || 
        (alreadySaved
          ? savedTools.filter(id => id !== dbToolId)
          : [...savedTools, dbToolId]);
      
      setSavedTools(newSavedTools);
      
      // Dispatch event for UI updates elsewhere
      window.dispatchEvent(new CustomEvent('savedToolsUpdated'));

      toast.success(alreadySaved ? "Removed from saved." : "Tool saved!");

    } catch (error) {
      console.error('Error toggling save:', error);
      toast.error("Failed to update saved tools. Please try again later.");
      
      // Refresh preferences to ensure UI is in sync with server
      loadUserPreferences();
    } finally {
      setIsSaveLoading(false);
    }
  };

  return {
    upvotedTools,
    savedTools,
    isUpvoted,
    isSaved,
    toggleUpvote,
    toggleSave,
    isUpvoteLoading,
    isSaveLoading,
    isLoading,
    loadUserPreferences,
  };
}