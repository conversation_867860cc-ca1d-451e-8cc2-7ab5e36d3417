@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 151 55% 50%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 151 55% 50%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 151 55% 50%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 151 55% 50%;
  }

  * {
    font-family: 'Inter', sans-serif;
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

@layer components {
  .glass-card {
    @apply bg-white/80 backdrop-blur-lg border border-white/20 shadow-lg;
  }

  .hover-scale {
    @apply transition-transform duration-300 hover:scale-[1.02];
  }
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  html, body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: none;
    position: relative;
    height: 100%;
    touch-action: manipulation;
  }
  
  /* Better touch feedback */
  a, button, [role="button"] {
    -webkit-tap-highlight-color: transparent;
    cursor: pointer;
  }
  
  /* Active states for mobile tap */
  .active-scale {
    transition: transform 0.1s ease;
  }
  
  .active-scale:active {
    transform: scale(0.97);
  }
  
  /* Mobile scrolling areas */
  .mobile-scroll-area {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }
  
  .mobile-scroll-area::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
  
  /* Add smooth corners to match iOS design language */
  .mobile-rounded {
    border-radius: 0.75rem;
  }
  
  /* Mobile safe areas for bottom navigation */
  .mobile-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom, 1rem);
  }
  
  /* Improve modal interactions on mobile */
  .mobile-modal-sheet {
    border-radius: 1rem 1rem 0 0;
    transform: translateY(0);
    transition: transform 0.3s ease-out;
  }
  
  .mobile-modal-sheet.closed {
    transform: translateY(100%);
  }
  
  /* Mobile bottom sheet */
  .mobile-bottom-sheet {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background-color: white;
    border-top-left-radius: 1rem;
    border-top-right-radius: 1rem;
    box-shadow: 0 -4px 6px -1px rgb(0 0 0 / 0.05);
    transform: translateY(0);
    transition: transform 0.3s cubic-bezier(0.33, 1, 0.68, 1);
  }
  
  .mobile-bottom-sheet.closed {
    transform: translateY(100%);
  }
}