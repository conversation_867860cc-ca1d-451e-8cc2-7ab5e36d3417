import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from "@/contexts/AuthContext";
import { Tool } from '@/types/tool';

const API_URL = import.meta.env.VITE_API_URL || 'https://api.aihunt.site';

// API functions
async function getTools(): Promise<Tool[]> {
  console.log('Fetching tools');
  const response = await fetch(`${API_URL}/api/tools`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    console.error('Error fetching tools:', errorData);
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  console.log('Received tools data:', data);
  return data;
}

async function createTool(token: string, toolData: Omit<Tool, 'id' | 'createdAt' | 'updatedAt'>) {
  const response = await fetch(`${API_URL}/api/tools`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(toolData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

async function updateTool(token: string, toolId: string, toolData: Partial<Tool>) {
  const response = await fetch(`${API_URL}/api/tools/${toolId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(toolData),
  });

  if (!response.ok) {
    throw new Error('Failed to update tool');
  }

  return response.json();
}

async function deleteTool(token: string, toolId: string) {
  const response = await fetch(`${API_URL}/api/tools/${toolId}`, {
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to delete tool');
  }

  return response.json();
}

async function updateToolStatus(token: string, toolId: string, status: Tool['status']) {
  const response = await fetch(`${API_URL}/api/tools/${toolId}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ status }),
  });

  if (!response.ok) {
    throw new Error('Failed to update tool status');
  }

  return response.json();
}

// React Query hooks
export function useTools() {
  return useQuery({
    queryKey: ['tools'],
    queryFn: getTools,
  });
}

export function useCreateTool() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (toolData: Omit<Tool, 'id' | 'createdAt' | 'updatedAt'>) => {
      const token = await getToken();
      return createTool(token, toolData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tools'] });
    },
  });
}

export function useUpdateTool() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      toolId, 
      data 
    }: { 
      toolId: string; 
      data: Partial<Tool>; 
    }) => {
      const token = await getToken();
      return updateTool(token, toolId, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tools'] });
    },
  });
}

export function useDeleteTool() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (toolId: string) => {
      const token = await getToken();
      return deleteTool(token, toolId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tools'] });
    },
  });
}

export function useUpdateToolStatus() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      toolId, 
      status 
    }: { 
      toolId: string; 
      status: Tool['status']; 
    }) => {
      const token = await getToken();
      return updateToolStatus(token, toolId, status);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tools'] });
    },
  });
} 