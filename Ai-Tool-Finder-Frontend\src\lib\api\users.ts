import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from "@/contexts/AuthContext";
import { User } from '@/types/user';

const API_URL = import.meta.env.VITE_API_URL || 'https://api.aihunt.site';

// API functions
async function getUsers(token: string): Promise<User[]> {
  console.log('Fetching users...');
  const response = await fetch(`${API_URL}/api/users`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    console.error('Error fetching users:', errorData);
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  console.log('Received users:', data.length);
  return data;
}

async function updateUserRole(token: string, userId: string, role: User['role'], reason: string) {
  // Clean userId - remove user_ prefix if present
  const cleanUserId = userId.replace('user_', '');
  console.log('Updating role for user:', {
    originalId: userId,
    cleanId: cleanUserId,
    role,
    reason
  });
  
  const response = await fetch(`${API_URL}/api/users/${cleanUserId}/role`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({ role, reason }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    console.error('Role update failed:', errorData);
    throw new Error(errorData.error || `Failed to update user role: ${response.status}`);
  }

  const data = await response.json();
  console.log('Role update successful:', data);
  return data;
}

async function updateUserStatus(token: string, userId: string, status: User['status'], reason: string) {
  // Clean userId - remove user_ prefix if present
  const cleanUserId = userId.replace('user_', '');
  console.log('Updating status for user:', {
    originalId: userId,
    cleanId: cleanUserId,
    status,
    reason
  });

  const response = await fetch(`${API_URL}/api/users/${cleanUserId}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({ status, reason }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    console.error('Status update failed:', errorData);
    throw new Error(errorData.error || `Failed to update user status: ${response.status}`);
  }

  const data = await response.json();
  console.log('Status update successful:', data);
  return data;
}

async function getUserActivity(token: string, userId: string) {
  // Remove 'user_' prefix if it exists
  const cleanUserId = userId.replace('user_', '');

  const response = await fetch(`${API_URL}/api/users/${cleanUserId}/activity`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `Failed to fetch user activity: ${response.status}`);
  }
  return response.json();
}

async function deleteUser(token: string, userId: string) {
  const cleanUserId = userId.replace('user_', '');
  const response = await fetch(`${API_URL}/api/users/${cleanUserId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
    }
  });
  if (!response.ok) {
    const err = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(err.error || `Failed to delete user: ${response.status}`);
  }
  return response.json();
}

async function resetUserPassword(token: string, userId: string, newPassword: string, reason: string) {
  const cleanUserId = userId.replace('user_', '');
  const response = await fetch(`${API_URL}/api/users/${cleanUserId}/password`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({ newPassword, reason }),
  });
  if (!response.ok) {
    const err = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(err.error || `Failed to reset password: ${response.status}`);
  }
  return response.json();
}

async function createUser(token: string, payload: { name: string; email: string; password: string; role: User['role'] }) {
  const response = await fetch(`${API_URL}/api/users`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(payload),
  });
  if (!response.ok) {
    const err = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(err.error || `Failed to create user: ${response.status}`);
  }
  return response.json();
}

async function setAdminRole(token: string, userId: string) {
  // Remove 'user_' prefix if it exists
  const cleanUserId = userId.replace('user_', '');

  const response = await fetch(`${API_URL}/api/users/${cleanUserId}/make-admin`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// Agency user management API functions
async function getAgencyManagedUsers(token: string): Promise<User[]> {
  const response = await fetch(`${API_URL}/api/users/agency-managed`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

async function createAgencyUser(token: string, payload: { name: string; email: string; password: string }) {
  const response = await fetch(`${API_URL}/api/users/agency-create`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(payload),
  });
  
  if (!response.ok) {
    const err = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(err.error || `Failed to create user: ${response.status}`);
  }
  
  return response.json();
}

async function updateAgencyUser(token: string, userId: string, payload: { name?: string; email?: string; password?: string }) {
  const response = await fetch(`${API_URL}/api/users/agency-managed/${userId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(payload),
  });
  
  if (!response.ok) {
    const err = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(err.error || `Failed to update user: ${response.status}`);
  }
  
  return response.json();
}

async function deleteAgencyUser(token: string, userId: string) {
  const response = await fetch(`${API_URL}/api/users/agency-managed/${userId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
  
  if (!response.ok) {
    const err = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(err.error || `Failed to delete user: ${response.status}`);
  }
  
  return response.json();
}

// React Query hooks
export function useUsers() {
  const { getToken } = useAuth();
  
  return useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      try {
        const token = await getToken();
        if (!token) {
          throw new Error('No authentication token available');
        }
        console.log('Fetching users with token:', token ? 'Present' : 'Missing');
        return getUsers(token);
      } catch (error) {
        console.error('Error in useUsers hook:', error);
        throw error;
      }
    },
  });
}

export function useUserActivity(userId: string) {
  const { getToken } = useAuth();
  
  return useQuery({
    queryKey: ['user-activity', userId],
    queryFn: async () => {
      const token = await getToken();
      return getUserActivity(token, userId);
    },
    enabled: !!userId,
  });
}

export function useUpdateUserRole() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ userId, role, reason }: { userId: string; role: User['role']; reason: string }) => {
      const token = await getToken();
      return updateUserRole(token, userId, role, reason);
    },
    onSuccess: (_, { userId, role }) => {
      queryClient.setQueryData<User[] | undefined>(['users'], (old) => {
        if (!old) return old;
        return old.map(u => (u.id === userId ? { ...u, role } : u));
      });
    },
  });
}

export function useUpdateUserStatus() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ userId, status, reason }: { userId: string; status: User['status']; reason: string }) => {
      const token = await getToken();
      return updateUserStatus(token, userId, status, reason);
    },
    onSuccess: (_, { userId, status }) => {
      queryClient.setQueryData<User[] | undefined>(['users'], (old) => {
        if (!old) return old;
        return old.map(u => (u.id === userId ? { ...u, status } : u));
      });
    }
  });
}

export function useSetAdminRole() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (userId: string) => {
      const token = await getToken();
      return setAdminRole(token, userId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    }
  });
}

export function useResetUserPassword(userId: string) {
  const { getToken } = useAuth();
  return useMutation({
    mutationFn: async ({ newPassword, reason }: { newPassword: string; reason: string }) => {
      const token = await getToken();
      return resetUserPassword(token, userId, newPassword, reason);
    }
  });
}

export function useDeleteUser() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (userId: string) => {
      const token = await getToken();
      return deleteUser(token, userId);
    },
    onSuccess: (_, userId) => {
      queryClient.setQueryData<User[] | undefined>(['users'], (old) => {
        if (!old) return old;
        return old.filter(u => u.id !== userId);
      });
    }
  });
}

export function useCreateUser() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: { name: string; email: string; password: string; role: User['role'] }) => {
      const token = await getToken();
      return createUser(token, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    }
  });
}

// React Query hooks for agency user management
export function useAgencyManagedUsers() {
  const { getToken } = useAuth();
  
  return useQuery({
    queryKey: ['agency-users'],
    queryFn: async () => {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }
      return getAgencyManagedUsers(token);
    },
  });
}

export function useCreateAgencyUser() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (payload: { name: string; email: string; password: string }) => {
      const token = await getToken();
      return createAgencyUser(token, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agency-users'] });
    }
  });
}

export function useUpdateAgencyUser() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, ...payload }: { userId: string; name?: string; email?: string; password?: string }) => {
      const token = await getToken();
      return updateAgencyUser(token, userId, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agency-users'] });
    }
  });
}

export function useDeleteAgencyUser() {
  const { getToken } = useAuth();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userId: string) => {
      const token = await getToken();
      return deleteAgencyUser(token, userId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agency-users'] });
    }
  });
}