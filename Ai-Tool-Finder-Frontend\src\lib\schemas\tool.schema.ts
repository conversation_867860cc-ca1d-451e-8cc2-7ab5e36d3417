import { z } from "zod";

export const TOOL_CATEGORIES = [
  "Image Generation",
  "Text Generation",
  "Code Generation",
  "Audio Generation",
  "Video Generation",
  "Data Analysis",
  "Chatbots",
  "Marketing",
  "Productivity",
  "Other",
] as const;

export const COMMON_TAGS = new Set([
  "AI",
  "Machine Learning",
  "GPT",
  "Neural Networks",
  "Deep Learning",
  "NLP",
  "Computer Vision",
  "Automation",
  "No-Code",
  "API",
]);

export const toolSchema = z.object({
  name: z.string().min(1, "Name is required"),
  slug: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  websiteUrl: z.string().url("Invalid URL"),
  category: z.enum(TOOL_CATEGORIES),
  tags: z.array(z.string()),
  pricing: z.object({
    type: z.enum(["free", "freemium", "paid", "enterprise"]),
    startingPrice: z.number().optional(),
  }),
  features: z.array(z.string()),
  logo: z.string().url("Invalid URL").optional(),
  status: z.enum(["draft", "published", "archived"]),
  isTrending: z.boolean().default(false),
  isNew: z.boolean().default(false),
  isUpcoming: z.boolean().default(false),
  isTopRated: z.boolean().default(false),
});

export type ToolFormData = z.infer<typeof toolSchema>; 