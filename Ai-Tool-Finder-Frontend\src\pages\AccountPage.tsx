import { useAuth } from "@/contexts/AuthContext";
import axios from 'axios';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

/**
 * Simple account settings page.
 * Shows logged-in user's profile information (avatar, name, email, role) with Tailwind styling.
 * Future enhancement: add forms to update profile / password etc.
 */
export default function AccountPage() {
  const { user, refreshUser } = useAuth();
  const [firstName, setFirstName] = useState(user?.firstName || '');
  const [username, setUsername] = useState(user?.username || '');

  // keep local state in sync when user loads or changes
  useEffect(() => {
    if (user) {
      setFirstName(user.firstName || '');
      setUsername(user.username || '');
    }
  }, [user]);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  if (!user) {
    return (
      <div className="min-h-[calc(100vh-4rem)] flex items-center justify-center">
        <span className="text-gray-500">Loading account…</span>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 pt-24 pb-10 px-4 min-h-screen">
      <div className="mx-auto w-full max-w-3xl rounded-lg bg-white p-8 shadow">
        <h1 className="mb-8 text-2xl font-bold tracking-tight text-gray-900">Account settings</h1>

        <div className="flex items-center space-x-6">
          <img
            src={user.imageUrl || `https://www.gravatar.com/avatar/${user.email}?d=identicon`}
            alt="Avatar"
            className="h-20 w-20 rounded-full object-cover"
          />
          <div>
            <p className="text-lg font-medium text-gray-900">
              {user.firstName || user.name || user.email?.split("@")[0]}
            </p>
            <p className="text-sm text-gray-500">{user.email}</p>
          </div>
        </div>

        {/* Profile information */}
        <div className="mt-10 border-t pt-8">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">Profile</h2>
          <form
            onSubmit={async (e) => {
              e.preventDefault();
              try {
                const res = await axios.put('/api/auth/me', { firstName, username });
                toast.success('Profile updated');
                await refreshUser();
              } catch (err: any) {
                toast.error(err.response?.data?.error || 'Failed to update profile');
              }
            }}
            className="grid grid-cols-1 gap-6 sm:grid-cols-2"
          >
            <div>
              <label className="block text-sm font-medium text-gray-700">Name</label>
              <input
                type="text"
                required
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 bg-gray-100  px-3 py-2 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Username</label>
              <input
                type="text"
                required
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="mt-1 block w-full rounded-md bg-gray-100 border-gray-300 px-3 py-2 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>
            <div className="sm:col-span-2">
              <button
                type="submit"
                className="inline-flex items-center rounded-md bg-emerald-600 px-4 py-2 text-sm font-medium text-white shadow hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500"
              >
                Save changes
              </button>
            </div>
          </form>
        </div>

        {/* Change password section */}
        <div className="mt-10 border-t pt-8">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">Change password</h2>
          <form
            onSubmit={async (e) => {
              e.preventDefault();
              if (newPassword !== confirmPassword) {
                toast.error('Passwords do not match');
                return;
              }
              try {
                await axios.post('/api/auth/change-password', { currentPassword, newPassword });
                toast.success('Password updated');
                setCurrentPassword('');
                setNewPassword('');
                setConfirmPassword('');
              } catch (err: any) {
                toast.error(err.response?.data?.error || 'Failed to update password');
              }
            }}
            className="grid grid-cols-1 gap-6 sm:grid-cols-2"
          >
            <div className="sm:col-span-2">
              <label className="block text-sm font-medium text-gray-700">Current password</label>
              <input
                type="password"
                required
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 px-3 py-2 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">New password</label>
              <input
                type="password"
                required
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 px-3 py-2 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Confirm new password</label>
              <input
                type="password"
                required
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 px-3 py-2 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>
            <div className="sm:col-span-2">
              <button
                type="submit"
                className="inline-flex items-center rounded-md bg-emerald-600 px-4 py-2 text-sm font-medium text-white shadow hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500"
              >
                Update password
              </button>
            </div>
          </form>
        </div>

        <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <label className="block text-sm font-medium text-gray-700">Full name</label>
            <input
              type="text"
              disabled
              value={user.firstName || user.name || user.email?.split("@")[0]}
              className="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 px-3 py-2 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Email address</label>
            <input
              type="email"
              disabled
              value={user.email}
              className="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 px-3 py-2 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
            />
          </div>
          {user.role && (
            <div>
              <label className="block text-sm font-medium text-gray-700">Role</label>
              <input
                type="text"
                disabled
                value={user.role}
                className="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 px-3 py-2 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
