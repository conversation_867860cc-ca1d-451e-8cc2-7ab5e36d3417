import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { useToolActions } from '@/hooks/useToolActions';
import { Tool } from '@/types/tool';
import axios from 'axios';
import {
  BookmarkIcon,
  LogOut,
  Settings,
  Star,
  ThumbsUp,
  <PERSON>ren<PERSON>U<PERSON>,
  User,
  Loader2,
  Users
} from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { AgencyUserManagement } from "@/components/AgencyUserManagement";
import { useAuth } from "@/contexts/AuthContext";

const API_URL = import.meta.env.VITE_API_URL || "https://api.aihunt.site";

export default function Dashboard() {
  const navigate = useNavigate();
  const { user, signOut, refreshUser } = useAuth();
  const { toast } = useToast();
  const [tools, setTools] = useState<Tool[]>([]);
  const [isLoadingTools, setIsLoadingTools] = useState(true);
  const [isLoadingPreferences, setIsLoadingPreferences] = useState(true);
  const [activeTab, setActiveTab] = useState(user?.role === 'agency' ? "team" : "saved");
  const [savedPageSize, setSavedPageSize] = useState(6);
  const [upvotedPageSize, setUpvotedPageSize] = useState(6);
  const [isSubmitModalOpen, setIsSubmitModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Avatar generator state
  const [selectedStyle, setSelectedStyle] = useState("avataaars");
  const [seed, setSeed] = useState(user?.id || "random");
  
  // User preferences state
  const [userSavedTools, setUserSavedTools] = useState<string[]>([]);
  const [userUpvotedTools, setUserUpvotedTools] = useState<string[]>([]);
  // Saved and upvoted tools from unified hook (localStorage + live state)
  const { savedTools, upvotedTools, loadUserPreferences } = useToolActions();

  // Submit form state
  const [submitForm, setSubmitForm] = useState<{
    name: string;
    description: string;
    website: string;
    category: string;
  }>({
    name: "",
    description: "",
    website: "",
    category: "",
  });
  
  // Get the actual tool objects for saved and upvoted tools
  const savedToolObjects = tools.filter(tool => {
    const identifier = tool.id || tool._id || (tool as any).slug;
    return savedTools.includes(identifier);
  });
  const upvotedToolObjects = tools.filter(tool => {
    const identifier = tool.id || tool._id || (tool as any).slug;
    return upvotedTools.includes(identifier);
  });

  // Avatar styles
  const avatarStyles = [
    { value: "avataaars", label: "Default" },
    { value: "pixel-art", label: "Pixel Art" },
    { value: "bottts", label: "Robots" },
    { value: "initials", label: "Initials" },
    { value: "fun-emoji", label: "Fun Emoji" },
  ];

  const getAvatarUrl = (style: string, seed: string) => {
    return `https://api.dicebear.com/7.x/${style}/svg?seed=${seed}`;
  };

  const updateProfileImage = async (style: string, seed: string) => {
    if (!user) return;
    
    setIsUpdating(true);
    const avatarUrl = getAvatarUrl(style, seed);
    
    try {
      // Fetch the SVG content
      const response = await fetch(avatarUrl);
      if (!response.ok) throw new Error('Failed to fetch avatar');
      
      // Convert SVG to PNG using canvas
      const svgBlob = await response.blob();
      const img = new Image();
      const svgUrl = URL.createObjectURL(svgBlob);
      
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = svgUrl;
      });

      const canvas = document.createElement('canvas');
      canvas.width = 400;  // Set size for good quality
      canvas.height = 400;
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Failed to get canvas context');
      
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      
      // Convert canvas to blob
      const pngBlob = await new Promise<Blob>((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (blob) resolve(blob);
          else reject(new Error('Failed to convert to PNG'));
        }, 'image/png');
      });
      
      // Create file from blob
      const file = new File([pngBlob], 'avatar.png', { type: 'image/png' });
      
      // Update user's profile image using Clerk
      await axios.put('/api/auth/me', { profileImageUrl: avatarUrl });
      await refreshUser();
      
      // Cleanup
      URL.revokeObjectURL(svgUrl);
      
      toast({
        title: "Success",
        description: "Profile picture updated successfully!",
        variant: "default",
      });
    } catch (error) {
      console.error('Error updating profile image:', error);
      toast({
        title: "Error",
        description: "Failed to update profile picture. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Update stats to be more dynamic
  const stats = [
    { label: "Saved Tools", value: savedToolObjects.length, icon: BookmarkIcon, color: "text-green-600", bgColor: "bg-green-100" },
    { label: "Upvoted", value: upvotedToolObjects.length, icon: ThumbsUp, color: "text-green-600", bgColor: "bg-green-100" },
  ];

  useEffect(() => {
    // Load tools and user preferences when the user is authenticated
    if (user) {
      fetchTools();
      
      // Set active tab based on user role
      setActiveTab(user.role === 'agency' ? "team" : "saved");
      
      // loadUserPreferences is now handled by the useToolActions hook
      console.log('Dashboard: User authenticated, preferences will be loaded via useToolActions hook');
    } else {
      console.log('Dashboard: User not authenticated, cannot load preferences');
      setIsLoadingPreferences(false);
    }
  }, [user]);

  // Listen for changes in the savedTools and upvotedTools from useToolActions
  useEffect(() => {
    setIsLoadingPreferences(false);
    console.log('Dashboard: User preferences updated from useToolActions hook', {
      savedTools: savedTools.length,
      upvotedTools: upvotedTools.length
    });
    
    // Refresh tools list when preferences change to ensure we have the latest data
    if (savedTools.length > 0 || upvotedTools.length > 0) {
      fetchTools();
    }
  }, [savedTools, upvotedTools]);

  // Add event listener for tool vote/save changes
  useEffect(() => {
    const handleToolUpdate = () => {
      console.log('Dashboard: Detected tool update, refreshing preferences');
      loadUserPreferences();
    };
    
    window.addEventListener('toolVotesUpdated', handleToolUpdate);
    window.addEventListener('savedToolsUpdated', handleToolUpdate);
    
    return () => {
      window.removeEventListener('toolVotesUpdated', handleToolUpdate);
      window.removeEventListener('savedToolsUpdated', handleToolUpdate);
    };
  }, [loadUserPreferences]);

  const handleSignOut = async () => {
    await signOut();
    navigate("/");
  };

  const handleSubmitTool = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Check if user is authenticated
    if (!user) {
      toast({
        title: "Unauthorized",
        description: "You must be logged in to submit a tool",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);
    try {
      // In a real implementation, we would use the API client to submit the tool
      // For now, we're just simulating the submission with a timeout
      // In a real app, you would use something like:
      
      // TODO: Replace this simulation with actual API call when backend is ready
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // const { useSubmitTool } = await import("@/lib/api/submissions");
      // const submitToolMutation = useSubmitTool();
      // await submitToolMutation.mutateAsync(submitForm);
      
      toast({
        title: "Success",
        description: "Tool submitted successfully!",
        variant: "default",
      });
      setIsSubmitModalOpen(false);
      setSubmitForm({
        name: "",
        description: "",
        website: "",
        category: "",
      });
    } catch (error) {
      console.error('Tool submission error:', error);
      let errorMessage = 'Failed to submit tool';
      
      // Handle authentication errors specifically
      if (error instanceof Error) {
        if (error.message.includes('Authentication required') || 
            error.message.includes('Unauthorized')) {
          errorMessage = 'You must be logged in to submit a tool';
        } else {
          errorMessage = error.message;
        }
      }
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add a utility function to get the correct image URL
  const getToolImageUrl = (tool: Tool) => {
    // Try different properties that might contain the image
    return tool.logo || 
           tool.websiteUrl && `https://www.google.com/s2/favicons?domain=${tool.websiteUrl}&sz=128` || 
           `https://ui-avatars.com/api/?name=${encodeURIComponent(tool.name)}`;
  };
  
  // Fetch all tools
  const fetchTools = async () => {
    try {
      setIsLoadingTools(true);
      console.log('Dashboard: Fetching tools from API');
      const response = await fetch(`${API_URL}/api/tools`);
      if (response.ok) {
        const data = await response.json();
        console.log(`Dashboard: Fetched ${data.length} tools successfully`);
        setTools(data);
      } else {
        console.error('Error fetching tools: Server returned', response.status);
      }
    } catch (error) {
      console.error('Error fetching tools:', error);
    } finally {
      setIsLoadingTools(false);
    }
  };

  // Initial data loading when component mounts or user changes
  useEffect(() => {
    if (user) {
      console.log('Dashboard: User authenticated, fetching tools');
      fetchTools();
    } else {
      console.log('Dashboard: No user, skipping tool fetch');
    }
  }, [user]);

  return (
    <div className="container mx-auto px-4 py-8 mt-20">
      {/* Submit Tool Modal */}
      <Dialog 
        open={isSubmitModalOpen && !!user} 
        onOpenChange={(open) => {
          // Only allow opening if user is logged in
          if (open && !user) {
            toast({
              title: "Authentication Required",
              description: "Please log in to submit a new tool",
              variant: "destructive",
            });
            return;
          }
          setIsSubmitModalOpen(open);
        }}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Submit New Tool</DialogTitle>
            <DialogDescription>
              Submit a new AI tool to share with the community.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitTool} className="space-y-6 mt-4">
            <div className="space-y-2">
              <Label htmlFor="name">Tool Name</Label>
              <Input
                id="name"
                value={submitForm.name}
                onChange={(e) => setSubmitForm({ ...submitForm, name: e.target.value })}
                placeholder="Enter tool name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={submitForm.description}
                onChange={(e) => setSubmitForm({ ...submitForm, description: e.target.value })}
                placeholder="Describe what the tool does"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="website">Website URL</Label>
              <Input
                id="website"
                type="url"
                value={submitForm.website}
                onChange={(e) => setSubmitForm({ ...submitForm, website: e.target.value })}
                placeholder="https://example.com"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={submitForm.category}
                onValueChange={(value) => setSubmitForm({ ...submitForm, category: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="chatbots">Chatbots</SelectItem>
                  <SelectItem value="image-generation">Image Generation</SelectItem>
                  <SelectItem value="video-editing">Video Editing</SelectItem>
                  <SelectItem value="writing">Writing</SelectItem>
                  <SelectItem value="development">Development</SelectItem>
                  <SelectItem value="productivity">Productivity</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end gap-4 mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsSubmitModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-gradient-to-r from-green-600 to-green-500 text-white hover:from-green-700 hover:to-green-600"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Submitting...
                  </div>
                ) : (
                  "Submit Tool"
                )}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Header with User Info */}
      <div className="flex flex-col md:flex-row items-center md:items-start gap-8 mb-12">
        <div className="flex-shrink-0">
          <div className="w-32 h-32 rounded-2xl overflow-hidden border-4 border-green-100 shadow-xl">
            {user?.imageUrl ? (
              <img
                src={user.profileImageUrl}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : (
              <img
                src={getAvatarUrl(selectedStyle, seed)}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            )}
          </div>
        </div>
        <div className="flex-grow text-center md:text-left">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-green-500 bg-clip-text text-transparent mb-2">
            Welcome back, {user?.firstName || user?.username || "User"}!
          </h1>
          <p className="text-gray-600 mb-6">
            {user?.emailAddresses?.[0]?.emailAddress || ""}
          </p>
          <div className="flex flex-wrap gap-4 justify-center md:justify-start">
            <Button 
              variant="outline" 
              className="gap-2 border-green-200 hover:border-green-300 text-green-600 hover:text-green-700"
              onClick={() => setActiveTab("settings")}
            >
              <Settings className="w-4 h-4" />
              Edit Profile
            </Button>
            <Button 
              variant="outline" 
              className="gap-2 border-red-200 hover:border-red-300 text-red-600 hover:text-red-700"
              onClick={handleSignOut}
            >
              <LogOut className="w-4 h-4" />
              Sign Out
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 rounded-xl bg-green-100">
                <BookmarkIcon className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Saved Tools</p>
                <h3 className="text-2xl font-bold text-gray-900">{savedToolObjects.length}</h3>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 rounded-xl bg-green-100">
                <ThumbsUp className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Upvoted</p>
                <h3 className="text-2xl font-bold text-gray-900">{upvotedToolObjects.length}</h3>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Left Column: Tabs Content */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
            <TabsList className={`grid ${user?.role === 'agency' ? 'grid-cols-4' : 'grid-cols-3'} gap-4 bg-transparent h-auto p-0`}>
              <TabsTrigger
                value="saved"
                className="data-[state=active]:bg-green-100 data-[state=active]:text-green-700 h-24 rounded-xl border border-gray-200 hover:border-green-200 transition-all"
              >
                <div className="flex flex-col items-center gap-2">
                  <BookmarkIcon className="w-6 h-6" />
                  <span>Saved Tools</span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="upvoted"
                className="data-[state=active]:bg-green-100 data-[state=active]:text-green-700 h-24 rounded-xl border border-gray-200 hover:border-green-200 transition-all"
              >
                <div className="flex flex-col items-center gap-2">
                  <ThumbsUp className="w-6 h-6" />
                  <span>Upvoted</span>
                </div>
              </TabsTrigger>
              {user?.role === 'agency' && (
                <TabsTrigger
                  value="team"
                  className="data-[state=active]:bg-green-100 data-[state=active]:text-green-700 h-24 rounded-xl border border-gray-200 hover:border-green-200 transition-all"
                >
                  <div className="flex flex-col items-center gap-2">
                    <Users className="w-6 h-6" />
                    <span>Team</span>
                  </div>
                </TabsTrigger>
              )}
              <TabsTrigger
                value="settings"
                className="data-[state=active]:bg-green-100 data-[state=active]:text-green-700 h-24 rounded-xl border border-gray-200 hover:border-green-200 transition-all"
              >
                <div className="flex flex-col items-center gap-2">
                  <Settings className="w-6 h-6" />
                  <span>Settings</span>
                </div>
              </TabsTrigger>
            </TabsList>

            {/* Saved Tools */}
            <TabsContent value="saved" className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-semibold text-gray-900">Saved Tools</h2>
                <span className="text-sm text-gray-500">Showing {Math.min(savedToolObjects.length, savedPageSize)} of {savedToolObjects.length}</span>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {isLoadingTools || isLoadingPreferences ? (
                  <div className="col-span-full flex flex-col items-center justify-center py-12">
                    <Loader2 className="h-8 w-8 text-green-500 animate-spin mb-4" />
                    <p className="text-gray-500">Loading your saved tools...</p>
                  </div>
                ) : savedToolObjects.length > 0 ? (
                  savedToolObjects.slice(0, savedPageSize).map((tool) => (
                    <Link 
                      to={`/ai-tools/${tool.slug || tool.id}`}
                      key={tool.id}
                      className="group relative bg-white rounded-3xl p-6 shadow-sm ring-1 ring-black/[0.08] transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                    >
                      <div className="flex items-start gap-4">
                        <div className="w-16 h-16 rounded-2xl overflow-hidden bg-gradient-to-br from-green-50 to-green-100/50 flex-shrink-0">
                          <img
                            src={getToolImageUrl(tool)}
                            alt={tool.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-gray-900 text-lg mb-1 truncate">
                            {tool.name}
                          </h3>
                          <p className="text-sm text-gray-600 line-clamp-2">
                            {tool.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 mt-4">
                        <Badge variant="secondary" className="bg-green-50 text-green-700 hover:bg-green-100">
                          {tool.category}
                        </Badge>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Star className="w-4 h-4 text-yellow-400" />
                          {tool.rating}
                        </div>
                      </div>
                    </Link>
                  ))
                ) : (
                  <div className="col-span-full text-center py-8">
                    <BookmarkIcon className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                    <p className="text-gray-500">No saved tools yet</p>
                    <p className="text-sm text-gray-400 mt-2">Save tools you like by clicking the bookmark icon on tool pages</p>
                  </div>
                )}
              </div>
              
              {/* Load More button for saved tools */}
              {savedToolObjects.length > savedPageSize && (
                <div className="flex justify-center mt-6">
                  <Button
                    variant="outline"
                    className="border-green-200 hover:border-green-300 text-green-700"
                    onClick={() => setSavedPageSize(prev => prev + 6)}
                  >
                    Load More
                  </Button>
                </div>
              )}
            </TabsContent>

            {/* Upvoted Tools */}
            <TabsContent value="upvoted" className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-semibold text-gray-900">Upvoted Tools</h2>
                <span className="text-sm text-gray-500">Showing {Math.min(upvotedToolObjects.length, upvotedPageSize)} of {upvotedToolObjects.length}</span>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {isLoadingTools || isLoadingPreferences ? (
                  <div className="col-span-full flex flex-col items-center justify-center py-12">
                    <Loader2 className="h-8 w-8 text-green-500 animate-spin mb-4" />
                    <p className="text-gray-500">Loading your upvoted tools...</p>
                  </div>
                ) : upvotedToolObjects.length > 0 ? (
                  upvotedToolObjects.slice(0, upvotedPageSize).map((tool) => (
                    <Link 
                      to={`/ai-tools/${tool.slug || tool.id}`}
                      key={tool.id}
                      className="group relative bg-white rounded-3xl p-6 shadow-sm ring-1 ring-black/[0.08] transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                    >
                      <div className="flex items-start gap-4">
                        <div className="w-16 h-16 rounded-2xl overflow-hidden bg-gradient-to-br from-green-50 to-green-100/50 flex-shrink-0">
                          <img
                            src={getToolImageUrl(tool)}
                            alt={tool.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-gray-900 text-lg mb-1 truncate">
                            {tool.name}
                          </h3>
                          <p className="text-sm text-gray-600 line-clamp-2">
                            {tool.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between mt-4">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="bg-green-50 text-green-700 hover:bg-green-100">
                            {tool.category}
                          </Badge>
                          <div className="flex items-center gap-1 text-sm text-gray-500">
                            <Star className="w-4 h-4 text-yellow-400" />
                            {tool.rating}
                          </div>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-green-600 font-medium">
                          <ThumbsUp className="w-4 h-4" />
                          {tool.votes}
                        </div>
                      </div>
                    </Link>
                  ))
                ) : (
                  <div className="col-span-full text-center py-8">
                    <ThumbsUp className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                    <p className="text-gray-500">No upvoted tools yet</p>
                    <p className="text-sm text-gray-400 mt-2">Upvote tools you like by clicking the upvote button on tool pages</p>
                  </div>
                )}
              </div>
              
              {/* Load More button for upvoted tools */}
              {upvotedToolObjects.length > upvotedPageSize && (
                <div className="flex justify-center mt-6">
                  <Button
                    variant="outline"
                    className="border-green-200 hover:border-green-300 text-green-700"
                    onClick={() => setUpvotedPageSize(prev => prev + 6)}
                  >
                    Load More
                  </Button>
                </div>
              )}
            </TabsContent>

            {/* Team Management */}
            {user?.role === 'agency' && (
              <TabsContent value="team" className="space-y-6">
                <AgencyUserManagement />
              </TabsContent>
            )}

            {/* Settings */}
            <TabsContent value="settings" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Profile Settings</CardTitle>
                  <CardDescription>
                    Manage your profile and account settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Avatar Generator Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium flex items-center gap-2">
                      <User className="w-5 h-5" />
                      Avatar Generator
                    </h3>
                    <div className="flex items-start gap-8">
                      <div className="flex-shrink-0">
                        <div className="w-24 h-24 rounded-xl overflow-hidden border-2 border-green-100">
                          <img
                            src={getAvatarUrl(selectedStyle, seed)}
                            alt="Selected Avatar"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                      <div className="flex-grow space-y-4">
                        <div className="space-y-2">
                          <Label>Avatar Style</Label>
                          <Select
                            value={selectedStyle}
                            onValueChange={setSelectedStyle}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select style" />
                            </SelectTrigger>
                            <SelectContent>
                              {avatarStyles.map((style) => (
                                <SelectItem key={style.value} value={style.value}>
                                  {style.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>Randomize</Label>
                          <div className="flex gap-2">
                            <Input
                              value={seed}
                              onChange={(e) => setSeed(e.target.value)}
                              placeholder="Enter text to generate avatar"
                              className="flex-grow"
                            />
                            <Button
                              variant="outline"
                              onClick={() => setSeed(Math.random().toString())}
                              className="flex-shrink-0"
                            >
                              Randomize
                            </Button>
                          </div>
                        </div>
                        <Button
                          onClick={() => updateProfileImage(selectedStyle, seed)}
                          className="w-full"
                          disabled={isUpdating}
                        >
                          {isUpdating ? (
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                              Updating...
                            </div>
                          ) : (
                            "Update Profile Picture"
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Existing Profile Information */}
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Name</Label>
                        <div className="flex gap-2">
                          <Input id="name" value={user?.firstName || ""} readOnly />
                          <Button 
                            type="button" 
                            variant="outline"
                            size="sm"
                            onClick={() => navigate('/account')}
                          >
                            Edit
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input id="email" value={user?.email || ""} readOnly />
                      </div>
                    </div>
                    <div className="space-y-2 mt-4">
                      <Label htmlFor="username">Username</Label>
                      <div className="flex gap-2">
                        <Input id="username" value={user?.username || ""} readOnly />
                        <Button 
                          type="button" 
                          variant="outline"
                          size="sm"
                          onClick={() => navigate('/account')}
                        >
                          Edit
                        </Button>
                      </div>
                    </div>
                    <div className="flex gap-4 mt-6">
                      <Button
                        variant="outline"
                        onClick={() => navigate('/account')}
                      >
                        Account Settings
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={handleSignOut}
                      >
                        Sign Out
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column: Activity Feed */}
        <div className="space-y-6">
          {/* Trending Tools */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-green-600" />
                Trending Tools
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isLoadingTools ? (
                  <div className="flex justify-center py-4">
                    <div className="w-8 h-8 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
                  </div>
                ) : (
                  tools
                    .filter(tool => tool.isTrending)
                    .slice(0, 3)
                    .map((tool, index) => (
                      <div key={tool.id || tool._id} className="flex items-center gap-4 p-3 rounded-xl hover:bg-gray-50 transition-colors">
                        <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <Star className="w-5 h-5 text-green-600" />
                        </div>
                        <div className="flex-grow min-w-0">
                          <p className="font-medium text-gray-900 truncate">{tool.name}</p>
                          <p className="text-sm text-gray-500">{tool.category}</p>
                        </div>
                        <div className="flex items-center gap-1 text-green-600">
                          <ThumbsUp className="w-4 h-4" />
                          <span className="text-sm font-medium">{tool.votes}</span>
                        </div>
                      </div>
                    ))
                )}
                {!isLoadingTools && tools.filter(tool => tool.isTrending).length === 0 && (
                  <div className="text-center py-4">
                    <p className="text-gray-500">No trending tools available</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}