import React, { useState } from 'react';
import { Link, Navigate, useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ROLE_LABELS: Record<string, { title: string; subtitle: string }> = {
  basic: {
    title: 'Create a free account',
    subtitle: 'Upgrade anytime to unlock more tools',
  },
  unlimited: {
    title: 'Unlimited Plan sign-up',
    subtitle: 'Unlock unlimited submissions & premium placement',
  },
  agency: {
    title: 'Agency Plan sign-up',
    subtitle: 'Manage multiple projects for clients with advanced features',
  },
  business: {
    title: 'Business Plan sign-up',
    subtitle: 'Advanced analytics & team workflows for growing companies',
  },
};

export default function SignupPage() {
  const { signup } = useAuth();
  const navigate = useNavigate();
  const { role = 'basic' } = useParams<{ role: string }>();

  if (!ROLE_LABELS[role]) {
    return <Navigate to="/signup/basic" replace />;
  }

  const { title, subtitle } = ROLE_LABELS[role];

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirm, setConfirm] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (password !== confirm) {
      setError('Passwords do not match');
      return;
    }
    try {
      await signup(email, password, role);
      setSuccess('Account created! Redirecting...');
      setTimeout(() => navigate('/dashboard'), 1500);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Signup failed');
    }
  };

  return (
    <div className="min-h-[calc(100vh-4rem)] flex items-center justify-center pt-20 bg-gray-50 px-4 py-12">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
          <p className="mt-2 text-sm text-gray-600">{subtitle}</p>
          <p className="mt-2 text-sm text-gray-600">
            Or
            <Link to="/login" className="font-medium text-emerald-600 hover:text-emerald-500 ml-1">
              sign in to your account
            </Link>
          </p>
        </div>
        <div className="rounded-lg bg-white p-8 shadow">
          {error && <p className="mb-4 text-sm text-red-600">{error}</p>}
          {success && <p className="mb-4 text-sm text-emerald-600">{success}</p>}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email address</label>
              <input
                id="email"
                type="email"
                autoComplete="email"
                required
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-emerald-500 focus:outline-none focus:ring-emerald-500"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">Password</label>
              <input
                id="password"
                type="password"
                autoComplete="new-password"
                required
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-emerald-500 focus:outline-none focus:ring-emerald-500"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="confirm" className="block text-sm font-medium text-gray-700">Confirm password</label>
              <input
                id="confirm"
                type="password"
                autoComplete="new-password"
                required
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-emerald-500 focus:outline-none focus:ring-emerald-500"
                value={confirm}
                onChange={(e) => setConfirm(e.target.value)}
              />
            </div>
            <button
              type="submit"
              className="flex w-full justify-center rounded-md bg-emerald-600 py-2 px-4 text-sm font-medium text-white shadow hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
            >
              Sign up
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
