import { useState } from "react";
import { Tool } from "@/types/tool";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { 
  Plus, 
  MoreHorizontal, 
  Pencil, 
  Trash2, 
  Eye, 
  Archive,
  Search,
  ExternalLink,
  Tag,
  DollarSign,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { ToolFormDialog } from "@/components/admin/tools/ToolFormDialog";
import { useTools, useCreateTool, useUpdateTool, useDeleteTool, useUpdateToolStatus } from "@/lib/api/tools";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

export default function ToolsManagementPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [formOpen, setFormOpen] = useState(false);
  const [selectedTool, setSelectedTool] = useState<Tool | undefined>();

  // Fetch tools
  const { data: tools = [], isLoading, error } = useTools();

  // Mutations
  const createToolMutation = useCreateTool();
  const updateToolMutation = useUpdateTool();
  const deleteToolMutation = useDeleteTool();
  const updateStatusMutation = useUpdateToolStatus();

  if (error) {
    console.error('Error fetching tools:', error);
    toast.error('Failed to load tools');
  }

  const filteredTools = tools.filter(tool =>
    tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tool.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tool.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSubmit = async (data: Omit<Tool, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      if (selectedTool) {
        // Update existing tool
        await updateToolMutation.mutateAsync({ 
          toolId: selectedTool.id, 
          data 
        });
        toast.success("Tool updated successfully");
      } else {
        // Create new tool
        await createToolMutation.mutateAsync(data);
        toast.success("Tool created successfully");
      }
      setFormOpen(false);
      setSelectedTool(undefined);
    } catch (error) {
      console.error('Error saving tool:', error);
      toast.error("Failed to save tool");
    }
  };

  const handleEdit = (tool: Tool) => {
    setSelectedTool(tool);
    setFormOpen(true);
  };

  const handleDelete = async (tool: Tool) => {
    if (window.confirm(`Are you sure you want to delete ${tool.name}?`)) {
      try {
        await deleteToolMutation.mutateAsync(tool.id);
        toast.success("Tool deleted successfully");
      } catch (error) {
        console.error('Error deleting tool:', error);
        toast.error("Failed to delete tool");
      }
    }
  };

  const handleArchive = async (tool: Tool) => {
    try {
      await updateStatusMutation.mutateAsync({
        toolId: tool.id,
        status: 'archived'
      });
      toast.success("Tool archived successfully");
    } catch (error) {
      console.error('Error archiving tool:', error);
      toast.error("Failed to archive tool");
    }
  };

  const getStatusColor = (status: Tool['status']) => {
    switch (status) {
      case 'published':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'draft':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'archived':
        return 'bg-gray-50 text-gray-700 border-gray-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  const getPricingColor = (type: Tool['pricing']['type']) => {
    switch (type) {
      case 'free':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'freemium':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'paid':
        return 'bg-purple-50 text-purple-700 border-purple-200';
      case 'enterprise':
        return 'bg-orange-50 text-orange-700 border-orange-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600" />
      </div>
    );
  }

  return (
    <div>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-semibold tracking-tight">Tools Management</h2>
            <p className="text-sm text-gray-500">
              Manage AI tools in the directory
            </p>
          </div>
          <Button onClick={() => {
            setSelectedTool(undefined);
            setFormOpen(true);
          }}>
            <Plus className="mr-2 h-4 w-4" />
            Add New Tool
          </Button>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 flex-1 max-w-md">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search tools..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>
        </div>

        <div className="bg-white shadow-sm rounded-lg border overflow-hidden">
          <div className="h-[600px] overflow-auto">
            <Table>
              <TableHeader className="sticky top-0 bg-white z-10">
                <TableRow>
                  <TableHead>Tool</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Pricing</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Updated</TableHead>
                  <TableHead className="w-[70px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTools.map((tool) => (
                  <TableRow key={tool.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-lg overflow-hidden bg-gray-100">
                          <img
                            src={tool.logo || `https://ui-avatars.com/api/?name=${encodeURIComponent(tool.name)}`}
                            alt={tool.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <div className="font-medium">{tool.name}</div>
                          <div className="text-sm text-gray-500 truncate max-w-[300px]">
                            {tool.description}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="bg-gray-100">
                        {tool.category}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={cn("capitalize", getPricingColor(tool.pricing.type))}>
                        {tool.pricing.type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={cn("capitalize", getStatusColor(tool.status))}>
                        {tool.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-gray-500">
                        {new Date(tool.updatedAt).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEdit(tool)}>
                            <Pencil className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => window.open(`/ai-tools/${tool.slug}`, '_blank')}>
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleArchive(tool)}>
                            <Archive className="mr-2 h-4 w-4" />
                            Archive
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDelete(tool)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      <ToolFormDialog
        open={formOpen}
        onOpenChange={setFormOpen}
        onSubmit={handleSubmit}
        initialData={selectedTool}
      />
    </div>
  );
} 