import { Megaphone, Target, BarChart, Users, Mail, ArrowRight, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ContactSalesModal } from "@/components/modals/ContactSalesModal";
import { useState } from "react";
import { useUser } from "@clerk/clerk-react";
import { useToast } from "@/components/ui/use-toast";

interface PricingTier {
  name: string;
  price: string;
  description: string;
  features: string[];
  highlight?: boolean;
}

export const Advertise = () => {
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);
  const { isSignedIn } = useUser();
  const { toast } = useToast();

  const handleContactClick = () => {
    if (!isSignedIn) {
      toast({
        title: "Authentication Required",
        description: "Please log in to contact sales",
        variant: "destructive",
      });
      return;
    }
    setIsContactModalOpen(true);
  };

  const pricingTiers: PricingTier[] = [
    {
      name: "Basic",
      price: "$99",
      description: "Perfect for startups and small AI tools",
      features: [
        "Featured listing for 7 days",
        "Social media promotion",
        "Basic analytics",
        "Email support"
      ]
    },
    {
      name: "Professional",
      price: "$299",
      description: "Ideal for growing AI companies",
      features: [
        "Featured listing for 30 days",
        "Premium placement",
        "Social media campaign",
        "Detailed analytics",
        "Newsletter feature",
        "Priority support"
      ],
      highlight: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      description: "For established AI businesses",
      features: [
        "Custom duration",
        "Premium placement",
        "Comprehensive marketing",
        "Advanced analytics",
        "Custom integrations",
        "Dedicated support"
      ]
    }
  ];

  return (
    <div className="relative">
      <div className="container mx-auto px-4 py-8 mt-20">
        {/* Header */}
        <div className="flex flex-col items-center text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-green-50 mb-4">
            <Megaphone className="w-8 h-8 text-green-500" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-green-500 to-green-600 bg-clip-text text-transparent mb-4">
            Advertise Your AI Tool
          </h1>
          <p className="text-gray-600 max-w-2xl">
            Reach thousands of AI enthusiasts, developers, and businesses looking for the latest AI tools and solutions.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white rounded-2xl p-6 border border-gray-100 hover:border-green-100 transition-all duration-300 hover:shadow-lg text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-green-50 text-green-500 mb-4">
              <Users className="w-6 h-6" />
            </div>
            <div className="text-3xl font-bold text-gray-900 mb-2">50K+</div>
            <div className="text-gray-600">Monthly Visitors</div>
          </div>
          <div className="bg-white rounded-2xl p-6 border border-gray-100 hover:border-green-100 transition-all duration-300 hover:shadow-lg text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-green-50 text-green-500 mb-4">
              <Target className="w-6 h-6" />
            </div>
            <div className="text-3xl font-bold text-gray-900 mb-2">85%</div>
            <div className="text-gray-600">Tech Decision Makers</div>
          </div>
          <div className="bg-white rounded-2xl p-6 border border-gray-100 hover:border-green-100 transition-all duration-300 hover:shadow-lg text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-green-50 text-green-500 mb-4">
              <BarChart className="w-6 h-6" />
            </div>
            <div className="text-3xl font-bold text-gray-900 mb-2">3.5x</div>
            <div className="text-gray-600">Avg. ROI</div>
          </div>
        </div>

        {/* Pricing */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Choose Your Plan
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {pricingTiers.map((tier) => (
              <div 
                key={tier.name}
                className={`
                  relative bg-white rounded-2xl p-8 border transition-all duration-300
                  ${tier.highlight 
                    ? 'border-green-100 shadow-xl scale-105' 
                    : 'border-gray-100 hover:border-green-100 hover:shadow-lg'
                  }
                `}
              >
                {tier.highlight && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-1 rounded-full text-sm">
                      Most Popular
                    </span>
                  </div>
                )}
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {tier.name}
                </h3>
                <div className="flex items-baseline gap-1 mb-4">
                  <span className="text-4xl font-bold text-gray-900">{tier.price}</span>
                  {tier.price !== "Custom" && <span className="text-gray-500">/month</span>}
                </div>
                <p className="text-gray-600 mb-6">
                  {tier.description}
                </p>
                <ul className="space-y-4 mb-8">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex items-center gap-3 text-gray-600">
                      <Check className="w-5 h-5 text-green-500" />
                      {feature}
                    </li>
                  ))}
                </ul>
                <Button 
                  className={`
                    w-full rounded-xl
                    ${tier.highlight
                      ? 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700'
                      : 'bg-white border-2 border-green-100 text-green-500 hover:bg-green-50'
                    }
                  `}
                  onClick={handleContactClick}
                >
                  Get Started
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Contact - Separate from main container */}
      <div className="relative z-10 bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center bg-white rounded-2xl p-8 border border-gray-100 hover:border-green-100 transition-all duration-300 hover:shadow-lg">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Need a Custom Solution?
            </h2>
            <p className="text-gray-600 mb-8">
              Contact us to discuss custom advertising packages tailored to your specific needs.
            </p>
            <Button 
              className="bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 rounded-xl px-8 py-6 h-auto font-medium text-base relative z-20"
              onClick={handleContactClick}
            >
              <Mail className="w-5 h-5 mr-3" />
              Contact Sales
            </Button>
          </div>
        </div>
      </div>

      {/* Contact Sales Modal */}
      <ContactSalesModal 
        isOpen={isContactModalOpen}
        onOpenChange={setIsContactModalOpen}
      />
    </div>
  );
}; 