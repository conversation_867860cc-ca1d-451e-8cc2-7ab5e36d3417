import React, { useState, useEffect } from "react";
import { Helmet } from "react-helmet";
import { motion } from "framer-motion";
import {
  Check,
  Star,
  ChevronRight,
  Users,
  BarChart2,
  Mail,
  Phone,
  MessageSquare,
  ArrowRight,
  X,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Link } from "react-router-dom";

interface CRMTool {
  id: string;
  name: string;
  logo: string;
  description: string;
  rating: number;
  bestFor: string[];
  pros: string[];
  cons: string[];
  pricing: {
    free: boolean;
    startingPrice: string;
  };
  url: string;
  features: {
    aiAssistant: boolean;
    contactManagement: boolean;
    salesAutomation: boolean;
    emailIntegration: boolean;
    reporting: boolean;
  };
}

const toolsList: CRMTool[] = [
  {
    id: "1",
    name: "HubSpot",
    logo: "https://logo.clearbit.com/hubspot.com",
    description: "All-in-one CRM platform with powerful marketing, sales, and service tools enhanced by AI capabilities.",
    rating: 4.8,
    bestFor: ["Marketing teams", "Growing businesses", "Sales teams"],
    pros: ["Generous free plan", "User-friendly interface", "Comprehensive toolset", "Great automation"],
    cons: ["Paid plans are expensive", "Can be complex", "Advanced features have steep learning curve"],
    pricing: {
      free: true,
      startingPrice: "$20/month"
    },
    url: "https://hubspot.com",
    features: {
      aiAssistant: true,
      contactManagement: true,
      salesAutomation: true,
      emailIntegration: true,
      reporting: true
    }
  },
  {
    id: "2",
    name: "Salesforce",
    logo: "https://logo.clearbit.com/salesforce.com",
    description: "Enterprise-grade CRM with Einstein AI to help teams sell smarter and provide better customer service.",
    rating: 4.7,
    bestFor: ["Enterprise organizations", "Complex sales processes", "Multi-department teams"],
    pros: ["Extremely customizable", "Robust reporting", "AI-powered insights", "Vast app ecosystem"],
    cons: ["Expensive", "Complex to implement", "Requires dedicated admin", "Steep learning curve"],
    pricing: {
      free: false,
      startingPrice: "$25/user/month"
    },
    url: "https://salesforce.com",
    features: {
      aiAssistant: true,
      contactManagement: true,
      salesAutomation: true,
      emailIntegration: true,
      reporting: true
    }
  },
  {
    id: "3",
    name: "Pipedrive",
    logo: "https://logo.clearbit.com/pipedrive.com",
    description: "Sales-focused CRM with AI sales assistant designed to visualize sales pipelines and close more deals.",
    rating: 4.6,
    bestFor: ["Sales-focused teams", "Visual pipeline users", "Small to mid-sized businesses"],
    pros: ["Visual pipeline view", "Easy to use", "Great mobile app", "Activity-based approach"],
    cons: ["Limited marketing features", "Basic reporting", "Limited customization", "AI features limited to higher tiers"],
    pricing: {
      free: false,
      startingPrice: "$15/user/month"
    },
    url: "https://pipedrive.com",
    features: {
      aiAssistant: true,
      contactManagement: true,
      salesAutomation: true,
      emailIntegration: true,
      reporting: true
    }
  },
  {
    id: "4",
    name: "Monday Sales CRM",
    logo: "https://logo.clearbit.com/monday.com",
    description: "Visual CRM platform that combines sales, marketing, and customer management in a customizable workspace.",
    rating: 4.5,
    bestFor: ["Visual teams", "Project management users", "Cross-functional teams"],
    pros: ["Visual interface", "Highly customizable", "Easy collaboration", "Works with Monday ecosystem"],
    cons: ["Can get expensive", "Too flexible for some users", "Learning curve for complex setups"],
    pricing: {
      free: true,
      startingPrice: "$10/user/month"
    },
    url: "https://monday.com/sales-crm",
    features: {
      aiAssistant: false,
      contactManagement: true,
      salesAutomation: true,
      emailIntegration: true,
      reporting: true
    }
  },
  {
    id: "5",
    name: "Zoho CRM",
    logo: "https://logo.clearbit.com/zoho.com",
    description: "Affordable CRM with AI assistant (Zia) for small and medium businesses with comprehensive automation.",
    rating: 4.6,
    bestFor: ["Budget-conscious teams", "Small businesses", "International companies"],
    pros: ["Affordable pricing", "Extensive features", "Great automation", "Works with Zoho suite"],
    cons: ["Interface can feel dated", "Support can be slow", "Complex setup for beginners"],
    pricing: {
      free: true,
      startingPrice: "$14/user/month"
    },
    url: "https://zoho.com/crm",
    features: {
      aiAssistant: true,
      contactManagement: true,
      salesAutomation: true,
      emailIntegration: true,
      reporting: true
    }
  }
];

export default function BestCRMSoftwareForTeams() {
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  
  return (
    <>
      <Helmet>
        <title>Best CRM Software for Teams in 2025 - AI Hunt</title>
        <meta name="description" content="Discover the best CRM software for teams to capture leads, nurture your contacts, and streamline your sales process in 2025." />
      </Helmet>

      {/* Hero Section with minimalist design */}
      <div className="bg-gradient-to-br from-orange-600 to-amber-700 text-white pt-36 pb-20 px-4 relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/img/grid-pattern.svg')] opacity-5"></div>
        <motion.div 
          className="container mx-auto max-w-4xl relative z-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col items-center text-center mb-12">
            <Badge className="mb-4 bg-orange-800/60 text-white px-4 py-1 text-sm font-medium">Team CRM</Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
              Best CRM Software for Teams in 2025
            </h1>
            <p className="text-lg text-orange-100 max-w-2xl leading-relaxed">
              Capture your leads, nurture your contacts, and enable your team to collaborate effectively with these top CRM solutions.
            </p>
            <div className="mt-8 text-sm text-orange-200 flex items-center">
              <img src="https://ui-avatars.com/api/?name=Jennifer+Martinez&background=F97316&color=fff" alt="Jennifer Martinez" className="w-8 h-8 rounded-full mr-2 border-2 border-orange-400" />
              <p>By Jennifer Martinez • Last updated: June 2025</p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Introduction with minimalist design */}
      <div className="py-16 px-4 bg-gray-50">
        <div className="container mx-auto max-w-3xl">
          <motion.div 
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-2xl font-bold mb-6 text-gray-900">Customer Relationships, Elevated</h2>
            <p className="text-gray-700 leading-relaxed mb-0">
              Modern CRM software goes beyond simple contact management, offering AI-powered insights, automation, and collaborative tools that help teams stay aligned and close more deals. The right CRM can transform how your team engages with prospects and nurtures customer relationships.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Tool Reviews with cleaner design */}
      <div className="py-20 bg-white px-4">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Best CRM Software for Teams</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">We've evaluated the leading CRM solutions to help you find the perfect platform for your team's needs.</p>
          </div>
          
          {toolsList.map((tool, index) => (
            <motion.div 
              key={tool.id} 
              className="mb-16 border border-gray-100 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true, margin: "-100px" }}
            >
              {/* Tool Header with simplified design */}
              <div className="flex items-center gap-4 border-b border-gray-100 p-6 bg-white">
                <div className="w-14 h-14 bg-white rounded-lg flex items-center justify-center shadow-sm overflow-hidden border border-gray-100">
                  <img 
                    src={tool.logo} 
                    alt={tool.name} 
                    className="w-10 h-10 object-contain"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = `https://ui-avatars.com/api/?name=${encodeURIComponent(tool.name)}&background=F97316&color=fff`;
                    }}
                  />
                </div>
                <div>
                  <div className="flex items-center">
                    <h3 className="text-xl font-bold text-gray-900">{tool.name}</h3>
                    <div className="ml-3 px-2 py-0.5 bg-orange-100 text-orange-800 rounded-full text-xs font-medium">
                      #{index + 1}
                    </div>
                  </div>
                  <div className="flex items-center mt-1">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star 
                          key={i} 
                          className={`w-4 h-4 ${i < Math.floor(tool.rating) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} 
                        />
                      ))}
                    </div>
                    <span className="text-sm ml-2 text-gray-700">{tool.rating.toFixed(1)}/5.0</span>
                  </div>
                </div>
                
                <div className="ml-auto">
                  <a href={tool.url} target="_blank" rel="noopener noreferrer">
                    <Button className="bg-orange-600 hover:bg-orange-700 rounded-lg">
                      Visit Website <ArrowRight className="ml-2 w-4 h-4" />
                    </Button>
                  </a>
                </div>
              </div>

              {/* Tool Content with simplified design */}
              <div className="p-6">
                <p className="text-gray-700 leading-relaxed mb-8">{tool.description}</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  {/* Pros and Cons in two columns */}
                  <div className="bg-gray-50 p-5 rounded-lg">
                    <h4 className="font-semibold mb-4 text-gray-900 flex items-center">
                      <span className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-2">
                        <Check className="w-3 h-3 text-green-700" />
                      </span>
                      Pros
                    </h4>
                    <ul className="space-y-2">
                      {tool.pros.map((pro, i) => (
                        <li key={i} className="flex items-start text-sm">
                          <span className="w-5 h-5 mr-2 rounded-full bg-green-100 flex items-center justify-center mt-0">
                            <Check className="w-3 h-3 text-green-600" />
                          </span>
                          <span className="text-gray-800">{pro}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="bg-gray-50 p-5 rounded-lg">
                    <h4 className="font-semibold mb-4 text-gray-900 flex items-center">
                      <span className="w-5 h-5 rounded-full bg-red-100 flex items-center justify-center mr-2">
                        <X className="w-3 h-3 text-red-700" />
                      </span>
                      Cons
                    </h4>
                    <ul className="space-y-2">
                      {tool.cons.map((con, i) => (
                        <li key={i} className="flex items-start text-sm">
                          <span className="w-5 h-5 mr-2 rounded-full bg-red-100 flex items-center justify-center mt-0">
                            <X className="w-3 h-3 text-red-600" />
                          </span>
                          <span className="text-gray-800">{con}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                {/* Features Icons with simplified design */}
                <div className="grid grid-cols-5 gap-3 mb-6 bg-gray-50 p-4 rounded-lg">
                  {[
                    { name: "AI Assistant", icon: <MessageSquare className="w-4 h-4" />, active: tool.features.aiAssistant },
                    { name: "Contact Mgmt", icon: <Users className="w-4 h-4" />, active: tool.features.contactManagement },
                    { name: "Sales Automation", icon: <BarChart2 className="w-4 h-4" />, active: tool.features.salesAutomation },
                    { name: "Email Integration", icon: <Mail className="w-4 h-4" />, active: tool.features.emailIntegration },
                    { name: "Reporting", icon: <BarChart2 className="w-4 h-4" />, active: tool.features.reporting }
                  ].map((feature, i) => (
                    <div key={i} className={`flex flex-col items-center p-3 rounded-lg ${feature.active ? 'text-orange-700' : 'text-gray-400'}`}>
                      {feature.icon}
                      <span className="text-xs font-medium text-center mt-1">{feature.name}</span>
                      {feature.active ? (
                        <div className="w-3 h-3 mt-1 rounded-full bg-orange-500"></div>
                      ) : (
                        <div className="w-3 h-3 mt-1 rounded-full bg-gray-300"></div>
                      )}
                    </div>
                  ))}
                </div>
                
                {/* Best suited for section */}
                <div className="mb-6">
                  <h4 className="font-semibold mb-3 text-gray-900 text-sm">Best For:</h4>
                  <div className="flex flex-wrap gap-2">
                    {tool.bestFor.map((user, i) => (
                      <Badge key={i} className="bg-orange-100 text-orange-800 font-medium">
                        {user}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                {/* Footer with simplified design */}
                <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-100">
                  <div className="text-sm">
                    <span className="text-gray-500">Starting Price:</span>
                    <span className="ml-2 font-medium text-gray-900">{tool.pricing.startingPrice}</span>
                    {tool.pricing.free && (
                      <Badge className="ml-2 bg-green-100 text-green-800 font-medium">Free Plan</Badge>
                    )}
                  </div>
                  
                  <a href={tool.url} target="_blank" rel="noopener noreferrer">
                    <Button className="bg-orange-600 hover:bg-orange-700">
                      Try It Now
                    </Button>
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Simplified Newsletter Signup */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto max-w-3xl px-4">
          <div className="bg-gradient-to-r from-orange-600 to-amber-700 rounded-lg p-8 text-center shadow-lg">
            <h2 className="text-2xl font-bold mb-3 text-white">Join our newsletter</h2>
            <p className="text-orange-100 mb-6 max-w-2xl mx-auto">Get the latest updates on new CRM solutions and sales tools delivered to your inbox.</p>
            <div className="flex max-w-md mx-auto gap-3">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 rounded-lg border-0 px-4 py-2 focus:ring-2 focus:ring-white focus:ring-opacity-50"
              />
              <Button className="bg-white text-orange-700 hover:bg-gray-100 rounded-lg px-4 py-2 font-medium">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}; 