import { <PERSON>rid, MessageSquare, Image, Code, Video, Music, Brain, Bot, Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { useTools } from "@/lib/api/tools";

interface Category {
  id: string;
  name: string;
  description: string;
  icon: JSX.Element;
  count: number;
  filterValue: string;
}

export const Categories = () => {
  const navigate = useNavigate();
  const { data: tools = [], isLoading, error } = useTools();

  const categories: Category[] = [
    {
      id: "chatbots",
      name: "Chatbots & Assistants",
      description: "AI-powered conversational agents and virtual assistants",
      icon: <MessageSquare className="w-6 h-6" />,
      count: tools.filter(tool => tool.category === "AI Chatbots and Assistants").length,
      filterValue: "AI Chatbots and Assistants"
    },
    {
      id: "image",
      name: "Image Generation",
      description: "Create, edit, and enhance images with AI",
      icon: <Image className="w-6 h-6" />,
      count: tools.filter(tool => tool.category === "AI for Image Generation").length,
      filterValue: "AI for Image Generation"
    },
    {
      id: "code",
      name: "Code & Development",
      description: "AI tools for programming and development",
      icon: <Code className="w-6 h-6" />,
      count: tools.filter(tool => 
        tool.category === "AI for Coding and Development" || 
        tool.category === "AI for Development"
      ).length,
      filterValue: "AI for Coding and Development"
    },
    {
      id: "video",
      name: "Video & Animation",
      description: "Create and edit videos using AI",
      icon: <Video className="w-6 h-6" />,
      count: tools.filter(tool => 
        tool.category === "AI for Video Generation" || 
        tool.category === "AI for Video Editing"
      ).length,
      filterValue: "AI for Video Generation"
    },
    {
      id: "audio",
      name: "Audio & Music",
      description: "AI-powered audio generation and editing",
      icon: <Music className="w-6 h-6" />,
      count: tools.filter(tool => 
        tool.category === "AI for Audio Enhancement" || 
        tool.category === "AI for Music Generation" ||
        tool.category === "AI for Voice Generation"
      ).length,
      filterValue: "AI for Audio Enhancement"
    },
    {
      id: "research",
      name: "Research & Analysis",
      description: "AI tools for data analysis and research",
      icon: <Search className="w-6 h-6" />,
      count: tools.filter(tool => tool.category === "AI Search Engines and Research Tools").length,
      filterValue: "AI Search Engines and Research Tools"
    },
    {
      id: "productivity",
      name: "Productivity",
      description: "AI-powered tools to boost productivity",
      icon: <Brain className="w-6 h-6" />,
      count: tools.filter(tool => tool.category === "AI for Productivity").length,
      filterValue: "AI for Productivity"
    },
    {
      id: "automation",
      name: "Automation",
      description: "Automate tasks and workflows with AI",
      icon: <Bot className="w-6 h-6" />,
      count: tools.filter(tool => tool.category === "AI for Automation").length,
      filterValue: "AI for Automation"
    }
  ];

  const handleCategoryClick = (category: Category) => {
    navigate(`/category/${category.id}`, { state: { category: category.filterValue } });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Categories</h2>
          <p className="text-gray-600">Please try again later</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 mt-20">
      {/* Header */}
      <div className="flex flex-col items-center text-center mb-12">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-purple-100 mb-4">
          <Grid className="w-8 h-8 text-purple-600" />
        </div>
        <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-4">
          Browse by Category
        </h1>
        <p className="text-gray-600 max-w-2xl">
          Explore our comprehensive collection of AI tools organized by category. Find the perfect solution for your needs.
        </p>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {categories.map((category) => (
          <div 
            key={category.id}
            className="group relative bg-white rounded-2xl p-6 border border-gray-100 hover:border-purple-200 transition-all duration-300 hover:shadow-lg cursor-pointer"
            onClick={() => handleCategoryClick(category)}
          >
            {/* Icon */}
            <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-purple-100 text-purple-600 mb-4 group-hover:scale-110 transition-transform duration-300">
              {category.icon}
            </div>

            {/* Content */}
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {category.name}
            </h3>
            <p className="text-gray-600 mb-4">
              {category.description}
            </p>

            {/* Count Badge */}
            <div className="absolute top-6 right-6 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm">
              {category.count} tools
            </div>

            {/* View All Button */}
            <Button 
              variant="ghost"
              className="w-full justify-center rounded-xl hover:bg-purple-50 hover:text-purple-600 group-hover:border-purple-200"
            >
              View All
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}; 