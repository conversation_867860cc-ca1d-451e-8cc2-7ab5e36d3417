// Simple test script to verify the config API endpoint
import axios from 'axios';

const apiUrl = 'http://localhost:3005/api/config';

async function testConfig() {
  try {
    console.log('Testing API endpoint:', apiUrl);
    const response = await axios.get(apiUrl);
    console.log('Success! Response:', response.data);
  } catch (error) {
    console.error('Error testing config endpoint:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testConfig(); 