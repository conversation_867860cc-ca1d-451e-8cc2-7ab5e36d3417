# Changelog

All notable changes to the Mindrix project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.4] - 2025-05-07

### Fixed
- Fixed issues with the Appearance Settings page where logo uploads were failing
- Improved logo management with better update handling for default, light and dark mode logos
- Fixed authentication checks in backend API endpoints for logo uploads
- Added support for admin access via email domain verification
- Fixed UI refresh issues with the site settings
- Added local storage fallback for logo uploads when Cloudinary is unavailable
- Enhanced Cloudinary integration with automatic detection of missing credentials
- Added better error messages and fallback for image uploads when Cloudinary is not configured
- Fixed SVG upload issues by providing specific guidance on SVG requirements (xmlns, width, height)
- Improved error handling for various file types with clearer error messages

### Added
- Added proper meta tags management with Open Graph (OG) image support
- Added visual previews showing how shared links appear on social platforms
- Added a global SiteConfigContext provider for better state management
- Created a useSiteSettings hook with auto-refreshing capabilities
- Implemented a MetaTagsManager component using react-helmet-async
- Added robust file upload system with cloud and local storage options
- Added option to display logo without site name text for cleaner navigation when logo already includes text
- Added dedicated favicon management with preview and instant updates
- Added size recommendations for logos and favicons with helpful guidelines
- Added comprehensive social media integration with Facebook, Twitter, Instagram, LinkedIn, and GitHub links in footer
- Implemented Google Analytics support with automatic page view tracking across the application
- Added automatic storage type detection in image uploads (shows whether files are stored in Cloudinary or local storage)
- Improved navigation with fixed navbar in main site and admin panel
- Added helpful SVG format guidance in the logo upload component

### Changed
- Improved error handling and logging for file uploads
- Simplified the Appearance tab in admin settings
- Enhanced authentication flow for admin operations
- Updated documentation with new features
- Removed Features tab from site settings for a more streamlined interface
- Enhanced footer component to display all configured social media links
- Updated backend to intelligently use Cloudinary when available or local storage when not

## v1.0.4 (2025-05-07)

### Improvements
- Enhanced image upload functionality with better error handling and improved Cloudinary integration
- Simplified logo and favicon upload by restricting to only JPG and PNG formats for better compatibility
  - **IMPORTANT:** SVG format support has been removed due to integration challenges
  - Use PNG format for logos requiring transparency
- Added automatic fallback to local storage when Cloudinary is unavailable
- Improved error messages for upload failures with specific guidance based on file types
- Added detailed logging for troubleshooting upload issues

### Bug Fixes
- Fixed favicon and logo upload issues with Cloudinary by optimizing upload configuration
- Resolved error handling for file type validation
- Fixed inconsistent storage location reporting in the UI

### Other Changes
- Updated documentation to clarify supported image formats
- Added configuration parameter to control image quality and dimensions

## v1.0.3 (2025-05-01)

// ... existing code ... 