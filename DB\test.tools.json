[{"_id": {"$oid": "000000000000000000000043"}, "name": "Nosto", "slug": "nosto", "description": "An AI-powered platform that delivers personalized shopping experiences for e-commerce businesses.", "websiteUrl": "https://www.nosto.com", "category": "AI for E-commerce", "tags": ["AI E-commerce", "Personalization", "Customer Experience"], "pricing": {"type": "paid", "startingPrice": 199}, "features": ["Personalized Recommendations", "Customer Segmentation", "A/B Testing"], "logo": "https://logo.clearbit.com/nosto.com", "status": "published", "isTrending": true, "isNew": false, "views": 362, "votes": 180, "rating": 4.4, "reviews": 180, "__v": 0, "updatedAt": {"$date": "2025-04-17T13:57:23.661Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000049"}, "name": "<PERSON><PERSON><PERSON>", "slug": "emarsys", "description": "An AI-powered marketing automation platform that helps e-commerce businesses deliver personalized campaigns and drive sales.", "websiteUrl": "https://www.emarsys.com", "category": "AI for E-commerce", "tags": ["AI E-commerce", "Marketing Automation", "Personalization"], "pricing": {"type": "paid", "startingPrice": 1000}, "features": ["Marketing Automation", "Personalization", "Customer Segmentation"], "logo": "https://logo.clearbit.com/emarsys.com", "status": "published", "isTrending": true, "isNew": false, "views": 365, "votes": 180, "rating": 4.4, "reviews": 180, "__v": 0, "updatedAt": {"$date": "2025-04-17T04:41:05.614Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000005a"}, "name": "Gift Genie AI", "slug": "gift-genie-ai", "description": "Free AI-powered tool that generates personalized gift ideas for various occasions like Christmas, Birthdays, and Holidays.", "websiteUrl": "https://giftgenie.ai", "category": "AI for Fun", "tags": ["Gifts", "Recommendations", "Personal"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Gift Recommendations", "Occasion-based Suggestions", "Personalization"], "logo": "https://ui-avatars.com/api/?name=Gift%20Genie%20AI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 102, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.017Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000005d"}, "name": "The GPT Who Lived", "slug": "the-gpt-who-lived", "description": "A cloud-based file storage and collaboration platform with AI-powered organization and search capabilities.", "websiteUrl": "https://gptlived.ai", "category": "AI for Productivity", "tags": ["Storage", "Collaboration", "Organization"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Smart Storage", "AI Organization", "Collaboration Tools"], "logo": "https://ui-avatars.com/api/?name=The%20GPT%20Who%20Lived&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 40, "votes": 140, "rating": 4.3, "reviews": 140, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.017Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "00000000000000000000005e"}, "name": "<PERSON>", "slug": "<PERSON><PERSON>", "description": "AI-powered email assistant that helps craft intelligent and professional replies to emails.", "websiteUrl": "https://ellieai.com", "category": "AI for Productivity", "tags": ["Email", "Communication", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 799}, "features": ["Smart Replies", "Email Templates", "Tone Adjustment"], "logo": "https://logo.clearbit.com/ellieai.com", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 220, "rating": 4.5, "reviews": 220, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000006d"}, "name": "Pirate Diffusion", "slug": "pirate-diffusion", "description": "Creative AI-powered image generation tool with unique artistic styles and effects.", "websiteUrl": "https://piratediffusion.com", "category": "AI for Image Generation", "tags": ["Art", "Image Generation", "Creative"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Image Generation", "Style Transfer", "Custom Effects"], "logo": "https://logo.clearbit.com/piratediffusion.com", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 220, "rating": 4.5, "reviews": 220, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000072"}, "name": "AI Alfred", "slug": "ai-<PERSON><PERSON>", "description": "Smart Chrome Extension that summarizes articles and web content for quick comprehension.", "websiteUrl": "https://aialfred.app", "category": "AI for Productivity", "tags": ["Chrome Extension", "Summarization", "Reading"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Article Summarization", "Quick Reading", "Browser Integration"], "logo": "https://ui-avatars.com/api/?name=AI%20Alfred&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 250, "rating": 4.5, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000079"}, "name": "Magic Prints", "slug": "magic-prints", "description": "AI-powered platform for creating unique merchandise designs with customizable styles.", "websiteUrl": "https://magicprints.ai", "category": "AI for Image Generation", "tags": ["Design", "Merchandise", "Art"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Design Generation", "Merchandise Creation", "Style Customization"], "logo": "https://ui-avatars.com/api/?name=Magic%20Prints&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 51, "votes": 160, "rating": 4.4, "reviews": 160, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000086"}, "name": "Obviously AI", "slug": "obviously-ai", "description": "Comprehensive platform for AI model building, deployment, monitoring, and optimization.", "websiteUrl": "https://obviously.ai", "category": "AI for Coding and Development", "tags": ["AI", "Development", "Machine Learning"], "pricing": {"type": "paid", "startingPrice": 79}, "features": ["Model Building", "Deployment Tools", "Performance Monitoring"], "logo": "https://logo.clearbit.com/obviously.ai", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 280, "rating": 4.7, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000095"}, "name": "ReadEasy.ai", "slug": "readeasy-ai", "description": "AI-powered tool for simplifying complex text and making content more accessible.", "websiteUrl": "https://readeasy.ai", "category": "AI for Text Enhancement", "tags": ["Text", "Accessibility", "Education"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Text Simplification", "Readability Enhancement", "Content Adaptation"], "logo": "https://logo.clearbit.com/readeasy.ai", "status": "published", "isTrending": false, "isNew": true, "views": 45, "votes": 150, "rating": 4.4, "reviews": 150, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000a5"}, "name": "Emvoice", "slug": "emvoice", "description": "DAW plugin for generating realistic vocal performances from text with AI technology.", "websiteUrl": "https://emvoice.com", "category": "AI for Audio Creation", "tags": ["Music", "Voice", "Audio"], "pricing": {"type": "paid", "startingPrice": 3999}, "features": ["Vocal Synthesis", "Text-to-Speech", "Music Production"], "logo": "https://ui-avatars.com/api/?name=Emvoice&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 290, "rating": 4.7, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000c9"}, "name": "Toucan", "slug": "toucan-1", "description": "Versatile AI platform for writing and chatbot content generation.", "websiteUrl": "https://toucan.ai", "category": "AI for Marketing", "tags": ["Content", "Chatbots", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Content Generation", "Chatbot Creation", "Marketing Copy"], "logo": "https://ui-avatars.com/api/?name=Toucan&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 71, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000e5"}, "name": "DataViz AI", "slug": "dataviz-ai-1", "description": "Intelligent data visualization tool that creates beautiful charts and graphs automatically.", "websiteUrl": "https://dataviz.ai", "category": "AI for Business", "tags": ["Data", "Visualization", "Business"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Data Visualization", "Chart Generation", "Interactive Graphs"], "logo": "https://ui-avatars.com/api/?name=DataViz%20AI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 220, "rating": 4.5, "reviews": 220, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000f5"}, "name": "Rewind", "slug": "rewind", "description": "Save anything, including conversations and make them searchable with AI.", "websiteUrl": "https://rewind.ai", "category": "AI for Productivity", "tags": ["Productivity", "Search", "Archive"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Conversation Storage", "Smart Search", "History Tracking"], "logo": "https://logo.clearbit.com/rewind.ai", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000f8"}, "name": "Make My Workout", "slug": "make-my-workout", "description": "AI-powered tool for generating personalized workout plans and fitness routines.", "websiteUrl": "https://makemyworkout.ai", "category": "AI for Self-Improvement", "tags": ["Fitness", "Health", "Workout"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Workout Generation", "Fitness Planning", "Progress Tracking"], "logo": "https://ui-avatars.com/api/?name=Make%20My%20Workout&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 220, "rating": 4.5, "reviews": 220, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000130"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "quillbot", "description": "AI writing and paraphrasing tool for improving content quality.", "websiteUrl": "https://quillbot.com", "category": "AI for Writing", "tags": ["Writing", "Paraphrasing", "Grammar"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Paraphrasing", "Grammar Check", "Content Enhancement"], "logo": "https://logo.clearbit.com/quillbot.com", "status": "published", "isTrending": false, "isNew": true, "views": 202, "votes": 480, "rating": 4.7, "reviews": 480, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000140"}, "name": "Pika Labs", "slug": "pika-labs", "description": "AI video generation and editing platform with advanced features.", "websiteUrl": "https://pika.art", "category": "AI for Video", "tags": ["Video", "Animation", "AI"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Video Generation", "Video Editing", "Animation"], "logo": "https://ui-avatars.com/api/?name=Pika%20Labs&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 102, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000146"}, "name": "Character AI", "slug": "character-ai", "description": "Platform for creating and interacting with AI characters.", "websiteUrl": "https://character.ai", "category": "AI for Entertainment", "tags": ["AI", "Characters", "Entertainment"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Character Creation", "AI Chat", "Roleplay"], "logo": "https://logo.clearbit.com/character.ai", "status": "published", "isTrending": false, "isNew": true, "views": 189, "votes": 420, "rating": 4.6, "reviews": 420, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000014b"}, "name": "Leonardo AI", "slug": "leonardo-ai", "description": "AI platform for generating and editing images with advanced controls.", "websiteUrl": "https://leonardo.ai", "category": "AI for Art", "tags": ["Art", "AI", "Design"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Image Generation", "Image Editing", "Style Transfer"], "logo": "https://logo.clearbit.com/leonardo.ai", "status": "published", "isTrending": false, "isNew": true, "views": 141, "votes": 360, "rating": 4.7, "reviews": 360, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000154"}, "name": "Obsidian", "slug": "obsidian", "description": "Knowledge base with AI-powered features and plugins.", "websiteUrl": "https://obsidian.md", "category": "AI for Productivity", "tags": ["Notes", "Knowledge", "<PERSON><PERSON>"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Note Taking", "Knowledge Graph", "AI Plugins"], "logo": "https://logo.clearbit.com/obsidian.md", "status": "published", "isTrending": false, "isNew": true, "views": 258, "votes": 620, "rating": 4.8, "reviews": 620, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000002"}, "name": "MidJourney", "slug": "midjourney", "description": "An AI tool that transforms text prompts into artistic and surreal visuals, ideal for designers and marketers.", "websiteUrl": "https://www.midjourney.com", "category": "AI for Image Generation", "tags": ["AI Art", "Creative Tools", "Design"], "pricing": {"type": "paid", "startingPrice": 10}, "features": ["Text-to-Image", "Artistic Styles", "High-Quality Visuals"], "logo": "https://logo.clearbit.com/midjourney.com", "status": "published", "isTrending": true, "isNew": false, "views": 508, "votes": 869, "rating": 4.7, "reviews": 869, "__v": 0, "updatedAt": {"$date": "2025-04-17T17:25:13.506Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000018"}, "name": "Surfer SEO", "slug": "surfer-seo", "description": "An AI-powered SEO tool that helps optimize content for search engines.", "websiteUrl": "https://www.surferSEO.com", "category": "AI for SEO", "tags": ["AI SEO", "Content Optimization", "Marketing"], "pricing": {"type": "paid", "startingPrice": 59}, "features": ["Content Optimization", "Keyword Research", "SEO Audits"], "logo": "https://logo.clearbit.com/surferseo.com", "status": "published", "isTrending": true, "isNew": false, "views": 323, "votes": 200, "rating": 4.5, "reviews": 200, "__v": 0, "updatedAt": {"$date": "2025-04-15T06:41:20.538Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000001d"}, "name": "Wordtune", "slug": "wordtune", "description": "An AI writing assistant that helps rephrase and improve the tone of written content.", "websiteUrl": "https://www.wordtune.com", "category": "AI for Text Enhancement", "tags": ["AI Writing", "Productivity", "Text Enhancement"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Rephrasing", "Tone Adjustment", "Grammar Checks"], "logo": "https://logo.clearbit.com/wordtune.com", "status": "published", "isTrending": true, "isNew": false, "views": 262, "votes": 190, "rating": 4.4, "reviews": 190, "__v": 0, "updatedAt": {"$date": "2025-04-17T18:10:18.110Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000001f"}, "name": "DeepL", "slug": "deepl", "description": "An AI-powered translation tool that provides accurate and natural translations in multiple languages.", "websiteUrl": "https://www.deepl.com", "category": "AI for Translation", "tags": ["AI Translation", "Productivity", "Multilingual"], "pricing": {"type": "freemium", "startingPrice": 874}, "features": ["Multilingual Support", "High Accuracy", "Document Translation"], "logo": "https://logo.clearbit.com/deepl.com", "status": "published", "isTrending": true, "isNew": false, "views": 511, "votes": 300, "rating": 4.7, "reviews": 300, "__v": 0, "updatedAt": {"$date": "2025-04-16T15:56:49.115Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000002b"}, "name": "Sourcery", "slug": "sourcery", "description": "An AI-powered tool for refactoring and improving code quality in Python.", "websiteUrl": "https://sourcery.ai", "category": "AI for Coding and Development", "tags": ["AI Coding", "Python", "Developer Tools"], "pricing": {"type": "freemium", "startingPrice": 10}, "features": ["Code Refactoring", "Code Quality Improvement", "Python Support"], "logo": "https://logo.clearbit.com/sourcery.ai", "status": "published", "isTrending": true, "isNew": false, "views": 105, "votes": 130, "rating": 4.4, "reviews": 130, "__v": 0, "updatedAt": {"$date": "2025-04-13T08:28:58.446Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000002c"}, "name": "AIXcoder", "slug": "aixcoder", "description": "An AI-powered code suggestion and optimization tool for developers.", "websiteUrl": "https://www.aixcoder.com", "category": "AI for Coding and Development", "tags": ["AI Coding", "Developer Tools", "Code Optimization"], "pricing": {"type": "freemium", "startingPrice": 15}, "features": ["Code Suggestions", "Code Optimization", "Multi-Language Support"], "logo": "https://logo.clearbit.com/aixcoder.com", "status": "published", "isTrending": true, "isNew": false, "views": 115, "votes": 140, "rating": 4.3, "reviews": 140, "__v": 0, "updatedAt": {"$date": "2025-04-14T17:51:59.612Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000048"}, "name": "<PERSON><PERSON><PERSON>", "slug": "klevu", "description": "An AI-powered e-commerce search platform that helps businesses improve product discovery and customer experience.", "websiteUrl": "https://www.klevu.com", "category": "AI for E-commerce", "tags": ["AI E-commerce", "Search", "Personalization"], "pricing": {"type": "paid", "startingPrice": 99}, "features": ["Search Optimization", "Personalized Results", "Analytics"], "logo": "https://logo.clearbit.com/klevu.com", "status": "published", "isTrending": true, "isNew": false, "views": 310, "votes": 160, "rating": 4.3, "reviews": 160, "__v": 0, "updatedAt": {"$date": "2025-04-16T12:47:44.788Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000054"}, "name": "Flowshot", "slug": "flowshot", "description": "A powerful tool for Google Sheets automation, streamlining workflows and enhancing productivity.", "websiteUrl": "https://flowshot.app", "category": "AI for Productivity", "tags": ["Automation", "Google Sheets", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Sheets Automation", "Workflow Templates", "Custom Scripts"], "logo": "https://ui-avatars.com/api/?name=Flowshot&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 60, "votes": 180, "rating": 4.4, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.017Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "00000000000000000000005f"}, "name": "Dreamescape", "slug": "dreamescape", "description": "AI-powered iOS app for exploring and understanding dreams through analysis and interpretation.", "websiteUrl": "https://dreamescape.app", "category": "AI for Self-Improvement", "tags": ["Dreams", "Self-Improvement", "Wellness"], "pricing": {"type": "freemium", "startingPrice": 499}, "features": ["Dream Analysis", "Pattern Recognition", "Personal Insights"], "logo": "https://logo.clearbit.com/dreamescape.app", "status": "published", "isTrending": false, "isNew": true, "views": 60, "votes": 190, "rating": 4.4, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000006a"}, "name": "WowTo", "slug": "wowto", "description": "All-in-one video creation and knowledge base platform for creating, hosting, and updating educational videos.", "websiteUrl": "https://wowto.ai", "category": "AI for Video Generation", "tags": ["Video", "Education", "Knowledge Base"], "pricing": {"type": "paid", "startingPrice": 3499}, "features": ["Video Creation", "Knowledge Base", "Content Management"], "logo": "https://logo.clearbit.com/wowto.ai", "status": "published", "isTrending": false, "isNew": true, "views": 65, "votes": 190, "rating": 4.5, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000084"}, "name": "Userdoc", "slug": "userdoc", "description": "AI-powered tool for requirements management and system documentation organization.", "websiteUrl": "https://userdoc.ai", "category": "AI for Productivity", "tags": ["Documentation", "Management", "Organization"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Requirements Management", "Documentation", "Organization"], "logo": "https://ui-avatars.com/api/?name=Userdoc&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 55, "votes": 170, "rating": 4.4, "reviews": 170, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000009b"}, "name": "ResearchGPT", "slug": "researchgpt", "description": "AI research assistant for academic literature review and paper summarization.", "websiteUrl": "https://researchgpt.ai", "category": "AI for Research", "tags": ["Research", "Academic", "Analysis"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Paper Summarization", "Literature Review", "Citation Management"], "logo": "https://ui-avatars.com/api/?name=ResearchGPT&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 105, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000a1"}, "name": "Elicit", "slug": "elicit", "description": "Automated research assistant with powerful workflows for academic and scientific research.", "websiteUrl": "https://elicit.org", "category": "AI for Research", "tags": ["Research", "Academic", "Science"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Research Automation", "Literature Analysis", "Citation Management"], "logo": "https://logo.clearbit.com/elicit.org", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000ac"}, "name": "ResolveAI", "slug": "<PERSON><PERSON>", "description": "Comprehensive AI-powered customer support platform for businesses.", "websiteUrl": "https://resolveai.com", "category": "AI for Marketing", "tags": ["Customer Support", "Automation", "Business"], "pricing": {"type": "paid", "startingPrice": 4999}, "features": ["Customer Support Automation", "Ticket Management", "Analytics"], "logo": "https://logo.clearbit.com/resolveai.com", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 290, "rating": 4.7, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000b1"}, "name": "Hoppy Copy", "slug": "hoppy-copy", "description": "AI-powered platform for creating compelling email and ad copy.", "websiteUrl": "https://hoppycopy.co", "category": "AI for Content Creation", "tags": ["Copywriting", "Email", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Email Copy Generation", "Ad Copy Creation", "Template Library"], "logo": "https://logo.clearbit.com/hoppycopy.co", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 220, "rating": 4.5, "reviews": 220, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000b6"}, "name": "Storyd", "slug": "storyd", "description": "AI-powered platform for creating compelling data presentations and visualizations.", "websiteUrl": "https://storyd.ai", "category": "AI for Productivity", "tags": ["Presentations", "Data", "Visualization"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Data Visualization", "Presentation Creation", "Story Building"], "logo": "https://logo.clearbit.com/storyd.ai", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000cc"}, "name": "Invoke AI", "slug": "invoke-ai", "description": "User-friendly interface for Stable Diffusion with advanced customization options.", "websiteUrl": "https://invoke.ai", "category": "AI for Image Generation", "tags": ["Art", "Image Generation", "Design"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Image Generation", "Style Control", "Customization"], "logo": "https://logo.clearbit.com/invoke.ai", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 230, "rating": 4.5, "reviews": 230, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000ff"}, "name": "Hugging Face", "slug": "hugging-face", "description": "Essential tool for face training in Stable Diffusion and AI model development.", "websiteUrl": "https://huggingface.co", "category": "AI for Image Generation", "tags": ["AI", "Development", "Image Generation"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Face Training", "Model Development", "AI Integration"], "logo": "https://logo.clearbit.com/huggingface.co", "status": "published", "isTrending": false, "isNew": true, "views": 150, "votes": 520, "rating": 4.8, "reviews": 520, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "00000000000000000000010e"}, "name": "Hoppy Copy", "slug": "hoppy-copy-2", "description": "AI platform for creating engaging email and ad copy that converts.", "websiteUrl": "https://hoppycopy.co", "category": "AI for Content Creation", "tags": ["Email", "Copywriting", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Email Copy Generation", "Template Library", "A/B Testing"], "logo": "https://logo.clearbit.com/hoppycopy.co", "status": "published", "isTrending": false, "isNew": true, "views": 76, "votes": 220, "rating": 4.5, "reviews": 220, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000014d"}, "name": "Playground AI", "slug": "playground-ai", "description": "Free platform for experimenting with AI image generation.", "websiteUrl": "https://playground.ai", "category": "AI for Art", "tags": ["Art", "AI", "Free"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Image Generation", "Model Testing", "Community"], "logo": "https://logo.clearbit.com/playground.ai", "status": "published", "isTrending": false, "isNew": true, "views": 113, "votes": 320, "rating": 4.5, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000153"}, "name": "Roam Research", "slug": "roam-research", "description": "Note-taking tool for networked thought with AI features.", "websiteUrl": "https://roamresearch.com", "category": "AI for Productivity", "tags": ["Notes", "Knowledge", "Research"], "pricing": {"type": "paid", "startingPrice": 15}, "features": ["Note Taking", "Knowledge Graph", "AI Integration"], "logo": "https://logo.clearbit.com/roamresearch.com", "status": "published", "isTrending": false, "isNew": true, "views": 183, "votes": 480, "rating": 4.7, "reviews": 480, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000001a"}, "name": "<PERSON><PERSON>", "slug": "krisp", "description": "An AI-powered noise-canceling tool for clear audio during calls and recordings.", "websiteUrl": "https://krisp.ai", "category": "AI for Audio Enhancement", "tags": ["AI Audio", "Productivity", "Noise Cancellation"], "pricing": {"type": "freemium", "startingPrice": 5}, "features": ["Noise Cancellation", "Echo Removal", "Background Noise Reduction"], "logo": "https://logo.clearbit.com/krisp.ai", "status": "published", "isTrending": true, "isNew": false, "views": 412, "votes": 250, "rating": 4.6, "reviews": 250, "__v": 0, "updatedAt": {"$date": "2025-04-17T12:46:32.949Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000001e"}, "name": "Anyword", "slug": "anyword", "description": "An AI copywriting tool that generates and optimizes marketing copy for better performance.", "websiteUrl": "https://www.anyword.com", "category": "AI for Content Creation", "tags": ["AI Writing", "Marketing", "Copywriting"], "pricing": {"type": "paid", "startingPrice": 29}, "features": ["Copy Optimization", "Performance Prediction", "Content Generation"], "logo": "https://logo.clearbit.com/anyword.com", "status": "published", "isTrending": true, "isNew": false, "views": 201, "votes": 140, "rating": 4.3, "reviews": 140, "__v": 0, "updatedAt": {"$date": "2025-03-03T10:36:35.155Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000025"}, "name": "Replit AI", "slug": "replit-ai", "description": "An AI-powered collaborative coding platform with built-in debugging and code generation features.", "websiteUrl": "https://replit.com", "category": "AI for Coding and Development", "tags": ["AI Coding", "Developer Tools", "Collaboration"], "pricing": {"type": "freemium", "startingPrice": 7}, "features": ["Collaborative Coding", "Debugging", "Code Generation"], "logo": "https://logo.clearbit.com/replit.com", "status": "published", "isTrending": true, "isNew": false, "views": 106, "votes": 150, "rating": 4.4, "reviews": 150, "__v": 0, "updatedAt": {"$date": "2025-04-17T09:11:18.828Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000027"}, "name": "Amazon CodeWhisperer", "slug": "amazon-codewhisperer", "description": "An AI-powered code suggestion tool by Amazon for improving developer productivity.", "websiteUrl": "https://aws.amazon.com/codewhisperer", "category": "AI for Coding and Development", "tags": ["AI Coding", "Developer Tools", "AWS"], "pricing": {"type": "freemium", "startingPrice": 19}, "features": ["Code Suggestions", "Multi-Language Support", "Integration with AWS"], "logo": "https://logo.clearbit.com/aws.amazon.com", "status": "published", "isTrending": true, "isNew": false, "views": 88, "votes": 110, "rating": 4.2, "reviews": 110, "__v": 0, "updatedAt": {"$date": "2025-04-14T21:45:45.091Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000002f"}, "name": "Sprout Social", "slug": "sprout-social", "description": "An AI-powered social media analytics and management tool for businesses to optimize their social media strategy.", "websiteUrl": "https://sproutsocial.com", "category": "AI for Social Media", "tags": ["AI Social Media", "Analytics", "Management"], "pricing": {"type": "paid", "startingPrice": 99}, "features": ["Social Media Analytics", "Engagement Tracking", "Content Scheduling"], "logo": "https://logo.clearbit.com/sproutsocial.com", "status": "published", "isTrending": true, "isNew": false, "views": 351, "votes": 220, "rating": 4.6, "reviews": 220, "__v": 0, "updatedAt": {"$date": "2025-04-14T05:53:59.732Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000033"}, "name": "Agorapulse", "slug": "agorapulse", "description": "An AI-powered social media scheduling and management tool for businesses to streamline their social media efforts.", "websiteUrl": "https://www.agorapulse.com", "category": "AI for Social Media", "tags": ["AI Social Media", "Scheduling", "Management"], "pricing": {"type": "freemium", "startingPrice": 49}, "features": ["Social Media Scheduling", "Engagement Tracking", "Analytics"], "logo": "https://logo.clearbit.com/agorapulse.com", "status": "published", "isTrending": true, "isNew": false, "views": 226, "votes": 160, "rating": 4.4, "reviews": 160, "__v": 0, "updatedAt": {"$date": "2025-04-13T00:04:20.945Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000035"}, "name": "Planoly", "slug": "planoly", "description": "An AI-powered Instagram planning and scheduling tool for businesses and influencers.", "websiteUrl": "https://www.planoly.com", "category": "AI for Social Media", "tags": ["AI Social Media", "Instagram", "Planning"], "pricing": {"type": "freemium", "startingPrice": 7}, "features": ["Instagram Planning", "Scheduling", "Analytics"], "logo": "https://logo.clearbit.com/planoly.com", "status": "published", "isTrending": true, "isNew": false, "views": 172, "votes": 130, "rating": 4.2, "reviews": 130, "__v": 0, "updatedAt": {"$date": "2025-03-02T06:38:18.518Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000064"}, "name": "Rotor Videos", "slug": "rotor-videos", "description": "AI-powered tool for creating professional lyric videos with automated synchronization and effects.", "websiteUrl": "https://rotorvideos.com", "category": "AI for Video Generation", "tags": ["Video", "Music", "Content"], "pricing": {"type": "paid", "startingPrice": 2499}, "features": ["Lyric Sync", "Video Effects", "Music Integration"], "logo": "https://logo.clearbit.com/rotorvideos.com", "status": "published", "isTrending": false, "isNew": true, "views": 65, "votes": 180, "rating": 4.4, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000006b"}, "name": "GetAnswer", "slug": "getanswer", "description": "Intelligent platform for creating and managing customer support chatbots with natural language processing.", "websiteUrl": "https://getanswer.ai", "category": "AI Chatbots and Assistants", "tags": ["<PERSON><PERSON><PERSON>", "Support", "Automation"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Chatbot Creation", "NLP Integration", "Analytics Dashboard"], "logo": "https://logo.clearbit.com/getanswer.ai", "status": "published", "isTrending": false, "isNew": true, "views": 50, "votes": 160, "rating": 4.4, "reviews": 160, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000076"}, "name": "Fireflies AI", "slug": "fireflies-ai-1", "description": "Comprehensive meeting assistant that provides automated transcription, recaps, tasks, and analytics.", "websiteUrl": "https://fireflies.ai", "category": "AI for Productivity", "tags": ["Meetings", "Transcription", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 19}, "features": ["Meeting Transcription", "Task Tracking", "Analytics"], "logo": "https://logo.clearbit.com/fireflies.ai", "status": "published", "isTrending": false, "isNew": true, "views": 110, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000008d"}, "name": "BarBot AI", "slug": "barbot-ai", "description": "Smart application for personalized cocktail suggestions and recipes.", "websiteUrl": "https://barbot.ai", "category": "AI for Fun", "tags": ["Cocktails", "Drinks", "Entertainment"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Recipe Generation", "Ingredient Suggestions", "Personalization"], "logo": "https://ui-avatars.com/api/?name=BarBot%20AI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "000000000000000000000090"}, "name": "<PERSON><PERSON><PERSON>", "slug": "favird", "description": "Comprehensive aggregator of AI and No-Code tools for easy discovery and comparison.", "websiteUrl": "https://favird.com", "category": "AI for Research", "tags": ["Aggregator", "Tools", "Research"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Tool Discovery", "Comparison", "Reviews"], "logo": "https://ui-avatars.com/api/?name=Favird&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 60, "votes": 180, "rating": 4.5, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "000000000000000000000096"}, "name": "Grain AI", "slug": "grain-ai", "description": "AI-powered meeting assistant for capturing and organizing key insights from video calls.", "websiteUrl": "https://grain.ai", "category": "AI for Productivity", "tags": ["Meetings", "Video", "Collaboration"], "pricing": {"type": "paid", "startingPrice": 29}, "features": ["Meeting Recording", "Insight Extraction", "Video Highlights"], "logo": "https://ui-avatars.com/api/?name=Grain%20AI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 90, "votes": 280, "rating": 4.7, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000f2"}, "name": "DiscuroAI", "slug": "discuroai-1", "description": "Platform for rapidly building and testing complex AI workflows and integrations.", "websiteUrl": "https://discuro.ai", "category": "AI for Coding and Development", "tags": ["Development", "AI", "Workflow"], "pricing": {"type": "paid", "startingPrice": 4999}, "features": ["Workflow Building", "AI Integration", "Testing Tools"], "logo": "https://ui-avatars.com/api/?name=DiscuroAI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 250, "rating": 4.6, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000f9"}, "name": "<PERSON><PERSON>", "slug": "andi", "description": "Smart AI assistant that answers questions or takes you to relevant apps and websites.", "websiteUrl": "https://andisearch.com", "category": "AI Chatbots and Assistants", "tags": ["Cha<PERSON>", "Search", "Navigation"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Smart Navigation", "Question Answering", "App Integration"], "logo": "https://logo.clearbit.com/andisearch.com", "status": "published", "isTrending": false, "isNew": true, "views": 81, "votes": 250, "rating": 4.6, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000fa"}, "name": "<PERSON><PERSON><PERSON>", "slug": "kadoa", "description": "Efficient data extraction tool for websites, PDFs, and databases using AI.", "websiteUrl": "https://kadoa.com", "category": "AI for Development", "tags": ["Data", "Extraction", "Development"], "pricing": {"type": "paid", "startingPrice": 3999}, "features": ["Data Extraction", "PDF Processing", "Database Integration"], "logo": "https://ui-avatars.com/api/?name=<PERSON><PERSON><PERSON>&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 60, "votes": 190, "rating": 4.7, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000fe"}, "name": "Programming Helper", "slug": "programming-helper", "description": "AI-powered assistant for programming, debugging, and development tasks.", "websiteUrl": "https://programminghelper.ai", "category": "AI for Coding and Development", "tags": ["Coding", "Development", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Code Assistance", "Debugging Help", "Development Tools"], "logo": "https://ui-avatars.com/api/?name=Programming%20Helper&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 110, "votes": 340, "rating": 4.6, "reviews": 340, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000101"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "akkio", "description": "Platform for building and deploying AI with your business data.", "websiteUrl": "https://akkio.com", "category": "AI for Business", "tags": ["Business", "Analytics", "AI"], "pricing": {"type": "enterprise", "startingPrice": 499}, "features": ["AI Development", "Data Integration", "Business Analytics"], "logo": "https://logo.clearbit.com/akkio.com", "status": "published", "isTrending": false, "isNew": true, "views": 65, "votes": 190, "rating": 4.7, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000113"}, "name": "GPTForMe", "slug": "gptforme-1", "description": "Upload your own content and then ask questions of it with AI assistance.", "websiteUrl": "https://gptforme.ai", "category": "AI Chatbots and Assistants", "tags": ["Cha<PERSON>", "Content", "Learning"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Content Upload", "Question Answering", "Document Analysis"], "logo": "https://ui-avatars.com/api/?name=GPTForMe&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 56, "votes": 180, "rating": 4.5, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000117"}, "name": "Toucan", "slug": "toucan-2", "description": "Language learning AI that helps you learn new languages while browsing the web.", "websiteUrl": "https://toucan.ai", "category": "AI for Education", "tags": ["Education", "Languages", "Learning"], "pricing": {"type": "freemium", "startingPrice": 899}, "features": ["Browser Integration", "Vocabulary Learning", "Progress Tracking"], "logo": "https://ui-avatars.com/api/?name=Toucan&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 96, "votes": 320, "rating": 4.6, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000119"}, "name": "<PERSON>", "slug": "jasper-1", "description": "Advanced AI writing assistant for long-form content and marketing copy.", "websiteUrl": "https://jasper.ai", "category": "AI for Content Creation", "tags": ["Writing", "Content", "Marketing"], "pricing": {"type": "paid", "startingPrice": 49}, "features": ["Long-form Writing", "Marketing Templates", "Team Collaboration"], "logo": "https://logo.clearbit.com/jasper.ai", "status": "published", "isTrending": false, "isNew": true, "views": 155, "votes": 450, "rating": 4.8, "reviews": 450, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000128"}, "name": "Eleven Labs", "slug": "eleven-labs", "description": "AI voice synthesis platform for natural-sounding speech.", "websiteUrl": "https://elevenlabs.io", "category": "AI for Audio", "tags": ["Audio", "Voice", "Speech"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Voice Synthesis", "Voice Cloning", "Text to Speech"], "logo": "https://logo.clearbit.com/elevenlabs.io", "status": "published", "isTrending": false, "isNew": true, "views": 130, "votes": 380, "rating": 4.8, "reviews": 380, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}}, {"_id": {"$oid": "000000000000000000000132"}, "name": "Fathom", "slug": "fathom", "description": "AI note-taking assistant for meetings and conversations.", "websiteUrl": "https://fathom.video", "category": "AI for Productivity", "tags": ["Meetings", "Notes", "Productivity"], "pricing": {"type": "paid", "startingPrice": 19}, "features": ["Meeting Notes", "Action Items", "Meeting Summaries"], "logo": "https://logo.clearbit.com/fathom.video", "status": "published", "isTrending": false, "isNew": true, "views": 111, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000133"}, "name": "Pictory", "slug": "pictory-2", "description": "AI video creation platform from long-form content.", "websiteUrl": "https://pictory.ai", "category": "AI for Video", "tags": ["Video", "Content", "Marketing"], "pricing": {"type": "paid", "startingPrice": 23}, "features": ["Video Creation", "Content Repurposing", "Automated Editing"], "logo": "https://logo.clearbit.com/pictory.ai", "status": "published", "isTrending": false, "isNew": true, "views": 89, "votes": 260, "rating": 4.6, "reviews": 260, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000167"}, "name": "Captions", "slug": "captions", "description": "Creators studio app for recording, captioning, and sharing videos.", "websiteUrl": "https://captions.ai", "category": "Data Analysis", "tags": ["Video", "Captions", "Social Media"], "pricing": {"type": "free", "startingPrice": 1}, "features": ["Video Captioning", "Social Media Export", "Custom Styling"], "logo": "https://logo.clearbit.com/captions.ai", "status": "archived", "isTrending": false, "isNew": true, "views": 145, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.024Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.024Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000009"}, "name": "Synthesia", "slug": "synthesia", "description": "An AI video creation tool that generates professional videos with AI avatars in multiple languages.", "websiteUrl": "https://www.synthesia.io", "category": "AI for Video Generation", "tags": ["AI Video", "Creative Tools", "Marketing"], "pricing": {"type": "paid", "startingPrice": 30}, "features": ["AI Avatars", "Multilingual Support", "Video Generation"], "logo": "https://logo.clearbit.com/synthesia.io", "status": "published", "isTrending": true, "isNew": false, "views": 108, "votes": 93, "rating": 4.4, "reviews": 93, "__v": 0, "updatedAt": {"$date": "2025-04-17T07:47:58.035Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000000b"}, "name": "Notion AI", "slug": "notion-ai", "description": "An AI-powered productivity tool for note-taking, summarization, and content generation.", "websiteUrl": "https://www.notion.so/product/ai", "category": "AI for Productivity", "tags": ["AI Productivity", "Note-Taking", "Task Management"], "pricing": {"type": "freemium", "startingPrice": 10}, "features": ["Note Summarization", "Content Generation", "Task Automation"], "logo": "https://logo.clearbit.com/notion.so", "status": "published", "isTrending": true, "isNew": false, "views": 814, "votes": 450, "rating": 4.6, "reviews": 450, "__v": 0, "updatedAt": {"$date": "2025-04-16T17:10:01.793Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000019"}, "name": "<PERSON>ase", "slug": "frase", "description": "An AI content optimization tool that helps create SEO-friendly content.", "websiteUrl": "https://www.frase.io", "category": "AI for SEO", "tags": ["AI SEO", "Content Optimization", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Content Research", "SEO Optimization", "Content Briefs"], "logo": "https://logo.clearbit.com/frase.io", "status": "published", "isTrending": true, "isNew": false, "views": 221, "votes": 170, "rating": 4.3, "reviews": 170, "__v": 0, "updatedAt": {"$date": "2025-02-25T00:13:09.855Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000003f"}, "name": "<PERSON><PERSON><PERSON>", "slug": "fetcher", "description": "An AI-powered candidate sourcing platform that automates the process of finding and engaging top talent.", "websiteUrl": "https://www.fetcher.ai", "category": "AI for HR and Recruitment", "tags": ["AI Recruitment", "Candidate Sourcing", "HR Tech"], "pricing": {"type": "paid", "startingPrice": 8000}, "features": ["Candidate Sourcing", "Automated Outreach", "Talent Pool Building"], "logo": "https://logo.clearbit.com/fetcher.ai", "status": "published", "isTrending": true, "isNew": false, "views": 161, "votes": 120, "rating": 4.2, "reviews": 120, "__v": 0, "updatedAt": {"$date": "2025-04-16T12:59:19.995Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000004d"}, "name": "Midjourney V6", "slug": "midjourney-v6", "description": "Next version of Midjourney with photorealistic capabilities", "websiteUrl": "https://www.midjourney.com", "category": "AI for Image Generation", "tags": ["AI Art", "Image Generation", "Design"], "pricing": {"type": "paid", "startingPrice": 10}, "features": ["Photorealistic Generation", "Enhanced Control", "Advanced Styling"], "logo": "https://logo.clearbit.com/midjourney.com", "status": "published", "isTrending": false, "isNew": false, "views": 75, "votes": 0, "rating": 0, "reviews": 0, "__v": 0, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "000000000000000000000062"}, "name": "Hidden Door", "slug": "hidden-door", "description": "Innovative social roleplaying platform using AI to generate unique and immersive stories.", "websiteUrl": "https://hiddendoor.co", "category": "AI for Gaming", "tags": ["Gaming", "Roleplay", "Stories"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Story Generation", "Character Creation", "Interactive Gameplay"], "logo": "https://logo.clearbit.com/hiddendoor.co", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 340, "rating": 4.7, "reviews": 340, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000067"}, "name": "PersonaGen", "slug": "personagen", "description": "AI-powered tool for generating detailed user personas and tailoring data-driven customer experiences.", "websiteUrl": "https://personagen.ai", "category": "AI for Marketing", "tags": ["Marketing", "Research", "Personas"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Persona Creation", "Data Analysis", "Customer Insights"], "logo": "https://ui-avatars.com/api/?name=PersonaGen&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 80, "votes": 230, "rating": 4.6, "reviews": 230, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000069"}, "name": "UGC Scripts", "slug": "ugc-scripts", "description": "AI tool for creating engaging copywriting and content based on audience data analysis.", "websiteUrl": "https://ugcscripts.com", "category": "AI for Content Creation", "tags": ["Content", "UGC", "Marketing"], "pricing": {"type": "paid", "startingPrice": 1999}, "features": ["Content Generation", "Audience Analysis", "Performance Tracking"], "logo": "https://logo.clearbit.com/ugcscripts.com", "status": "published", "isTrending": false, "isNew": true, "views": 55, "votes": 170, "rating": 4.4, "reviews": 170, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000074"}, "name": "Tekst", "slug": "tekst", "description": "Automated customer service platform with AI-powered response generation and management.", "websiteUrl": "https://tekst.ai", "category": "AI Chatbots and Assistants", "tags": ["Customer Service", "Automation", "Support"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Automated Responses", "Customer Service", "Analytics"], "logo": "https://logo.clearbit.com/tekst.ai", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 200, "rating": 4.5, "reviews": 200, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000085"}, "name": "Shulex VOC", "slug": "shulex-voc", "description": "Advanced AI-powered platform for analyzing customer reviews and feedback for actionable insights.", "websiteUrl": "https://shulex.ai", "category": "AI for Marketing", "tags": ["Analytics", "Customer Insights", "Research"], "pricing": {"type": "paid", "startingPrice": 4999}, "features": ["Review Analysis", "Sentiment Tracking", "Insight Generation"], "logo": "https://ui-avatars.com/api/?name=Shulex%20VOC&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 80, "votes": 230, "rating": 4.6, "reviews": 230, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "0000000000000000000000b7"}, "name": "Amadeus Code", "slug": "amadeus-code", "description": "Advanced AI songwriting assistant for music composition and arrangement.", "websiteUrl": "https://amadeuscode.com", "category": "AI for Audio Creation", "tags": ["Music", "Audio", "Composition"], "pricing": {"type": "paid", "startingPrice": 3999}, "features": ["Songwriting", "Composition", "Arrangement"], "logo": "https://logo.clearbit.com/amadeuscode.com", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000c5"}, "name": "Wordtune", "slug": "wordtune-1", "description": "Smart AI writing assistant available as a Chrome Extension.", "websiteUrl": "https://wordtune.com", "category": "AI for Content Creation", "tags": ["Writing", "Chrome Extension", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Writing Enhancement", "Tone Adjustment", "Translation"], "logo": "https://logo.clearbit.com/wordtune.com", "status": "published", "isTrending": true, "isNew": true, "views": 205, "votes": 520, "rating": 4.8, "reviews": 520, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000ce"}, "name": "Question Base", "slug": "question-base", "description": "AI-powered knowledge base for teams in Slack with smart search and organization.", "websiteUrl": "https://questionbase.ai", "category": "AI Chatbots and Assistants", "tags": ["Knowledge Base", "<PERSON><PERSON>ck", "Team"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Knowledge Management", "Slack Integration", "Smart Search"], "logo": "https://ui-avatars.com/api/?name=Question%20Base&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 80, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000d7"}, "name": "Pictory", "slug": "pictory-1", "description": "AI video creation platform for transforming long-form content into short videos.", "websiteUrl": "https://pictory.ai", "category": "AI for Video Creation", "tags": ["Video", "Content", "Marketing"], "pricing": {"type": "paid", "startingPrice": 3999}, "features": ["Video Creation", "Content Repurposing", "Automated Editing"], "logo": "https://logo.clearbit.com/pictory.ai", "status": "published", "isTrending": false, "isNew": true, "views": 90, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000e9"}, "name": "TED SMRZR", "slug": "ted-smrzr-1", "description": "AI-powered tool for summarizing TED talks and educational content.", "websiteUrl": "https://tedsmrzr.com", "category": "AI for Education", "tags": ["Education", "Learning", "Summarization"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Content Summarization", "Key Points Extraction", "Learning Assistance"], "logo": "https://ui-avatars.com/api/?name=TED%20SMRZR&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 80, "votes": 260, "rating": 4.5, "reviews": 260, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000ec"}, "name": "WordHero", "slug": "wordhero-1", "description": "AI-powered writing tool for creating engaging content.", "websiteUrl": "https://wordhero.co", "category": "AI for Writing", "tags": ["Writing", "Content", "SEO"], "pricing": {"type": "paid", "startingPrice": 2499}, "features": ["Content Creation", "Writing Assistant", "SEO Optimization"], "logo": "https://logo.clearbit.com/wordhero.co", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 280, "rating": 4.5, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000f4"}, "name": "GPTForMe", "slug": "gptforme", "description": "Upload your own content and then ask questions of it with AI assistance.", "websiteUrl": "https://gptforme.ai", "category": "AI Chatbots and Assistants", "tags": ["Cha<PERSON>", "Content", "Learning"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Content Upload", "Question Answering", "Document Analysis"], "logo": "https://ui-avatars.com/api/?name=GPTForMe&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 55, "votes": 180, "rating": 4.5, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000fd"}, "name": "Peachly AI", "slug": "peachly-ai", "description": "Complete AI solution for creating, targeting and optimizing social media ads.", "websiteUrl": "https://peachly.ai", "category": "AI for Marketing", "tags": ["Marketing", "Social Media", "Advertising"], "pricing": {"type": "paid", "startingPrice": 79}, "features": ["Ad Creation", "Targeting Optimization", "Performance Analytics"], "logo": "https://ui-avatars.com/api/?name=Peachly%20AI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 260, "rating": 4.7, "reviews": 260, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000103"}, "name": "Audioshake", "slug": "audioshake", "description": "AI-powered tool for separating songs into stems and instrumental tracks.", "websiteUrl": "https://audioshake.ai", "category": "AI for Audio Creation", "tags": ["Audio", "Music", "Production"], "pricing": {"type": "paid", "startingPrice": 2499}, "features": ["Stem Separation", "Audio Processing", "Track Isolation"], "logo": "https://logo.clearbit.com/audioshake.ai", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "00000000000000000000011b"}, "name": "Gamma", "slug": "gamma", "description": "AI-powered presentation and document creation platform.", "websiteUrl": "https://gamma.app", "category": "AI for Content Creation", "tags": ["Presentations", "Design", "Business"], "pricing": {"type": "freemium", "startingPrice": 10}, "features": ["Presentation Creation", "Document Design", "Content Generation"], "logo": "https://logo.clearbit.com/gamma.app", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 290, "rating": 4.7, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000120"}, "name": "Otter AI", "slug": "otter-ai-1", "description": "AI-powered meeting transcription and note-taking assistant.", "websiteUrl": "https://otter.ai", "category": "AI for Productivity", "tags": ["Transcription", "Meetings", "Notes"], "pricing": {"type": "freemium", "startingPrice": 833}, "features": ["Meeting Transcription", "Note Taking", "Meeting Summary"], "logo": "https://logo.clearbit.com/otter.ai", "status": "published", "isTrending": false, "isNew": true, "views": 125, "votes": 380, "rating": 4.6, "reviews": 380, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000127"}, "name": "Replicate", "slug": "replicate", "description": "Platform for running machine learning models in the cloud.", "websiteUrl": "https://replicate.com", "category": "AI Development", "tags": ["Development", "ML", "Cloud"], "pricing": {"type": "paid", "startingPrice": 0}, "features": ["Model Deployment", "API Access", "Cloud Computing"], "logo": "https://logo.clearbit.com/replicate.com", "status": "published", "isTrending": false, "isNew": true, "views": 141, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000012e"}, "name": "Mem.ai", "slug": "mem-ai", "description": "AI-powered note-taking and knowledge management platform.", "websiteUrl": "https://mem.ai", "category": "AI for Productivity", "tags": ["Notes", "Knowledge", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Note Taking", "Knowledge Management", "Smart Search"], "logo": "https://logo.clearbit.com/mem.ai", "status": "published", "isTrending": false, "isNew": true, "views": 81, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000012f"}, "name": "Duolingo Max", "slug": "duolingo-max", "description": "AI-powered language learning platform with advanced features.", "websiteUrl": "https://duolingo.com", "category": "AI for Education", "tags": ["Education", "Language", "Learning"], "pricing": {"type": "enterprise", "startingPrice": 1299}, "features": ["Language Learning", "AI Conversations", "Personalized Learning"], "logo": "https://logo.clearbit.com/duolingo.com", "status": "published", "isTrending": false, "isNew": true, "views": 281, "votes": 620, "rating": 4.8, "reviews": 620, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000137"}, "name": "Codeium", "slug": "codeium-1", "description": "AI coding assistant for faster and more efficient development.", "websiteUrl": "https://codeium.com", "category": "AI for Development", "tags": ["Development", "Coding", "AI"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Code Completion", "Code Generation", "Documentation"], "logo": "https://logo.clearbit.com/codeium.com", "status": "published", "isTrending": false, "isNew": true, "views": 135, "votes": 360, "rating": 4.7, "reviews": 360, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000001"}, "name": "ChatGPT", "slug": "chatgpt", "description": "An AI assistant from OpenAI that excels in natural language processing, content generation, and coding assistance.", "websiteUrl": "https://chat.openai.com", "category": "AI Chatbots and Assistants", "tags": ["AI", "<PERSON><PERSON><PERSON>", "NLP"], "pricing": {"type": "freemium", "startingPrice": 20}, "features": ["Natural Language Processing", "Content Generation", "Code Assistance"], "logo": "https://logo.clearbit.com/chat.openai.com", "status": "published", "isTrending": true, "isNew": false, "views": 20, "votes": 1200, "rating": 4.8, "reviews": 1200, "__v": 0, "updatedAt": {"$date": "2025-04-17T09:03:14.801Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000007"}, "name": "DALL·E 3", "slug": "dall-e-3", "description": "An AI image generator that creates high-quality, detailed images from text prompts.", "websiteUrl": "https://openai.com/dall-e", "category": "AI for Image Generation", "tags": ["AI Art", "Creative Tools", "Design"], "pricing": {"type": "freemium", "startingPrice": 15115}, "features": ["Text-to-Image", "High-Quality Visuals", "Customizable"], "logo": "https://logo.clearbit.com/openai.com", "status": "published", "isTrending": true, "isNew": false, "views": 257, "votes": 327, "rating": 4.6, "reviews": 327, "__v": 0, "updatedAt": {"$date": "2025-04-17T01:52:38.729Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000010"}, "name": "Otter.ai", "slug": "otter-ai", "description": "An AI transcription tool for converting audio and video into text with high accuracy.", "websiteUrl": "https://otter.ai", "category": "AI for Transcription", "tags": ["AI Transcription", "Productivity", "Collaboration"], "pricing": {"type": "freemium", "startingPrice": 10}, "features": ["Audio Transcription", "Real-Time Notes", "Collaboration"], "logo": "https://logo.clearbit.com/otter.ai", "status": "published", "isTrending": true, "isNew": false, "views": 204, "votes": 180, "rating": 4.4, "reviews": 180, "__v": 0, "updatedAt": {"$date": "2025-04-12T10:53:00.691Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000003a"}, "name": "XOR", "slug": "xor", "description": "An AI-powered recruitment automation platform that streamlines candidate sourcing, screening, and engagement.", "websiteUrl": "https://www.xor.ai", "category": "AI for HR and Recruitment", "tags": ["AI Recruitment", "Automation", "HR Tech"], "pricing": {"type": "paid", "startingPrice": 15000}, "features": ["Candidate Sourcing", "Chatbot Screening", "Automated Engagement"], "logo": "https://logo.clearbit.com/xor.ai", "status": "published", "isTrending": true, "isNew": false, "views": 183, "votes": 140, "rating": 4.3, "reviews": 140, "__v": 0, "updatedAt": {"$date": "2025-04-12T00:51:57.917Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000040"}, "name": "Ideal", "slug": "ideal", "description": "An AI-powered recruitment automation platform that helps recruiters screen, rank, and engage candidates.", "websiteUrl": "https://www.ideal.com", "category": "AI for HR and Recruitment", "tags": ["AI Recruitment", "Automation", "HR Tech"], "pricing": {"type": "paid", "startingPrice": 15000}, "features": ["Candidate Screening", "Ranking Algorithms", "Automated Engagement"], "logo": "https://logo.clearbit.com/ideal.com", "status": "published", "isTrending": true, "isNew": false, "views": 184, "votes": 140, "rating": 4.4, "reviews": 140, "__v": 0, "updatedAt": {"$date": "2025-04-14T15:55:51.049Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000006e"}, "name": "RightBlogger", "slug": "rightblogger", "description": "AI-powered platform for optimizing blog content and improving content marketing strategies.", "websiteUrl": "https://rightblogger.com", "category": "AI for Content Creation", "tags": ["Blogging", "Content", "SEO"], "pricing": {"type": "paid", "startingPrice": 2499}, "features": ["Blog Optimization", "Content Strategy", "SEO Tools"], "logo": "https://logo.clearbit.com/rightblogger.com", "status": "published", "isTrending": false, "isNew": true, "views": 45, "votes": 150, "rating": 4.3, "reviews": 150, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000077"}, "name": "Steamship", "slug": "steamship", "description": "Platform for hosting and managing LangChain Apps with seamless deployment capabilities.", "websiteUrl": "https://steamship.com", "category": "AI for Coding and Development", "tags": ["Development", "Hosting", "AI"], "pricing": {"type": "paid", "startingPrice": 49}, "features": ["App Hosting", "LangChain Integration", "Deployment Tools"], "logo": "https://logo.clearbit.com/steamship.com", "status": "published", "isTrending": false, "isNew": true, "views": 65, "votes": 180, "rating": 4.5, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000009d"}, "name": "MusicGen Pro", "slug": "musicgen-pro", "description": "Professional AI music composition and arrangement tool.", "websiteUrl": "https://musicgenpro.ai", "category": "AI for Audio Creation", "tags": ["Music", "Audio", "Composition"], "pricing": {"type": "paid", "startingPrice": 2499}, "features": ["Music Composition", "Arrangement", "Instrument Selection"], "logo": "https://ui-avatars.com/api/?name=MusicGen%20Pro&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 80, "votes": 250, "rating": 4.5, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "00000000000000000000009f"}, "name": "LearnSmart AI", "slug": "learnsmart-ai", "description": "Personalized learning platform that adapts to individual student needs and learning styles.", "websiteUrl": "https://learnsmart.ai", "category": "AI for Education", "tags": ["Education", "Learning", "Personalization"], "pricing": {"type": "freemium", "startingPrice": 1299}, "features": ["Adaptive Learning", "Progress Tracking", "Personalized Content"], "logo": "https://logo.clearbit.com/learnsmart.ai", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 290, "rating": 4.7, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000a0"}, "name": "Gradio", "slug": "gradio", "description": "Platform for building and sharing machine learning applications with easy-to-use interfaces.", "websiteUrl": "https://gradio.app", "category": "AI for Coding and Development", "tags": ["ML", "Development", "AI"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["ML App Building", "Model Deployment", "Interface Creation"], "logo": "https://logo.clearbit.com/gradio.app", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000a6"}, "name": "SamurAI", "slug": "samurai", "description": "Advanced Discord bot for creating and managing communities with AI-powered features.", "websiteUrl": "https://samurai.bot", "category": "AI Chatbots and Assistants", "tags": ["Discord", "Bot", "Community"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Community Management", "Automated Moderation", "Custom Commands"], "logo": "https://ui-avatars.com/api/?name=SamurAI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 72, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000d3"}, "name": "Vizologi", "slug": "vizologi", "description": "AI chatbot for business strategy, planning, and competitor analysis.", "websiteUrl": "https://vizologi.com", "category": "AI Chatbots and Assistants", "tags": ["Business", "Strategy", "Analysis"], "pricing": {"type": "paid", "startingPrice": 3499}, "features": ["Business Planning", "Market Analysis", "Strategy Development"], "logo": "https://logo.clearbit.com/vizologi.com", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 260, "rating": 4.6, "reviews": 260, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000e1"}, "name": "Speak", "slug": "speak", "description": "AI language learning app for practicing conversations and improving pronunciation.", "websiteUrl": "https://speak.com", "category": "AI for Education", "tags": ["Language", "Learning", "Education"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Language Practice", "Pronunciation Feedback", "Conversation AI"], "logo": "https://logo.clearbit.com/speak.com", "status": "published", "isTrending": false, "isNew": true, "views": 131, "votes": 360, "rating": 4.8, "reviews": 360, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000ef"}, "name": "Hoppy Copy", "slug": "hoppy-copy-1", "description": "AI platform for creating engaging email and ad copy that converts.", "websiteUrl": "https://hoppycopy.co", "category": "AI for Content Creation", "tags": ["Email", "Copywriting", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Email Copy Generation", "Template Library", "A/B Testing"], "logo": "https://logo.clearbit.com/hoppycopy.co", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 220, "rating": 4.5, "reviews": 220, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "00000000000000000000011a"}, "name": "Anthropic <PERSON>", "slug": "anthropic-claude", "description": "Advanced AI language model for research and analysis.", "websiteUrl": "https://anthropic.ai", "category": "AI Chatbots and Assistants", "tags": ["AI", "Research", "Writing"], "pricing": {"type": "paid", "startingPrice": 20}, "features": ["Research Analysis", "Content Generation", "Complex Problem Solving"], "logo": "https://logo.clearbit.com/anthropic.ai", "status": "published", "isTrending": false, "isNew": true, "views": 123, "votes": 380, "rating": 4.9, "reviews": 380, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000012c"}, "name": "Gamma", "slug": "gamma-1", "description": "AI presentation platform for creating beautiful decks and documents.", "websiteUrl": "https://gamma.app", "category": "AI for Presentations", "tags": ["Presentations", "Design", "Collaboration"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Presentation Design", "Content Generation", "Collaboration"], "logo": "https://logo.clearbit.com/gamma.app", "status": "published", "isTrending": false, "isNew": true, "views": 92, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000135"}, "name": "Aidungeon", "slug": "<PERSON><PERSON><PERSON>", "description": "AI-powered text adventure game with infinite storytelling possibilities.", "websiteUrl": "https://aidungeon.io", "category": "AI for Gaming", "tags": ["Gaming", "Stories", "Entertainment"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Story Generation", "Interactive Fiction", "Game Creation"], "logo": "https://logo.clearbit.com/aidungeon.io", "status": "published", "isTrending": false, "isNew": true, "views": 165, "votes": 420, "rating": 4.5, "reviews": 420, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000141"}, "name": "Heygen", "slug": "heygen", "description": "AI video creation platform with virtual avatars and presenters.", "websiteUrl": "https://heygen.com", "category": "AI for Video", "tags": ["Video", "Avatars", "AI"], "pricing": {"type": "paid", "startingPrice": 29}, "features": ["Avatar Creation", "Video Generation", "Voice Synthesis"], "logo": "https://logo.clearbit.com/heygen.com", "status": "published", "isTrending": false, "isNew": true, "views": 115, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000142"}, "name": "Deepbrain AI", "slug": "deepbrain-ai-1", "description": "AI platform for creating virtual humans and presenters.", "websiteUrl": "https://deepbrain.io", "category": "AI for Video", "tags": ["Video", "AI", "Virtual Humans"], "pricing": {"type": "enterprise", "startingPrice": 0}, "features": ["Virtual Humans", "Video Creation", "Voice Synthesis"], "logo": "https://logo.clearbit.com/deepbrain.io", "status": "published", "isTrending": false, "isNew": true, "views": 96, "votes": 280, "rating": 4.8, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000155"}, "name": "Craft", "slug": "craft", "description": "Document editor and knowledge base with AI capabilities.", "websiteUrl": "https://craft.do", "category": "AI for Productivity", "tags": ["Documents", "Writing", "Knowledge"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Document Editing", "Knowledge Management", "AI Writing"], "logo": "https://logo.clearbit.com/craft.do", "status": "published", "isTrending": false, "isNew": true, "views": 134, "votes": 380, "rating": 4.7, "reviews": 380, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000000f"}, "name": "Descript", "slug": "descript", "description": "An AI-powered video and audio editing tool that allows editing via text transcripts.", "websiteUrl": "https://www.descript.com", "category": "AI for Video Editing", "tags": ["AI Video", "Editing", "Podcasting"], "pricing": {"type": "freemium", "startingPrice": 15}, "features": ["Text-Based Editing", "Audio Editing", "Video Editing"], "logo": "https://logo.clearbit.com/descript.com", "status": "published", "isTrending": true, "isNew": false, "views": 257, "votes": 190, "rating": 4.5, "reviews": 190, "__v": 0, "updatedAt": {"$date": "2025-04-14T13:30:58.114Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000022"}, "name": "DeepArt", "slug": "deepart", "description": "An AI tool that transforms photos into artwork using various artistic styles.", "websiteUrl": "https://deepart.io", "category": "AI for Image Generation", "tags": ["AI Art", "Creative Tools", "Image Editing"], "pricing": {"type": "freemium", "startingPrice": 199}, "features": ["Artistic Filters", "Custom Styles", "High-Quality Output"], "logo": "https://logo.clearbit.com/deepart.io", "status": "published", "isTrending": true, "isNew": false, "views": 102, "votes": 90, "rating": 4.1, "reviews": 90, "__v": 0, "updatedAt": {"$date": "2025-04-11T15:08:18.385Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000003d"}, "name": "Entelo", "slug": "entelo", "description": "An AI-powered recruitment platform that helps recruiters source, engage, and hire top talent.", "websiteUrl": "https://www.entelo.com", "category": "AI for HR and Recruitment", "tags": ["AI Recruitment", "Candidate Sourcing", "HR Tech"], "pricing": {"type": "paid", "startingPrice": 12000}, "features": ["Candidate Sourcing", "Diversity Hiring", "Engagement Tools"], "logo": "https://logo.clearbit.com/entelo.com", "status": "published", "isTrending": true, "isNew": false, "views": 174, "votes": 130, "rating": 4.3, "reviews": 130, "__v": 0, "updatedAt": {"$date": "2025-04-14T17:59:56.639Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000044"}, "name": "Vue.ai", "slug": "vue-ai", "description": "An AI-powered platform for fashion e-commerce that helps businesses automate product tagging, recommendations, and personalization.", "websiteUrl": "https://www.vue.ai", "category": "AI for E-commerce", "tags": ["AI E-commerce", "Fashion", "Personalization"], "pricing": {"type": "paid", "startingPrice": 500}, "features": ["Product Tagging", "Personalized Recommendations", "Visual Search"], "logo": "https://logo.clearbit.com/vue.ai", "status": "published", "isTrending": true, "isNew": false, "views": 302, "votes": 150, "rating": 4.3, "reviews": 150, "__v": 0, "updatedAt": {"$date": "2025-04-11T08:38:40.097Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000045"}, "name": "Clerk.io", "slug": "clerk-io", "description": "An AI-powered product recommendation engine that helps e-commerce businesses increase sales through personalized recommendations.", "websiteUrl": "https://www.clerk.io", "category": "AI for E-commerce", "tags": ["AI E-commerce", "Product Recommendations", "Personalization"], "pricing": {"type": "paid", "startingPrice": 99}, "features": ["Product Recommendations", "Personalization", "Real-Time Analytics"], "logo": "https://logo.clearbit.com/clerk.io", "status": "published", "isTrending": true, "isNew": false, "views": 322, "votes": 170, "rating": 4.4, "reviews": 170, "__v": 0, "updatedAt": {"$date": "2025-04-14T22:04:02.741Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000046"}, "name": "Algolia", "slug": "algolia", "description": "An AI-powered search and discovery platform that helps e-commerce businesses deliver fast and relevant search results.", "websiteUrl": "https://www.algolia.com", "category": "AI for E-commerce", "tags": ["AI E-commerce", "Search", "Personalization"], "pricing": {"type": "freemium", "startingPrice": 29}, "features": ["Search Optimization", "Personalized Results", "Real-Time Analytics"], "logo": "https://logo.clearbit.com/algolia.com", "status": "published", "isTrending": true, "isNew": false, "views": 453, "votes": 220, "rating": 4.6, "reviews": 220, "__v": 0, "updatedAt": {"$date": "2025-04-14T05:56:23.118Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000004a"}, "name": "<PERSON><PERSON><PERSON>", "slug": "yotpo", "description": "An AI-powered platform that helps e-commerce businesses collect and leverage customer reviews for marketing and sales growth.", "websiteUrl": "https://www.yotpo.com", "category": "AI for E-commerce", "tags": ["AI E-commerce", "Reviews", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 19}, "features": ["Review Collection", "Review Marketing", "Customer Insights"], "logo": "https://logo.clearbit.com/yotpo.com", "status": "published", "isTrending": true, "isNew": false, "views": 381, "votes": 190, "rating": 4.5, "reviews": 190, "__v": 0, "updatedAt": {"$date": "2025-02-24T15:30:52.123Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000004e"}, "name": "Claude 3", "slug": "claude-3-1", "description": "Next generation AI assistant with enhanced reasoning and capabilities", "websiteUrl": "https://anthropic.com", "category": "AI Chatbots and Assistants", "tags": ["AI", "Language Model", "Enterprise"], "pricing": {"type": "paid", "startingPrice": 0}, "features": ["Enhanced Reasoning", "Improved Context Understanding", "Advanced Analysis"], "logo": "https://logo.clearbit.com/anthropic.com", "status": "published", "isTrending": false, "isNew": false, "views": 90, "votes": 0, "rating": 0, "reviews": 0, "__v": 0, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "000000000000000000000051"}, "name": "CodePilot AI", "slug": "codepilot-ai", "description": "Revolutionary AI coding assistant with advanced pair programming capabilities", "websiteUrl": "https://codepilot.ai", "category": "AI for Development", "tags": ["AI Coding", "Development", "Programming"], "pricing": {"type": "paid", "startingPrice": 20}, "features": ["Real-time Coding Assistance", "Code Review", "Test Generation"], "logo": "https://logo.clearbit.com/codepilot.ai", "status": "published", "isTrending": false, "isNew": false, "views": 45, "votes": 0, "rating": 0, "reviews": 0, "__v": 0, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "00000000000000000000006f"}, "name": "Obituary Writer", "slug": "obituary-writer", "description": "Sensitive AI tool for crafting respectful and personalized obituaries with care.", "websiteUrl": "https://obituarywriter.ai", "category": "AI for Content Creation", "tags": ["Writing", "Memorial", "Personal"], "pricing": {"type": "paid", "startingPrice": 999}, "features": ["Personalized Writing", "Respectful Tone", "Template Library"], "logo": "https://ui-avatars.com/api/?name=Obituary%20Writer&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 290, "rating": 4.7, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000007b"}, "name": "<PERSON>", "slug": "get-chunky", "description": "Intuitive platform for creating and customizing AI-powered chatbots for various purposes.", "websiteUrl": "https://getchunky.ai", "category": "AI Chatbots and Assistants", "tags": ["<PERSON><PERSON><PERSON>", "Automation", "Support"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Chatbot Creation", "Customization", "Integration"], "logo": "https://ui-avatars.com/api/?name=Get%20Chunky&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 45, "votes": 150, "rating": 4.3, "reviews": 150, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000082"}, "name": "FigCopy", "slug": "figcopy", "description": "Figma Plugin that generates automated UI designs with AI-powered suggestions.", "websiteUrl": "https://figcopy.design", "category": "AI for Design", "tags": ["Design", "Figma", "UI"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["UI Design", "Design Automation", "Figma Integration"], "logo": "https://ui-avatars.com/api/?name=FigCopy&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 45, "votes": 140, "rating": 4.3, "reviews": 140, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000008f"}, "name": "Cloudinary", "slug": "cloudinary", "description": "Comprehensive APIs for developing AI-powered art software and image manipulation tools.", "websiteUrl": "https://cloudinary.com", "category": "AI for Image Generation", "tags": ["API", "Images", "Development"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Image Processing", "AI Integration", "API Tools"], "logo": "https://logo.clearbit.com/cloudinary.com", "status": "published", "isTrending": false, "isNew": true, "views": 150, "votes": 420, "rating": 4.8, "reviews": 420, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "000000000000000000000094"}, "name": "<PERSON><PERSON><PERSON>", "slug": "voila", "description": "Chrome Extension for automatically creating copy, summarizing text, translating, and responding to emails.", "websiteUrl": "https://voila.ai", "category": "AI for Productivity", "tags": ["Chrome Extension", "Writing", "Translation"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Text Generation", "Translation", "Email Response"], "logo": "https://ui-avatars.com/api/?name=Voila&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 66, "votes": 190, "rating": 4.5, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000a2"}, "name": "TED SMRZR", "slug": "ted-smrzr", "description": "AI-powered tool that generates concise summaries of TED Talks for quick insights.", "websiteUrl": "https://tedsmrzr.com", "category": "AI for Research", "tags": ["TED Talks", "Summaries", "Education"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Talk Summarization", "Key Points Extraction", "Quick Insights"], "logo": "https://ui-avatars.com/api/?name=TED%20SMRZR&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 60, "votes": 180, "rating": 4.4, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000b3"}, "name": "MagickPen", "slug": "magickpen", "description": "Comprehensive AI writing assistant for various content types and formats.", "websiteUrl": "https://magickpen.com", "category": "AI for Productivity", "tags": ["Writing", "Content", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Content Writing", "Editing", "Format Conversion"], "logo": "https://logo.clearbit.com/magickpen.com", "status": "published", "isTrending": false, "isNew": true, "views": 76, "votes": 230, "rating": 4.5, "reviews": 230, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000b5"}, "name": "DiscuroAI", "slug": "discuroai", "description": "Platform for rapidly building and testing complex AI workflows and integrations.", "websiteUrl": "https://discuro.ai", "category": "AI for Coding and Development", "tags": ["Development", "AI", "Workflow"], "pricing": {"type": "paid", "startingPrice": 4999}, "features": ["Workflow Building", "AI Integration", "Testing Tools"], "logo": "https://ui-avatars.com/api/?name=DiscuroAI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 80, "votes": 250, "rating": 4.6, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000ba"}, "name": "LALAL.AI", "slug": "lalal-ai", "description": "Professional AI-powered vocal remover and music source separation tool.", "websiteUrl": "https://lalal.ai", "category": "AI for Audio Creation", "tags": ["Audio", "Music", "Production"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Vocal Removal", "Source Separation", "Audio Processing"], "logo": "https://logo.clearbit.com/lalal.ai", "status": "published", "isTrending": false, "isNew": true, "views": 120, "votes": 350, "rating": 4.8, "reviews": 350, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000be"}, "name": "Peppertype", "slug": "peppertype", "description": "AI-powered tool for generating marketing sales copy and engaging blog content.", "websiteUrl": "https://peppertype.ai", "category": "AI for Content Creation", "tags": ["Marketing", "Content", "Copywriting"], "pricing": {"type": "freemium", "startingPrice": 2499}, "features": ["Sales Copy", "Blog Writing", "Content Generation"], "logo": "https://logo.clearbit.com/peppertype.ai", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 260, "rating": 4.6, "reviews": 260, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000d0"}, "name": "STRING", "slug": "string", "description": "Data analysis platform for creating insightful visualizations and extracting data insights.", "websiteUrl": "https://string.ai", "category": "AI for Business", "tags": ["Data", "Analytics", "Business"], "pricing": {"type": "paid", "startingPrice": 3999}, "features": ["Data Analysis", "Visualization", "Insights Generation"], "logo": "https://logo.clearbit.com/string.ai", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 250, "rating": 4.6, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000da"}, "name": "Descript", "slug": "descript-1", "description": "All-in-one video and audio editing platform powered by AI.", "websiteUrl": "https://descript.com", "category": "AI for Video Editing", "tags": ["Video", "Audio", "Editing"], "pricing": {"type": "freemium", "startingPrice": 12}, "features": ["Video Editing", "Audio Editing", "Transcription"], "logo": "https://logo.clearbit.com/descript.com", "status": "published", "isTrending": false, "isNew": true, "views": 130, "votes": 350, "rating": 4.8, "reviews": 350, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000dd"}, "name": "Runway", "slug": "runway", "description": "Creative suite with AI tools for video editing and visual effects.", "websiteUrl": "https://runway.ml", "category": "AI for Video Editing", "tags": ["Video", "Effects", "Editing"], "pricing": {"type": "paid", "startingPrice": 15}, "features": ["Video Effects", "Motion Graphics", "Green Screen"], "logo": "https://logo.clearbit.com/runway.ml", "status": "published", "isTrending": false, "isNew": true, "views": 120, "votes": 340, "rating": 4.7, "reviews": 340, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000e3"}, "name": "CodeCraft AI", "slug": "codecraft-ai-1", "description": "AI-powered code generation and optimization tool for developers.", "websiteUrl": "https://codecraft.ai", "category": "AI for Coding and Development", "tags": ["Coding", "Development", "Programming"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Code Generation", "Code Optimization", "Bug Detection"], "logo": "https://logo.clearbit.com/codecraft.ai", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000e7"}, "name": "Gradio", "slug": "gradio-1", "description": "Platform for building and sharing machine learning applications with easy-to-use interfaces.", "websiteUrl": "https://gradio.app", "category": "AI for Coding and Development", "tags": ["ML", "Development", "AI"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["ML App Building", "Model Deployment", "Interface Creation"], "logo": "https://logo.clearbit.com/gradio.app", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000f3"}, "name": "Storyd", "slug": "storyd-1", "description": "AI-powered platform for creating compelling data presentations and visualizations.", "websiteUrl": "https://storyd.ai", "category": "AI for Productivity", "tags": ["Data", "Visualization", "Presentations"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Data Visualization", "Presentation Creation", "Story Templates"], "logo": "https://logo.clearbit.com/storyd.ai", "status": "published", "isTrending": false, "isNew": true, "views": 72, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000f6"}, "name": "Optimo", "slug": "optimo", "description": "Comprehensive AI tool for marketing-related tasks and campaign optimization.", "websiteUrl": "https://optimo.ai", "category": "AI for Marketing", "tags": ["Marketing", "Analytics", "Optimization"], "pricing": {"type": "paid", "startingPrice": 4999}, "features": ["Campaign Optimization", "Marketing Analytics", "Performance Tracking"], "logo": "https://ui-avatars.com/api/?name=Optimo&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000106"}, "name": "Replit AI", "slug": "replit-ai-2", "description": "AI-powered coding companion integrated with Replit IDE.", "websiteUrl": "https://replit.com", "category": "AI for Coding and Development", "tags": ["Coding", "Development", "Education"], "pricing": {"type": "freemium", "startingPrice": 10}, "features": ["Code Completion", "Error Detection", "Code Generation"], "logo": "https://logo.clearbit.com/replit.com", "status": "published", "isTrending": false, "isNew": true, "views": 122, "votes": 380, "rating": 4.7, "reviews": 380, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000010c"}, "name": "Spatial.ai", "slug": "spatial-ai-3", "description": "AI-powered geosocial intelligence platform for business insights.", "websiteUrl": "https://spatial.ai", "category": "AI for Business", "tags": ["Business", "Analytics", "Location"], "pricing": {"type": "enterprise", "startingPrice": 499}, "features": ["Location Analytics", "Market Research", "Consumer Insights"], "logo": "https://logo.clearbit.com/spatial.ai", "status": "published", "isTrending": false, "isNew": true, "views": 56, "votes": 180, "rating": 4.7, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000011c"}, "name": "Midjourney", "slug": "midjourney-1", "description": "Advanced AI image generation platform for creating stunning artwork and visuals.", "websiteUrl": "https://midjourney.com", "category": "AI for Image Generation", "tags": ["Art", "Design", "AI"], "pricing": {"type": "paid", "startingPrice": 10}, "features": ["Image Generation", "Art Creation", "Style Control"], "logo": "https://logo.clearbit.com/midjourney.com", "status": "published", "isTrending": false, "isNew": true, "views": 204, "votes": 580, "rating": 4.9, "reviews": 580, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000011d"}, "name": "Stable Diffusion", "slug": "stable-diffusion-1", "description": "Open-source AI model for generating high-quality images from text descriptions.", "websiteUrl": "https://stability.ai", "category": "AI for Image Generation", "tags": ["AI", "Image Generation", "Open Source"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Text-to-Image", "Image Editing", "Model Training"], "logo": "https://logo.clearbit.com/stability.ai", "status": "published", "isTrending": false, "isNew": true, "views": 181, "votes": 520, "rating": 4.8, "reviews": 520, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000121"}, "name": "<PERSON>", "slug": "claude", "description": "Advanced AI language model for writing, analysis, and coding assistance.", "websiteUrl": "https://anthropic.com/claude", "category": "AI for Writing and Analysis", "tags": ["AI", "Writing", "Programming"], "pricing": {"type": "paid", "startingPrice": 20}, "features": ["Text Generation", "Code Assistance", "Analysis"], "logo": "https://logo.clearbit.com/anthropic.com", "status": "published", "isTrending": false, "isNew": true, "views": 253, "votes": 620, "rating": 4.9, "reviews": 620, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000014a"}, "name": "Ideogram", "slug": "ideogram", "description": "AI art generation platform with advanced text-to-image capabilities.", "websiteUrl": "https://ideogram.ai", "category": "AI for Art", "tags": ["Art", "AI", "Design"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Image Generation", "Style Control", "Text to Image"], "logo": "https://logo.clearbit.com/ideogram.ai", "status": "published", "isTrending": false, "isNew": true, "views": 101, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000005"}, "name": "Gemini AI", "slug": "gemini-ai", "description": "Google's AI assistant that integrates with Google services for real-time data and advanced reasoning.", "websiteUrl": "https://www.google.com/gemini", "category": "AI Chatbots and Assistants", "tags": ["AI Assistant", "NLP", "Productivity"], "pricing": {"type": "paid", "startingPrice": 1999}, "features": ["Real-Time Data", "Advanced Reasoning", "Google Integration"], "logo": "https://logo.clearbit.com/google.com", "status": "published", "isTrending": true, "isNew": true, "views": 255, "votes": 542, "rating": 4.4, "reviews": 542, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.015Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.015Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000000c"}, "name": "Runway ML", "slug": "runway-ml", "description": "A creative suite for AI-powered video and image editing, including text-to-video generation.", "websiteUrl": "https://runwayml.com", "category": "AI for Video Generation", "tags": ["AI Video", "Creative Tools", "Editing"], "pricing": {"type": "freemium", "startingPrice": 15}, "features": ["Video Editing", "Text-to-Video", "AI Effects"], "logo": "https://logo.clearbit.com/runwayml.com", "status": "published", "isTrending": true, "isNew": false, "views": 505, "votes": 320, "rating": 4.5, "reviews": 320, "__v": 0, "updatedAt": {"$date": "2025-04-15T03:40:31.301Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000004c"}, "name": "GPT-5", "slug": "gpt-5", "description": "OpenAI's next major language model with breakthrough capabilities", "websiteUrl": "https://openai.com", "category": "AI Chatbots and Assistants", "tags": ["AI", "Language Model", "Enterprise"], "pricing": {"type": "paid", "startingPrice": 0}, "features": ["Advanced Intelligence", "Multimodal Processing", "Enhanced Understanding"], "logo": "https://logo.clearbit.com/openai.com", "status": "published", "isTrending": false, "isNew": false, "views": 102, "votes": 0, "rating": 0, "reviews": 0, "__v": 0, "updatedAt": {"$date": "2025-04-17T03:46:53.589Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000004f"}, "name": "AutoGPT 2.0", "slug": "autogpt-2-0", "description": "Autonomous AI agent with improved task completion capabilities", "websiteUrl": "https://autogpt.net", "category": "AI Automation", "tags": ["AI", "Automation", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Autonomous Operation", "Task Automation", "Self-Improvement"], "logo": "https://logo.clearbit.com/autogpt.net", "status": "published", "isTrending": false, "isNew": false, "views": 60, "votes": 0, "rating": 0, "reviews": 0, "__v": 0, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "000000000000000000000068"}, "name": "Lightning AI", "slug": "lightning-ai", "description": "Comprehensive platform for building, training and deploying AI products with ease.", "websiteUrl": "https://lightning.ai", "category": "AI for Coding and Development", "tags": ["Development", "AI", "Machine Learning"], "pricing": {"type": "freemium", "startingPrice": 49}, "features": ["AI Development", "Model Training", "Deployment Tools"], "logo": "https://logo.clearbit.com/lightning.ai", "status": "published", "isTrending": false, "isNew": true, "views": 90, "votes": 280, "rating": 4.7, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000080"}, "name": "<PERSON>bble", "slug": "hubble", "description": "Efficient platform for collecting and analyzing user feedback with AI insights.", "websiteUrl": "https://hubble.ai", "category": "AI for Marketing", "tags": ["<PERSON><PERSON><PERSON>", "Analytics", "Research"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Feedback Collection", "Analysis Tools", "Insight Generation"], "logo": "https://logo.clearbit.com/hubble.ai", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 200, "rating": 4.5, "reviews": 200, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "0000000000000000000000ab"}, "name": "PrompBase", "slug": "prompbase", "description": "Specialized tool for generating and customizing Midjourney prompts for better results.", "websiteUrl": "https://promptbase.com", "category": "AI for Image Generation", "tags": ["Prompts", "Midjourney", "Art"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Prompt Generation", "Customization Tools", "Style Guidelines"], "logo": "https://logo.clearbit.com/promptbase.com", "status": "published", "isTrending": false, "isNew": true, "views": 65, "votes": 190, "rating": 4.4, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000c1"}, "name": "Taskade", "slug": "taskade", "description": "AI-enhanced collaborative productivity platform for team project management.", "websiteUrl": "https://taskade.com", "category": "AI for Productivity", "tags": ["Productivity", "Collaboration", "Management"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Project Management", "Team Collaboration", "Task Automation"], "logo": "https://logo.clearbit.com/taskade.com", "status": "published", "isTrending": true, "isNew": true, "views": 173, "votes": 420, "rating": 4.7, "reviews": 420, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000d5"}, "name": "AV Mapping", "slug": "av-mapping", "description": "Advanced tool for music search and video music mapping with AI assistance.", "websiteUrl": "https://avmapping.com", "category": "AI for Audio Creation", "tags": ["Music", "Video", "Audio"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Music Search", "Video Mapping", "Audio Sync"], "logo": "https://ui-avatars.com/api/?name=AV%20Mapping&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 220, "rating": 4.5, "reviews": 220, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000d8"}, "name": "<PERSON><PERSON>", "slug": "murf", "description": "Professional AI voice generator for creating natural-sounding voiceovers.", "websiteUrl": "https://murf.ai", "category": "AI for Audio Creation", "tags": ["Voice", "Audio", "Text to Speech"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Voice Generation", "Text to Speech", "Voice Customization"], "logo": "https://logo.clearbit.com/murf.ai", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000dc"}, "name": "<PERSON><PERSON>", "slug": "krisp-1", "description": "AI-powered noise cancellation app for clear audio in calls and recordings.", "websiteUrl": "https://krisp.ai", "category": "AI for Audio Creation", "tags": ["Audio", "Noise Cancellation", "Voice"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Noise Cancellation", "Voice Enhancement", "Echo Removal"], "logo": "https://logo.clearbit.com/krisp.ai", "status": "published", "isTrending": false, "isNew": true, "views": 140, "votes": 380, "rating": 4.8, "reviews": 380, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000109"}, "name": "DataViz AI", "slug": "dataviz-ai-3", "description": "AI-powered data visualization and analysis platform.", "websiteUrl": "https://dataviz.ai", "category": "AI for Business", "tags": ["Data", "Analytics", "Business"], "pricing": {"type": "paid", "startingPrice": 7999}, "features": ["Data Visualization", "Analytics", "Reporting"], "logo": "https://ui-avatars.com/api/?name=DataViz%20AI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 71, "votes": 230, "rating": 4.7, "reviews": 230, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000010a"}, "name": "Writesonic", "slug": "writesonic-3", "description": "AI writing assistant for creating high-quality content.", "websiteUrl": "https://writesonic.com", "category": "AI for Writing", "tags": ["Writing", "Content", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 1299}, "features": ["Content Generation", "Article Writing", "Marketing Copy"], "logo": "https://logo.clearbit.com/writesonic.com", "status": "published", "isTrending": false, "isNew": true, "views": 105, "votes": 350, "rating": 4.6, "reviews": 350, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "00000000000000000000010f"}, "name": "Photosonic", "slug": "photosonic-2", "description": "AI image generator with advanced style and composition controls.", "websiteUrl": "https://photosonic.ai", "category": "AI for Image Generation", "tags": ["AI Art", "Image Generation", "Design"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Image Generation", "Style Control", "Composition Tools"], "logo": "https://logo.clearbit.com/photosonic.ai", "status": "published", "isTrending": false, "isNew": true, "views": 92, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000110"}, "name": "MagickPen", "slug": "magickpen-2", "description": "Comprehensive AI writing assistant for various content types.", "websiteUrl": "https://magickpen.ai", "category": "AI for Productivity", "tags": ["Writing", "Content", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Content Writing", "Grammar Check", "Style Suggestions"], "logo": "https://ui-avatars.com/api/?name=MagickPen&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 67, "votes": 200, "rating": 4.5, "reviews": 200, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000129"}, "name": "Hugging Face", "slug": "hugging-face-1", "description": "Platform for sharing and deploying machine learning models.", "websiteUrl": "https://huggingface.co", "category": "AI Development", "tags": ["ML", "Development", "AI"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Model Hub", "Model Training", "Deployment"], "logo": "https://logo.clearbit.com/huggingface.co", "status": "published", "isTrending": false, "isNew": true, "views": 251, "votes": 580, "rating": 4.9, "reviews": 580, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000013c"}, "name": "Copilot", "slug": "copilot", "description": "GitHub's AI pair programmer for code suggestions.", "websiteUrl": "https://github.com/features/copilot", "category": "AI for Development", "tags": ["Development", "GitHub", "AI"], "pricing": {"type": "paid", "startingPrice": 10}, "features": ["Code Generation", "Code Completion", "Documentation"], "logo": "https://logo.clearbit.com/github.com", "status": "published", "isTrending": false, "isNew": true, "views": 403, "votes": 820, "rating": 4.9, "reviews": 820, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000013e"}, "name": "Sourcegraph", "slug": "sourcegraph", "description": "AI-powered code search and intelligence platform.", "websiteUrl": "https://sourcegraph.com", "category": "AI for Development", "tags": ["Development", "Search", "Code"], "pricing": {"type": "enterprise", "startingPrice": 0}, "features": ["Code Search", "Code Intelligence", "Code Navigation"], "logo": "https://logo.clearbit.com/sourcegraph.com", "status": "published", "isTrending": false, "isNew": true, "views": 111, "votes": 320, "rating": 4.8, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000014c"}, "name": "Getimg.ai", "slug": "getimg-ai", "description": "AI image generation and editing platform with multiple models.", "websiteUrl": "https://getimg.ai", "category": "AI for Art", "tags": ["Art", "AI", "Images"], "pricing": {"type": "paid", "startingPrice": 10}, "features": ["Image Generation", "Image Editing", "Model Selection"], "logo": "https://logo.clearbit.com/getimg.ai", "status": "published", "isTrending": false, "isNew": true, "views": 81, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000014f"}, "name": "Todoist AI", "slug": "todoist-ai", "description": "AI-enhanced task management and productivity app.", "websiteUrl": "https://todoist.com", "category": "AI for Productivity", "tags": ["Productivity", "Tasks", "AI"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Task Management", "<PERSON> Scheduling", "AI Organization"], "logo": "https://logo.clearbit.com/todoist.com", "status": "published", "isTrending": false, "isNew": true, "views": 281, "votes": 620, "rating": 4.8, "reviews": 620, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000157"}, "name": "RemNote", "slug": "remnote", "description": "AI-powered note-taking and learning tool.", "websiteUrl": "https://remnote.com", "category": "AI for Education", "tags": ["Education", "Notes", "Learning"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Note Taking", "Spaced Repetition", "AI Learning"], "logo": "https://logo.clearbit.com/remnote.com", "status": "published", "isTrending": false, "isNew": true, "views": 112, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000016"}, "name": "DeepBrain AI", "slug": "deepbrain-ai", "description": "An AI video generator that creates videos with digital avatars for training, marketing, and more.", "websiteUrl": "https://www.deepbrain.io", "category": "AI for Video Generation", "tags": ["AI Video", "Creative Tools", "Marketing"], "pricing": {"type": "paid", "startingPrice": 30}, "features": ["AI Avatars", "Text-to-Video", "Multilingual Support"], "logo": "https://logo.clearbit.com/deepbrain.io", "status": "published", "isTrending": true, "isNew": true, "views": 137, "votes": 90, "rating": 4.3, "reviews": 90, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.015Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.015Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000003c"}, "name": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON>", "description": "An AI-powered candidate sourcing platform that helps recruiters find and engage top talent across multiple channels.", "websiteUrl": "https://www.hiretual.com", "category": "AI for HR and Recruitment", "tags": ["AI Recruitment", "Candidate Sourcing", "HR Tech"], "pricing": {"type": "paid", "startingPrice": 10000}, "features": ["Candidate Sourcing", "Talent Pool Building", "AI Matching"], "logo": "https://logo.clearbit.com/hiretual.com", "status": "published", "isTrending": true, "isNew": false, "views": 204, "votes": 150, "rating": 4.4, "reviews": 150, "__v": 0, "updatedAt": {"$date": "2025-04-12T06:05:00.127Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000041"}, "name": "Shopify AI", "slug": "shopify-ai", "description": "An AI-powered e-commerce optimization tool that helps businesses improve their online store performance and customer experience.", "websiteUrl": "https://www.shopify.com", "category": "AI for E-commerce", "tags": ["AI E-commerce", "Shopify", "Optimization"], "pricing": {"type": "paid", "startingPrice": 29}, "features": ["Store Optimization", "Customer Insights", "Personalized Recommendations"], "logo": "https://logo.clearbit.com/shopify.com", "status": "published", "isTrending": true, "isNew": false, "views": 502, "votes": 250, "rating": 4.6, "reviews": 250, "__v": 0, "updatedAt": {"$date": "2025-04-17T16:45:44.413Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000070"}, "name": "MagicPic", "slug": "magicpic", "description": "Advanced AI avatar and profile picture generator with customizable styles and effects.", "websiteUrl": "https://magicpic.ai", "category": "AI for Image Generation", "tags": ["Avatar", "Image Generation", "Profile"], "pricing": {"type": "freemium", "startingPrice": 499}, "features": ["Avatar Generation", "Style Customization", "Batch Processing"], "logo": "https://ui-avatars.com/api/?name=MagicPic&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 120, "votes": 310, "rating": 4.6, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000007f"}, "name": "MARA", "slug": "mara", "description": "AI tool for generating personalized responses to reviews in multiple languages.", "websiteUrl": "https://mara.ai", "category": "AI for Marketing", "tags": ["Reviews", "Customer Service", "Multi-language"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Review Response", "Multi-language Support", "Sentiment Analysis"], "logo": "https://logo.clearbit.com/mara.ai", "status": "published", "isTrending": false, "isNew": true, "views": 50, "votes": 160, "rating": 4.4, "reviews": 160, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000008e"}, "name": "EssayGrader", "slug": "essaygrader", "description": "AI tool for teachers to grade essays with automated summarization, spelling and grammar checks, and feedback.", "websiteUrl": "https://essaygrader.ai", "category": "AI for Education", "tags": ["Education", "Grading", "Essays"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Essay Grading", "Feedback Generation", "Grammar Check"], "logo": "https://logo.clearbit.com/essaygrader.ai", "status": "published", "isTrending": false, "isNew": true, "views": 76, "votes": 230, "rating": 4.6, "reviews": 230, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000099"}, "name": "Lexica Art", "slug": "lexica-art", "description": "Advanced AI art generation platform with unique style controls and high-resolution output.", "websiteUrl": "https://lexica.art", "category": "AI for Image Generation", "tags": ["Art", "Design", "Creative"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Image Generation", "Style Control", "High Resolution"], "logo": "https://logo.clearbit.com/lexica.art", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "00000000000000000000009c"}, "name": "CodeCraft AI", "slug": "codecraft-ai", "description": "AI-powered code generation and optimization tool for developers.", "websiteUrl": "https://codecraft.ai", "category": "AI for Coding and Development", "tags": ["Coding", "Development", "Programming"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Code Generation", "Code Optimization", "Bug Detection"], "logo": "https://logo.clearbit.com/codecraft.ai", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000a4"}, "name": "Releasenote.ai", "slug": "releasenote-ai", "description": "AI-powered tool for crafting professional and engaging release notes using GPT-3.", "websiteUrl": "https://releasenote.ai", "category": "AI for Content Creation", "tags": ["Release Notes", "Documentation", "Development"], "pricing": {"type": "paid", "startingPrice": 2499}, "features": ["Release Note Generation", "Version Tracking", "Customization"], "logo": "https://ui-avatars.com/api/?name=Releasenote.ai&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 82, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000aa"}, "name": "Figmill", "slug": "figmill", "description": "Professional AI-powered tool for creating stunning headshots and portraits.", "websiteUrl": "https://figmill.com", "category": "AI for Image Improvement", "tags": ["Headshots", "Photography", "Portraits"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Headshot Generation", "Portrait Enhancement", "Style Customization"], "logo": "https://ui-avatars.com/api/?name=Figmill&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 90, "votes": 270, "rating": 4.6, "reviews": 270, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000ae"}, "name": "<PERSON><PERSON><PERSON>", "slug": "napkin", "description": "Innovative tool for collecting, organizing, and developing ideas effectively.", "websiteUrl": "https://napkin.one", "category": "AI for Self-Improvement", "tags": ["Ideas", "Organization", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Idea Organization", "Note Taking", "Collaboration"], "logo": "https://logo.clearbit.com/napkin.one", "status": "published", "isTrending": false, "isNew": true, "views": 61, "votes": 180, "rating": 4.4, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000b0"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "monkeylearn", "description": "Advanced text analysis platform for extracting insights from customer feedback.", "websiteUrl": "https://monkeylearn.com", "category": "AI for Marketing", "tags": ["Analytics", "Text Analysis", "Customer Insights"], "pricing": {"type": "paid", "startingPrice": 299}, "features": ["Text Analysis", "Sentiment Analysis", "Data Visualization"], "logo": "https://logo.clearbit.com/monkeylearn.com", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000b9"}, "name": "<PERSON><PERSON>", "slug": "kayyo", "description": "Advanced AI-powered MMA trainer app for personalized training and improvement.", "websiteUrl": "https://kayyo.ai", "category": "AI for Self-Improvement", "tags": ["Fitness", "Training", "MMA"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Training Plans", "Progress Tracking", "Technique Analysis"], "logo": "https://ui-avatars.com/api/?name=<PERSON><PERSON>&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 61, "votes": 190, "rating": 4.5, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000bb"}, "name": "Landing AI", "slug": "landing-ai", "description": "Comprehensive platform for creating and deploying custom computer vision projects.", "websiteUrl": "https://landing.ai", "category": "AI for Coding and Development", "tags": ["Computer Vision", "AI", "Industrial"], "pricing": {"type": "paid", "startingPrice": 499}, "features": ["Computer Vision", "Model Training", "Deployment"], "logo": "https://logo.clearbit.com/landing.ai", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 280, "rating": 4.7, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000bc"}, "name": "Spatial.ai", "slug": "spatial-ai", "description": "Advanced platform for predicting and influencing customer behavior through AI analysis.", "websiteUrl": "https://spatial.ai", "category": "AI for Marketing", "tags": ["Analytics", "Customer Behavior", "Research"], "pricing": {"type": "paid", "startingPrice": 299}, "features": ["Behavior Analysis", "Prediction Models", "Customer Insights"], "logo": "https://logo.clearbit.com/spatial.ai", "status": "published", "isTrending": false, "isNew": true, "views": 80, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000d6"}, "name": "Replit AI", "slug": "replit-ai-1", "description": "AI-powered coding assistant integrated with the Replit IDE for faster development.", "websiteUrl": "https://replit.com/ai", "category": "AI for Coding and Development", "tags": ["Coding", "Development", "IDE"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Code Completion", "Error Detection", "Code Generation"], "logo": "https://logo.clearbit.com/replit.com", "status": "published", "isTrending": false, "isNew": true, "views": 120, "votes": 340, "rating": 4.8, "reviews": 340, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000e8"}, "name": "Elicit", "slug": "elicit-1", "description": "Automated research assistant with powerful workflows for academic and scientific research.", "websiteUrl": "https://elicit.org", "category": "AI for Research", "tags": ["Research", "Academic", "Science"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Research Automation", "Literature Analysis", "Citation Management"], "logo": "https://logo.clearbit.com/elicit.org", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000ea"}, "name": "DataViz AI", "slug": "dataviz-ai-2", "description": "AI-powered data visualization and analysis platform.", "websiteUrl": "https://dataviz.ai", "category": "AI for Business", "tags": ["Data", "Analytics", "Business"], "pricing": {"type": "paid", "startingPrice": 7999}, "features": ["Data Visualization", "Analytics", "Reporting"], "logo": "https://ui-avatars.com/api/?name=DataViz%20AI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 230, "rating": 4.7, "reviews": 230, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000ed"}, "name": "Spatial.ai", "slug": "spatial-ai-2", "description": "AI-powered geosocial intelligence platform for business insights.", "websiteUrl": "https://spatial.ai", "category": "AI for Business", "tags": ["Business", "Analytics", "Location"], "pricing": {"type": "enterprise", "startingPrice": 499}, "features": ["Location Analytics", "Market Research", "Consumer Insights"], "logo": "https://logo.clearbit.com/spatial.ai", "status": "published", "isTrending": false, "isNew": true, "views": 55, "votes": 180, "rating": 4.7, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000102"}, "name": "Altered", "slug": "altered", "description": "Professional voice alteration tool with AI for voiceover work.", "websiteUrl": "https://altered.ai", "category": "AI for Audio Creation", "tags": ["Voice", "Audio", "Production"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Voice Modulation", "Audio Processing", "Voice Customization"], "logo": "https://logo.clearbit.com/altered.ai", "status": "published", "isTrending": false, "isNew": true, "views": 77, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000116"}, "name": "PolyAI", "slug": "polyai-1", "description": "Advanced voice assistant platform to automate customer service interactions.", "websiteUrl": "https://poly.ai", "category": "AI for Audio Creation", "tags": ["Voice", "Customer Service", "Automation"], "pricing": {"type": "enterprise", "startingPrice": 0}, "features": ["Voice Automation", "Customer Service", "Natural Language Processing"], "logo": "https://logo.clearbit.com/poly.ai", "status": "published", "isTrending": false, "isNew": true, "views": 87, "votes": 280, "rating": 4.8, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000122"}, "name": "<PERSON>", "slug": "jasper-2", "description": "AI content creation platform for marketing and business content.", "websiteUrl": "https://jasper.ai", "category": "AI for Content Creation", "tags": ["Content", "Marketing", "Writing"], "pricing": {"type": "paid", "startingPrice": 49}, "features": ["Content Generation", "Marketing Copy", "Blog Writing"], "logo": "https://logo.clearbit.com/jasper.ai", "status": "published", "isTrending": false, "isNew": true, "views": 183, "votes": 450, "rating": 4.7, "reviews": 450, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000012d"}, "name": "Fireflies.ai", "slug": "fireflies-ai-2", "description": "AI meeting assistant for recording, transcribing, and analyzing conversations.", "websiteUrl": "https://fireflies.ai", "category": "AI for Productivity", "tags": ["Meetings", "Transcription", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Meeting Recording", "Transcription", "Meeting Analysis"], "logo": "https://logo.clearbit.com/fireflies.ai", "status": "published", "isTrending": false, "isNew": true, "views": 131, "votes": 360, "rating": 4.7, "reviews": 360, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000134"}, "name": "Loom", "slug": "loom", "description": "AI-enhanced video messaging platform for async communication.", "websiteUrl": "https://loom.com", "category": "AI for Communication", "tags": ["Video", "Communication", "Collaboration"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Video Messaging", "Screen Recording", "Team Collaboration"], "logo": "https://logo.clearbit.com/loom.com", "status": "published", "isTrending": false, "isNew": true, "views": 254, "votes": 580, "rating": 4.8, "reviews": 580, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000152"}, "name": "Logseq", "slug": "logseq", "description": "Open-source knowledge management platform with AI features.", "websiteUrl": "https://logseq.com", "category": "AI for Productivity", "tags": ["Knowledge", "Open Source", "AI"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Knowledge Graph", "Note Taking", "AI Integration"], "logo": "https://logo.clearbit.com/logseq.com", "status": "published", "isTrending": false, "isNew": true, "views": 117, "votes": 320, "rating": 4.6, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000015d"}, "name": "WordAi", "slug": "wordai", "description": "AI-powered content rewriting tool for unique and engaging content.", "websiteUrl": "https://wordai.com", "category": "AI for Writing", "tags": ["Writing", "Content", "SEO"], "pricing": {"type": "paid", "startingPrice": 29}, "features": ["Content Rewriting", "Article Spinning", "Bulk Processing"], "logo": "https://logo.clearbit.com/wordai.com", "status": "published", "isTrending": false, "isNew": true, "views": 97, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000013"}, "name": "Murf.ai", "slug": "murf-ai", "description": "An AI voice generator for creating realistic voiceovers and text-to-speech content.", "websiteUrl": "https://murf.ai", "category": "AI for Voice Generation", "tags": ["AI Voice", "Text-to-Speech", "Voiceovers"], "pricing": {"type": "freemium", "startingPrice": 26}, "features": ["Text-to-Speech", "Voice Cloning", "Multilingual Support"], "logo": "https://logo.clearbit.com/murf.ai", "status": "published", "isTrending": true, "isNew": false, "views": 173, "votes": 140, "rating": 4.3, "reviews": 140, "__v": 0, "updatedAt": {"$date": "2025-04-17T10:19:45.922Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000017"}, "name": "Lumen5", "slug": "lumen5", "description": "An AI-powered video creation platform that turns blog posts into engaging videos.", "websiteUrl": "https://www.lumen5.com", "category": "AI for Video Generation", "tags": ["AI Video", "Marketing", "Content Creation"], "pricing": {"type": "freemium", "startingPrice": 29}, "features": ["Text-to-Video", "Video Templates", "Brand Customization"], "logo": "https://logo.clearbit.com/lumen5.com", "status": "published", "isTrending": true, "isNew": false, "views": 252, "votes": 150, "rating": 4.4, "reviews": 150, "__v": 0, "updatedAt": {"$date": "2025-04-17T14:59:39.981Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000028"}, "name": "<PERSON>e", "slug": "kite", "description": "An AI-powered code completion tool specifically designed for Python developers.", "websiteUrl": "https://www.kite.com", "category": "AI for Coding and Development", "tags": ["AI Coding", "Python", "Developer Tools"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Python Code Completion", "Context-Aware Suggestions", "Integration with IDEs"], "logo": "https://logo.clearbit.com/kite.com", "status": "published", "isTrending": true, "isNew": false, "views": 78, "votes": 100, "rating": 4.1, "reviews": 100, "__v": 0, "updatedAt": {"$date": "2025-04-13T21:10:55.792Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000030"}, "name": "Lately", "slug": "lately", "description": "An AI-powered social media content creation tool that helps businesses generate engaging posts from existing content.", "websiteUrl": "https://www.lately.ai", "category": "AI for Social Media", "tags": ["AI Social Media", "Content Creation", "Scheduling"], "pricing": {"type": "freemium", "startingPrice": 19}, "features": ["Content Generation", "Social Media Scheduling", "Analytics"], "logo": "https://logo.clearbit.com/lately.ai", "status": "published", "isTrending": true, "isNew": false, "views": 203, "votes": 150, "rating": 4.3, "reviews": 150, "__v": 0, "updatedAt": {"$date": "2025-04-12T11:42:20.573Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000039"}, "name": "Textio", "slug": "textio", "description": "An AI-powered writing platform that helps recruiters optimize job descriptions for better candidate engagement.", "websiteUrl": "https://www.textio.com", "category": "AI for HR and Recruitment", "tags": ["AI Recruitment", "Job Descriptions", "HR Tech"], "pricing": {"type": "paid", "startingPrice": 5000}, "features": ["Job Description Optimization", "Bias Detection", "Performance Predictions"], "logo": "https://logo.clearbit.com/textio.com", "status": "published", "isTrending": true, "isNew": false, "views": 301, "votes": 190, "rating": 4.6, "reviews": 190, "__v": 0, "updatedAt": {"$date": "2025-03-01T08:48:59.491Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000003b"}, "name": "Eightfold AI", "slug": "eightfold-ai", "description": "An AI-powered talent management platform that helps organizations with recruitment, retention, and workforce planning.", "websiteUrl": "https://www.eightfold.ai", "category": "AI for HR and Recruitment", "tags": ["AI Recruitment", "Talent Management", "HR Tech"], "pricing": {"type": "paid", "startingPrice": 20000}, "features": ["Talent Matching", "Workforce Planning", "Bias Reduction"], "logo": "https://logo.clearbit.com/eightfold.ai", "status": "published", "isTrending": true, "isNew": false, "views": 224, "votes": 170, "rating": 4.5, "reviews": 170, "__v": 0, "updatedAt": {"$date": "2025-04-14T07:41:49.826Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000061"}, "name": "SuperChat", "slug": "superchat", "description": "Advanced content creation tool that generates SEO-optimized text with natural language processing.", "websiteUrl": "https://superchat.ai", "category": "AI for Content Creation", "tags": ["Content", "SEO", "Writing"], "pricing": {"type": "paid", "startingPrice": 1999}, "features": ["SEO Optimization", "Content Generation", "Analytics"], "logo": "https://ui-avatars.com/api/?name=SuperChat&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000071"}, "name": "User Evaluation", "slug": "user-evaluation", "description": "AI-powered tool for extracting valuable insights from customer conversations and feedback.", "websiteUrl": "https://userevaluation.com", "category": "AI for Marketing", "tags": ["Analytics", "Customer Insights", "Research"], "pricing": {"type": "paid", "startingPrice": 3999}, "features": ["Conversation Analysis", "Sentiment Analysis", "Insight Generation"], "logo": "https://logo.clearbit.com/userevaluation.com", "status": "published", "isTrending": false, "isNew": true, "views": 60, "votes": 180, "rating": 4.4, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000075"}, "name": "TechCrunch Summarizer", "slug": "techcrunch-summarizer", "description": "Specialized AI tool that provides concise summaries of TechCrunch articles for quick insights.", "websiteUrl": "https://techcrunchsummarizer.com", "category": "AI for Research", "tags": ["News", "Technology", "Summarization"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Article Summarization", "Tech News", "Quick Insights"], "logo": "https://ui-avatars.com/api/?name=TechCrunch%20Summarizer&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 40, "votes": 140, "rating": 4.3, "reviews": 140, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000007a"}, "name": "Ipso AI", "slug": "ipso-ai", "description": "Smart AI assistant that manages calendars and drafts emails for efficient time management.", "websiteUrl": "https://ipso.ai", "category": "AI for Productivity", "tags": ["Calendar", "Email", "Assistant"], "pricing": {"type": "paid", "startingPrice": 2499}, "features": ["Calendar Management", "Email Drafting", "Time Optimization"], "logo": "https://logo.clearbit.com/ipso.ai", "status": "published", "isTrending": false, "isNew": true, "views": 65, "votes": 190, "rating": 4.5, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000091"}, "name": "Locus", "slug": "locus", "description": "Google Chrome Extension for finding and organizing information from any webpage using AI.", "websiteUrl": "https://locus.ai", "category": "AI for Productivity", "tags": ["Chrome Extension", "Research", "Organization"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Information Extraction", "Organization", "Web Research"], "logo": "https://ui-avatars.com/api/?name=Locus&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 50, "votes": 160, "rating": 4.4, "reviews": 160, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "000000000000000000000097"}, "name": "Promptly", "slug": "promptly", "description": "AI-powered platform for creating and managing custom prompts for various AI models.", "websiteUrl": "https://promptly.ai", "category": "AI for Productivity", "tags": ["Prompts", "AI", "Development"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Prompt Management", "Template Library", "Version Control"], "logo": "https://ui-avatars.com/api/?name=Promptly&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "00000000000000000000009e"}, "name": "DataViz AI", "slug": "dataviz-ai", "description": "Intelligent data visualization tool that creates beautiful charts and graphs automatically.", "websiteUrl": "https://dataviz.ai", "category": "AI for Business", "tags": ["Data", "Visualization", "Business"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Data Visualization", "Chart Generation", "Interactive Graphs"], "logo": "https://ui-avatars.com/api/?name=DataViz%20AI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 220, "rating": 4.5, "reviews": 220, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000a9"}, "name": "CoverLetterSimple.ai", "slug": "coverlettersimple-ai", "description": "AI tool for creating customized, job-specific cover letters that stand out.", "websiteUrl": "https://coverlettersimple.ai", "category": "AI for Productivity", "tags": ["Cover Letter", "Career", "Writing"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Cover Letter Generation", "Job Matching", "Customization"], "logo": "https://ui-avatars.com/api/?name=CoverLetterSimple.ai&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 76, "votes": 230, "rating": 4.5, "reviews": 230, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000cd"}, "name": "Requstory", "slug": "requstory", "description": "User story writing tool to help teams describe product features effectively.", "websiteUrl": "https://requstory.com", "category": "AI for Productivity", "tags": ["User Stories", "Product Management", "Agile"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Story Writing", "Feature Description", "Team Collaboration"], "logo": "https://logo.clearbit.com/requstory.com", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000e2"}, "name": "ResearchGPT", "slug": "researchgpt-1", "description": "AI research assistant for academic literature review and paper summarization.", "websiteUrl": "https://researchgpt.ai", "category": "AI for Research", "tags": ["Research", "Academic", "Analysis"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Paper Summarization", "Literature Review", "Citation Management"], "logo": "https://ui-avatars.com/api/?name=ResearchGPT&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 105, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000ee"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "monkeylearn-1", "description": "Advanced text analysis platform for customer feedback and data insights.", "websiteUrl": "https://monkeylearn.com", "category": "AI for Marketing", "tags": ["Text Analysis", "Machine Learning", "Analytics"], "pricing": {"type": "paid", "startingPrice": 299}, "features": ["Sentiment Analysis", "Data Visualization", "Text Classification"], "logo": "https://logo.clearbit.com/monkeylearn.com", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000f0"}, "name": "Photosonic", "slug": "photosonic-1", "description": "AI image generator with advanced style and composition controls.", "websiteUrl": "https://photosonic.ai", "category": "AI for Image Generation", "tags": ["AI Art", "Image Generation", "Design"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Image Generation", "Style Control", "Composition Tools"], "logo": "https://logo.clearbit.com/photosonic.ai", "status": "published", "isTrending": false, "isNew": true, "views": 90, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000fc"}, "name": "HeroPack", "slug": "heropack", "description": "Create gaming avatars with AI, inspired by popular video games.", "websiteUrl": "https://heropack.ai", "category": "AI for Image Generation", "tags": ["Gaming", "Avatars", "Art"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Avatar Generation", "Game-Style Art", "Character Customization"], "logo": "https://ui-avatars.com/api/?name=HeroPack&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 91, "votes": 280, "rating": 4.8, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000012b"}, "name": "<PERSON><PERSON>", "slug": "tome-1", "description": "AI-powered storytelling platform for creating presentations and narratives.", "websiteUrl": "https://tome.app", "category": "AI for Presentations", "tags": ["Presentations", "Design", "Storytelling"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Presentation Creation", "Story Generation", "Design Assistance"], "logo": "https://logo.clearbit.com/tome.app", "status": "published", "isTrending": false, "isNew": true, "views": 112, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000145"}, "name": "Inflection AI", "slug": "inflection-ai", "description": "AI company developing advanced language models and applications.", "websiteUrl": "https://inflection.ai", "category": "AI Research and Development", "tags": ["AI", "Research", "Language"], "pricing": {"type": "enterprise", "startingPrice": 0}, "features": ["Language Models", "AI Development", "Research"], "logo": "https://logo.clearbit.com/inflection.ai", "status": "published", "isTrending": false, "isNew": true, "views": 99, "votes": 280, "rating": 4.7, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000151"}, "name": "<PERSON><PERSON>", "slug": "tana", "description": "AI-enhanced personal knowledge management system.", "websiteUrl": "https://tana.inc", "category": "AI for Productivity", "tags": ["Knowledge", "Productivity", "AI"], "pricing": {"type": "paid", "startingPrice": 20}, "features": ["Knowledge Management", "Smart Organization", "AI Assistant"], "logo": "https://logo.clearbit.com/tana.inc", "status": "published", "isTrending": false, "isNew": true, "views": 86, "votes": 240, "rating": 4.7, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000015e"}, "name": "MOVE AI", "slug": "move-ai", "description": "Extract high-fidelity motion from videos for digital animation.", "websiteUrl": "https://move.ai", "category": "AI for Animation", "tags": ["Animation", "Motion Capture", "3D"], "pricing": {"type": "enterprise", "startingPrice": 0}, "features": ["Motion Capture", "Animation", "Video Processing"], "logo": "https://logo.clearbit.com/move.ai", "status": "published", "isTrending": false, "isNew": false, "views": 75, "votes": 220, "rating": 4.7, "reviews": 220, "__v": 0, "updatedAt": {"$date": "2025-02-15T08:43:36.024Z"}}, {"_id": {"$oid": "000000000000000000000168"}, "name": "PhotoAI", "slug": "photoai", "description": "Generate customized photos for social media content.", "websiteUrl": "https://photoai.com", "category": "Image Generation", "tags": ["Photos", "Social Media", "AI"], "pricing": {"type": "paid", "startingPrice": 18}, "features": ["Photo Generation", "Style Customization", "Social Media Format"], "logo": "https://logo.clearbit.com/photoai.com", "status": "published", "isTrending": false, "isNew": true, "views": 132, "votes": 240, "rating": 4.5, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.024Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.024Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000006"}, "name": "GitHub Copilot", "slug": "github-copilot", "description": "An AI-powered coding assistant that suggests code snippets and autocompletes code in real-time.", "websiteUrl": "https://github.com/features/copilot", "category": "AI for Coding and Development", "tags": ["AI Coding", "Developer Tools", "Productivity"], "pricing": {"type": "paid", "startingPrice": 10}, "features": ["Code Suggestions", "Autocompletion", "Multi-Language Support"], "logo": "https://logo.clearbit.com/github.com", "status": "published", "isTrending": true, "isNew": false, "views": 155, "votes": 212, "rating": 4.7, "reviews": 212, "__v": 0, "updatedAt": {"$date": "2025-04-17T02:04:39.443Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000002a"}, "name": "Codota", "slug": "codota", "description": "An AI-powered code autocompletion tool that supports multiple programming languages.", "websiteUrl": "https://www.codota.com", "category": "AI for Coding and Development", "tags": ["AI Coding", "Developer Tools", "Code Completion"], "pricing": {"type": "freemium", "startingPrice": 10}, "features": ["Code Autocompletion", "Multi-Language Support", "Context-Aware Suggestions"], "logo": "https://logo.clearbit.com/codota.com", "status": "published", "isTrending": true, "isNew": false, "views": 92, "votes": 120, "rating": 4.2, "reviews": 120, "__v": 0, "updatedAt": {"$date": "2025-04-14T14:42:28.951Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000042"}, "name": "<PERSON><PERSON><PERSON>", "slug": "re<PERSON>e", "description": "An AI-powered product recommendation engine that helps e-commerce businesses deliver personalized shopping experiences.", "websiteUrl": "https://www.recombee.com", "category": "AI for E-commerce", "tags": ["AI E-commerce", "Product Recommendations", "Personalization"], "pricing": {"type": "paid", "startingPrice": 99}, "features": ["Product Recommendations", "Personalization", "Real-Time Analytics"], "logo": "https://logo.clearbit.com/recombee.com", "status": "published", "isTrending": true, "isNew": false, "views": 404, "votes": 200, "rating": 4.5, "reviews": 200, "__v": 0, "updatedAt": {"$date": "2025-04-12T11:59:44.433Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000007d"}, "name": "FounderAssist", "slug": "founder<PERSON><PERSON>", "description": "AI tool for creating and customizing comprehensive business plans and strategies.", "websiteUrl": "https://founderassist.ai", "category": "AI for Business", "tags": ["Business", "Planning", "Startups"], "pricing": {"type": "paid", "startingPrice": 3999}, "features": ["Business Planning", "Strategy Development", "Market Analysis"], "logo": "https://ui-avatars.com/api/?name=FounderAssist&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 220, "rating": 4.6, "reviews": 220, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000008a"}, "name": "Prodigy AI", "slug": "prodigy-ai", "description": "AI-powered platform helping software engineers find their ideal job opportunities.", "websiteUrl": "https://prodigy.ai", "category": "AI for Self-Improvement", "tags": ["Career", "Development", "Jobs"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Job Matching", "Skill Assessment", "Career Planning"], "logo": "https://logo.clearbit.com/prodigy.ai", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 250, "rating": 4.6, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000bf"}, "name": "Ask <PERSON>", "slug": "ask-robi", "description": "Versatile WhatsApp AI companion for writing, translation, math, code, and spreadsheets.", "websiteUrl": "https://askrobi.com", "category": "AI Chatbots and Assistants", "tags": ["WhatsApp", "Assistant", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["WhatsApp Integration", "Multi-purpose Assistant", "Language Support"], "logo": "https://ui-avatars.com/api/?name=Ask%20Robi&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 80, "votes": 290, "rating": 4.7, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000c0"}, "name": "InfraNodus", "slug": "infranodus", "description": "Advanced text network visualization and analysis tool.", "websiteUrl": "https://infranodus.com", "category": "AI for Research", "tags": ["Research", "Visualization", "Analysis"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Text Analysis", "Network Visualization", "Research Tools"], "logo": "https://logo.clearbit.com/infranodus.com", "status": "published", "isTrending": false, "isNew": true, "views": 35, "votes": 150, "rating": 4.6, "reviews": 150, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000cb"}, "name": "Videoleap", "slug": "videoleap", "description": "Comprehensive AI-powered video editing platform with advanced features.", "websiteUrl": "https://videoleap.com", "category": "AI for Video Editing", "tags": ["Video", "Editing", "Content"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Video Editing", "Effects", "Animation"], "logo": "https://logo.clearbit.com/videoleap.com", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000cf"}, "name": "Safurai", "slug": "safurai", "description": "Visual Studio Extension for AI-powered code assistance and optimization.", "websiteUrl": "https://safurai.com", "category": "AI for Coding and Development", "tags": ["VS Code", "Development", "Coding"], "pricing": {"type": "paid", "startingPrice": 2499}, "features": ["Code Assistance", "Optimization", "VS Code Integration"], "logo": "https://logo.clearbit.com/safurai.com", "status": "published", "isTrending": false, "isNew": true, "views": 96, "votes": 290, "rating": 4.7, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000d2"}, "name": "Tribescaler", "slug": "tribescaler", "description": "AI tool for writing engaging tweets and Twitter threads to grow audience.", "websiteUrl": "https://tribescaler.com", "category": "AI for Social Media", "tags": ["Twitter", "Social Media", "Growth"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Tweet Writing", "Thread Creation", "Audience Growth"], "logo": "https://logo.clearbit.com/tribescaler.com", "status": "published", "isTrending": false, "isNew": true, "views": 65, "votes": 190, "rating": 4.5, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000e4"}, "name": "MusicGen Pro", "slug": "musicgen-pro-1", "description": "Professional AI music composition and arrangement tool.", "websiteUrl": "https://musicgenpro.ai", "category": "AI for Audio Creation", "tags": ["Music", "Audio", "Composition"], "pricing": {"type": "paid", "startingPrice": 2499}, "features": ["Music Composition", "Arrangement", "Instrument Selection"], "logo": "https://ui-avatars.com/api/?name=MusicGen%20Pro&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 81, "votes": 250, "rating": 4.5, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000eb"}, "name": "Writesonic", "slug": "writesonic-2", "description": "AI writing assistant for creating high-quality content.", "websiteUrl": "https://writesonic.com", "category": "AI for Writing", "tags": ["Writing", "Content", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 1299}, "features": ["Content Generation", "Article Writing", "Marketing Copy"], "logo": "https://logo.clearbit.com/writesonic.com", "status": "published", "isTrending": false, "isNew": true, "views": 105, "votes": 350, "rating": 4.6, "reviews": 350, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "00000000000000000000010b"}, "name": "WordHero", "slug": "wordhero-2", "description": "AI-powered writing tool for creating engaging content.", "websiteUrl": "https://wordhero.co", "category": "AI for Writing", "tags": ["Writing", "Content", "SEO"], "pricing": {"type": "paid", "startingPrice": 2499}, "features": ["Content Creation", "Writing Assistant", "SEO Optimization"], "logo": "https://logo.clearbit.com/wordhero.co", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 280, "rating": 4.5, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "00000000000000000000010d"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "monkeylearn-2", "description": "Advanced text analysis platform for customer feedback and data insights.", "websiteUrl": "https://monkeylearn.com", "category": "AI for Marketing", "tags": ["Text Analysis", "Machine Learning", "Analytics"], "pricing": {"type": "paid", "startingPrice": 299}, "features": ["Sentiment Analysis", "Data Visualization", "Text Classification"], "logo": "https://logo.clearbit.com/monkeylearn.com", "status": "published", "isTrending": false, "isNew": true, "views": 104, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000118"}, "name": "Peppertype", "slug": "peppertype-2", "description": "AI content generation platform for marketers and content creators.", "websiteUrl": "https://peppertype.ai", "category": "AI for Content Creation", "tags": ["Content", "Marketing", "Writing"], "pricing": {"type": "paid", "startingPrice": 35}, "features": ["Content Generation", "Marketing Copy", "Social Media Posts"], "logo": "https://logo.clearbit.com/peppertype.ai", "status": "published", "isTrending": false, "isNew": true, "views": 89, "votes": 280, "rating": 4.7, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000123"}, "name": "Grammarly", "slug": "grammarly-1", "description": "AI-powered writing assistant for grammar, style, and tone improvement.", "websiteUrl": "https://grammarly.com", "category": "AI for Writing", "tags": ["Writing", "Grammar", "Editing"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Grammar Check", "Style Suggestions", "Tone Detection"], "logo": "https://logo.clearbit.com/grammarly.com", "status": "published", "isTrending": false, "isNew": true, "views": 502, "votes": 820, "rating": 4.8, "reviews": 820, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000013b"}, "name": "Bard", "slug": "bard", "description": "Google's AI chatbot for creative and analytical tasks.", "websiteUrl": "https://bard.google.com", "category": "AI Chatbots", "tags": ["AI", "<PERSON><PERSON><PERSON>", "Google"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Text Generation", "Analysis", "Creative Writing"], "logo": "https://logo.clearbit.com/bard.google.com", "status": "published", "isTrending": false, "isNew": true, "views": 287, "votes": 580, "rating": 4.7, "reviews": 580, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000013d"}, "name": "<PERSON><PERSON><PERSON>", "slug": "cursor", "description": "AI-powered code editor with advanced features.", "websiteUrl": "https://cursor.sh", "category": "AI for Development", "tags": ["Development", "Editor", "AI"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Code Editing", "AI Assistance", "Code Generation"], "logo": "https://logo.clearbit.com/cursor.sh", "status": "published", "isTrending": false, "isNew": true, "views": 144, "votes": 380, "rating": 4.7, "reviews": 380, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000147"}, "name": "Replika", "slug": "replika", "description": "AI companion app for conversations and emotional support.", "websiteUrl": "https://replika.ai", "category": "AI for Personal Development", "tags": ["AI", "Mental Health", "Personal"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["AI Chat", "Emotional Support", "Personal Growth"], "logo": "https://logo.clearbit.com/replika.ai", "status": "published", "isTrending": false, "isNew": true, "views": 253, "votes": 580, "rating": 4.5, "reviews": 580, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000159"}, "name": "Supernotes", "slug": "supernotes", "description": "AI-powered note-taking app for students and professionals.", "websiteUrl": "https://supernotes.app", "category": "AI for Education", "tags": ["Education", "Notes", "Study"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Note Taking", "AI Organization", "Study Tools"], "logo": "https://logo.clearbit.com/supernotes.app", "status": "published", "isTrending": false, "isNew": true, "views": 113, "votes": 320, "rating": 4.6, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000001b"}, "name": "Synthesys", "slug": "synthesys", "description": "An AI tool for creating realistic voiceovers and video content.", "websiteUrl": "https://synthesys.io", "category": "AI for Voice Generation", "tags": ["AI Voice", "Text-to-Speech", "Video Creation"], "pricing": {"type": "paid", "startingPrice": 29}, "features": ["Text-to-Speech", "AI Avatars", "Video Generation"], "logo": "https://logo.clearbit.com/synthesys.io", "status": "published", "isTrending": true, "isNew": false, "views": 151, "votes": 110, "rating": 4.2, "reviews": 110, "__v": 0, "updatedAt": {"$date": "2025-03-02T15:44:37.913Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000001c"}, "name": "Pictory", "slug": "pictory", "description": "An AI tool for creating videos from text, ideal for marketers and content creators.", "websiteUrl": "https://pictory.ai", "category": "AI for Video Generation", "tags": ["AI Video", "Content Creation", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 19}, "features": ["Text-to-Video", "Video Summarization", "Automated Captions"], "logo": "https://logo.clearbit.com/pictory.ai", "status": "published", "isTrending": true, "isNew": false, "views": 181, "votes": 130, "rating": 4.3, "reviews": 130, "__v": 0, "updatedAt": {"$date": "2025-04-11T20:45:13.858Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000002d"}, "name": "<PERSON><PERSON><PERSON>", "slug": "buffer", "description": "An AI-powered social media scheduling tool that helps businesses plan and publish content across multiple platforms.", "websiteUrl": "https://buffer.com", "category": "AI for Social Media", "tags": ["AI Social Media", "Scheduling", "Content Management"], "pricing": {"type": "freemium", "startingPrice": 5}, "features": ["Social Media Scheduling", "Content Calendar", "Analytics"], "logo": "https://logo.clearbit.com/buffer.com", "status": "published", "isTrending": true, "isNew": false, "views": 304, "votes": 200, "rating": 4.5, "reviews": 200, "__v": 0, "updatedAt": {"$date": "2025-03-02T07:26:47.214Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000005b"}, "name": "Brandfort.co", "slug": "brandfort-co", "description": "AI-powered tool that automatically removes unwanted comments on social media, helping maintain brand reputation.", "websiteUrl": "https://brandfort.co", "category": "AI for Social Media", "tags": ["Social Media", "Moderation", "Brand Protection"], "pricing": {"type": "paid", "startingPrice": 2499}, "features": ["Comment Moderation", "Brand Protection", "Automated Filtering"], "logo": "https://logo.clearbit.com/brandfort.co", "status": "published", "isTrending": false, "isNew": true, "views": 55, "votes": 160, "rating": 4.4, "reviews": 160, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.017Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "00000000000000000000005c"}, "name": "AI Bingo", "slug": "ai-bingo", "description": "An entertaining guessing game for artists to test their knowledge and creativity with AI art.", "websiteUrl": "https://aibingo.fun", "category": "AI for Fun", "tags": ["Games", "Art", "Entertainment"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Interactive Games", "Artist Challenges", "Community Features"], "logo": "https://ui-avatars.com/api/?name=AI%20Bingo&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.017Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "000000000000000000000066"}, "name": "ClipyBoard", "slug": "clipyboard", "description": "AI tool for managing multilingual customer service messages and team collaboration efficiently.", "websiteUrl": "https://clipyboard.ai", "category": "AI for Productivity", "tags": ["Customer Service", "Collaboration", "Multilingual"], "pricing": {"type": "paid", "startingPrice": 1999}, "features": ["Multilingual Support", "Team Collaboration", "Message Management"], "logo": "https://ui-avatars.com/api/?name=ClipyBoard&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 200, "rating": 4.5, "reviews": 200, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000007c"}, "name": "FGenEds", "slug": "fgeneds", "description": "AI-powered tool for summarizing lectures and educational materials to enhance learning.", "websiteUrl": "https://fgeneds.com", "category": "AI for Education", "tags": ["Education", "Learning", "Study"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Lecture Summarization", "Study Materials", "Learning Tools"], "logo": "https://ui-avatars.com/api/?name=FGenEds&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 55, "votes": 170, "rating": 4.4, "reviews": 170, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000081"}, "name": "Sellesta AI", "slug": "sellesta-ai", "description": "Comprehensive suite of tools for optimizing Amazon listings and improving sales performance.", "websiteUrl": "https://sellesta.ai", "category": "AI for Marketing", "tags": ["Amazon", "E-commerce", "Marketing"], "pricing": {"type": "paid", "startingPrice": 3999}, "features": ["Listing Optimization", "Sales Analytics", "Competitor Analysis"], "logo": "https://logo.clearbit.com/sellesta.ai", "status": "published", "isTrending": false, "isNew": true, "views": 86, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000087"}, "name": "Lately.ai", "slug": "lately-ai", "description": "AI-powered platform for content generation and social media management optimization.", "websiteUrl": "https://lately.ai", "category": "AI for Social Media", "tags": ["Social Media", "Content", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 2999}, "features": ["Content Generation", "Social Media Management", "Analytics"], "logo": "https://logo.clearbit.com/lately.ai", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 200, "rating": 4.5, "reviews": 200, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "0000000000000000000000a3"}, "name": "Ask Buzzing AI", "slug": "ask-buzzing-ai", "description": "Advanced content creation tool with AI-powered insights and optimization features.", "websiteUrl": "https://askbuzzing.ai", "category": "AI for Marketing", "tags": ["Marketing", "Content", "SEO"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Content Generation", "SEO Optimization", "Marketing Analytics"], "logo": "https://ui-avatars.com/api/?name=Ask%20Buzzing%20AI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 220, "rating": 4.5, "reviews": 220, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000a7"}, "name": "editGPT", "slug": "editgpt", "description": "Browser extension for proofreading, editing, and tracking changes in content with AI assistance.", "websiteUrl": "https://editgpt.app", "category": "AI for Productivity", "tags": ["Writing", "Editing", "Browser Extension"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Proofreading", "Change Tracking", "Content Editing"], "logo": "https://logo.clearbit.com/editgpt.app", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 250, "rating": 4.6, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000e0"}, "name": "Lexica", "slug": "lexica", "description": "AI art search engine and image generation platform.", "websiteUrl": "https://lexica.art", "category": "AI for Image Generation", "tags": ["Art", "Images", "Design"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Image Search", "Image Generation", "Style Discovery"], "logo": "https://logo.clearbit.com/lexica.art", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 290, "rating": 4.6, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000100"}, "name": "Bad Cook Club", "slug": "bad-cook-club", "description": "Fun and practical recipe generator for people who struggle with cooking.", "websiteUrl": "https://badcookclub.com", "category": "AI for Self-Improvement", "tags": ["Cooking", "Learning", "Recipes"], "pricing": {"type": "freemium", "startingPrice": 799}, "features": ["Recipe Generation", "Cooking Tips", "Step-by-Step Guides"], "logo": "https://ui-avatars.com/api/?name=Bad%20Cook%20Club&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 86, "votes": 280, "rating": 4.5, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000105"}, "name": "editGPT", "slug": "editgpt-1", "description": "AI-powered writing assistant for grammar and style improvements.", "websiteUrl": "https://editgpt.app", "category": "AI for Writing", "tags": ["Writing", "Grammar", "Editing"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Grammar Checking", "Style Enhancement", "Writing Assistance"], "logo": "https://logo.clearbit.com/editgpt.app", "status": "published", "isTrending": false, "isNew": true, "views": 131, "votes": 420, "rating": 4.5, "reviews": 420, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000108"}, "name": "TED SMRZR", "slug": "ted-smrzr-2", "description": "AI-powered tool for summarizing TED talks and educational content.", "websiteUrl": "https://tedsmrzr.com", "category": "AI for Education", "tags": ["Education", "Learning", "Summarization"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Content Summarization", "Key Points Extraction", "Learning Assistance"], "logo": "https://ui-avatars.com/api/?name=TED%20SMRZR&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 78, "votes": 260, "rating": 4.5, "reviews": 260, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000112"}, "name": "Storyd", "slug": "storyd-2", "description": "AI-powered platform for creating compelling data presentations and visualizations.", "websiteUrl": "https://storyd.ai", "category": "AI for Productivity", "tags": ["Data", "Visualization", "Presentations"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Data Visualization", "Presentation Creation", "Story Templates"], "logo": "https://logo.clearbit.com/storyd.ai", "status": "published", "isTrending": false, "isNew": true, "views": 72, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000011e"}, "name": "Perplexity AI", "slug": "perplexity-ai-1", "description": "AI-powered search engine that provides detailed answers with citations.", "websiteUrl": "https://perplexity.ai", "category": "AI Search Engines and Research Tools", "tags": ["Search", "Research", "AI"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Smart Search", "Citation Generation", "Research Assistant"], "logo": "https://logo.clearbit.com/perplexity.ai", "status": "published", "isTrending": false, "isNew": true, "views": 153, "votes": 420, "rating": 4.7, "reviews": 420, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000126"}, "name": "Copy.ai", "slug": "copy-ai-1", "description": "AI copywriting tool for marketing and business content.", "websiteUrl": "https://copy.ai", "category": "AI for Writing", "tags": ["Writing", "Marketing", "Business"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Copywriting", "Content Generation", "Marketing Copy"], "logo": "https://logo.clearbit.com/copy.ai", "status": "published", "isTrending": false, "isNew": true, "views": 163, "votes": 420, "rating": 4.6, "reviews": 420, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000014e"}, "name": "Taskade AI", "slug": "taskade-ai", "description": "AI-powered productivity and collaboration platform.", "websiteUrl": "https://taskade.com", "category": "AI for Productivity", "tags": ["Productivity", "AI", "Collaboration"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Task Management", "AI Assistant", "Collaboration"], "logo": "https://logo.clearbit.com/taskade.com", "status": "published", "isTrending": false, "isNew": true, "views": 140, "votes": 380, "rating": 4.7, "reviews": 380, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}}, {"_id": {"$oid": "00000000000000000000015b"}, "name": "Readwise", "slug": "readwise", "description": "AI-powered reading companion and knowledge management tool.", "websiteUrl": "https://readwise.io", "category": "AI for Education", "tags": ["Reading", "Knowledge", "Learning"], "pricing": {"type": "paid", "startingPrice": 8}, "features": ["Reading Assistant", "Knowledge Management", "AI Insights"], "logo": "https://logo.clearbit.com/readwise.io", "status": "published", "isTrending": false, "isNew": true, "views": 162, "votes": 420, "rating": 4.8, "reviews": 420, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000169"}, "name": "Sloyd", "slug": "sloyd", "description": "3D modelling tool for game-ready assets using ML and parametric generators.", "websiteUrl": "https://sloyd.ai", "category": "AI for Gaming", "tags": ["3D", "Gaming", "Design"], "pricing": {"type": "paid", "startingPrice": 29}, "features": ["3D Modelling", "Asset Generation", "Game-Ready Export"], "logo": "https://logo.clearbit.com/sloyd.ai", "status": "published", "isTrending": false, "isNew": false, "views": 65, "votes": 180, "rating": 4.7, "reviews": 180, "__v": 0, "updatedAt": {"$date": "2025-02-15T08:43:36.024Z"}}, {"_id": {"$oid": "000000000000000000000003"}, "name": "Perplexity AI", "slug": "perplexity-ai", "description": "An AI research engine that provides answers with citations, perfect for deep research and synthesis.", "websiteUrl": "https://www.perplexity.ai", "category": "AI Search Engines and Research Tools", "tags": ["AI Search", "Research", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 20}, "features": ["Citations", "Interactive Search", "Real-Time Data"], "logo": "https://logo.clearbit.com/perplexity.ai", "status": "published", "isTrending": true, "isNew": false, "views": 302, "votes": 784, "rating": 4.6, "reviews": 784, "__v": 0, "updatedAt": {"$date": "2025-04-12T10:44:38.163Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000056"}, "name": "Tweet Assist App", "slug": "tweet-assist-app", "description": "AI-powered Twitter assistant that helps create engaging tweets and manage social media presence.", "websiteUrl": "https://tweetassist.app", "category": "AI for Social Media", "tags": ["Twitter", "Social Media", "Content"], "pricing": {"type": "freemium", "startingPrice": 499}, "features": ["Tweet Generation", "Engagement Analytics", "Scheduling"], "logo": "https://ui-avatars.com/api/?name=Tweet%20Assist%20App&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 200, "rating": 4.4, "reviews": 200, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.017Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "000000000000000000000059"}, "name": "Inngest", "slug": "inngest", "description": "A powerful tool for creating personalized documentation and code examples for scheduling serverless functions.", "websiteUrl": "https://inngest.com", "category": "AI for Productivity", "tags": ["Documentation", "Serverless", "Development"], "pricing": {"type": "paid", "startingPrice": 19}, "features": ["Documentation Generation", "Code Examples", "Serverless Scheduling"], "logo": "https://logo.clearbit.com/inngest.com", "status": "published", "isTrending": false, "isNew": true, "views": 45, "votes": 150, "rating": 4.3, "reviews": 150, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.017Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "000000000000000000000065"}, "name": "EzMail.AI", "slug": "ezmail-ai", "description": "Smart email drafting assistant that helps compose professional and effective emails quickly.", "websiteUrl": "https://ezmail.ai", "category": "AI for Productivity", "tags": ["Email", "Communication", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 599}, "features": ["Email Templates", "Smart Suggestions", "Tone Analysis"], "logo": "https://logo.clearbit.com/ezmail.ai", "status": "published", "isTrending": false, "isNew": true, "views": 50, "votes": 160, "rating": 4.3, "reviews": 160, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000073"}, "name": "Runday", "slug": "runday", "description": "AI-powered appointment booking tool that optimizes scheduling and manages calendar efficiently.", "websiteUrl": "https://runday.ai", "category": "AI for Productivity", "tags": ["Scheduling", "Calendar", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["<PERSON> Scheduling", "Calendar Management", "Booking System"], "logo": "https://logo.clearbit.com/runday.ai", "status": "published", "isTrending": false, "isNew": true, "views": 56, "votes": 170, "rating": 4.4, "reviews": 170, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000098"}, "name": "Synthesia", "slug": "synthesia-1", "description": "Professional AI video creation platform for generating custom video content with virtual presenters.", "websiteUrl": "https://synthesia.io", "category": "AI for Video Generation", "tags": ["Video", "Content Creation", "Marketing"], "pricing": {"type": "paid", "startingPrice": 3999}, "features": ["Video Generation", "Avatar Creation", "Script Translation"], "logo": "https://logo.clearbit.com/synthesia.io", "status": "published", "isTrending": false, "isNew": true, "views": 110, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000c2"}, "name": "Mendable", "slug": "mendable", "description": "Create custom chat-powered search experiences trained on your documentation.", "websiteUrl": "https://mendable.ai", "category": "AI for Marketing", "tags": ["Search", "Documentation", "Support"], "pricing": {"type": "paid", "startingPrice": 59}, "features": ["Custom Search", "Documentation Training", "Chat Interface"], "logo": "https://logo.clearbit.com/mendable.ai", "status": "published", "isTrending": false, "isNew": true, "views": 41, "votes": 160, "rating": 4.5, "reviews": 160, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000c6"}, "name": "FineCam", "slug": "finecam", "description": "Virtual camera with AI-powered effects and templates for remote meetings.", "websiteUrl": "https://finecam.app", "category": "AI for Productivity", "tags": ["Video", "Meetings", "Productivity"], "pricing": {"type": "paid", "startingPrice": 1499}, "features": ["Virtual Camera", "Effects Library", "Meeting Templates"], "logo": "https://ui-avatars.com/api/?name=FineCam&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 50, "votes": 190, "rating": 4.5, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000d1"}, "name": "Magic Eraser", "slug": "magic-eraser", "description": "AI-powered tool for removing unwanted elements from images with precision.", "websiteUrl": "https://magiceraser.io", "category": "AI for Image Improvement", "tags": ["Image Editing", "Photo", "Design"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Object Removal", "Image Editing", "Background Cleanup"], "logo": "https://logo.clearbit.com/magiceraser.io", "status": "published", "isTrending": false, "isNew": true, "views": 110, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "000000000000000000000125"}, "name": "Anthropic", "slug": "anthropic", "description": "AI research company developing safe and ethical AI systems.", "websiteUrl": "https://anthropic.com", "category": "AI Research and Development", "tags": ["AI", "Research", "Ethics"], "pricing": {"type": "enterprise", "startingPrice": 0}, "features": ["AI Development", "Research", "Ethics"], "logo": "https://logo.clearbit.com/anthropic.com", "status": "published", "isTrending": false, "isNew": true, "views": 123, "votes": 280, "rating": 4.8, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000158"}, "name": "<PERSON><PERSON>", "slug": "anki-ai", "description": "AI-enhanced flashcard app for efficient learning.", "websiteUrl": "https://apps.ankiweb.net", "category": "AI for Education", "tags": ["Education", "Learning", "Flashcards"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Flashcards", "Spaced Repetition", "AI Learning"], "logo": "https://logo.clearbit.com/ankiweb.net", "status": "published", "isTrending": false, "isNew": true, "views": 252, "votes": 580, "rating": 4.8, "reviews": 580, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000015a"}, "name": "Glasp", "slug": "glasp", "description": "Social web highlighter with AI-powered features.", "websiteUrl": "https://glasp.co", "category": "AI for Education", "tags": ["Education", "Research", "Social"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Web Highlighting", "Knowledge Sharing", "AI Summary"], "logo": "https://logo.clearbit.com/glasp.co", "status": "published", "isTrending": false, "isNew": true, "views": 97, "votes": 280, "rating": 4.7, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000000d"}, "name": "Copy.ai", "slug": "copy-ai", "description": "An AI copywriting tool for generating marketing copy, social media posts, and emails.", "websiteUrl": "https://www.copy.ai", "category": "AI for Content Creation", "tags": ["AI Writing", "Marketing", "Copywriting"], "pricing": {"type": "freemium", "startingPrice": 49}, "features": ["Marketing Copy", "Social Media Posts", "Email Templates"], "logo": "https://logo.clearbit.com/copy.ai", "status": "published", "isTrending": true, "isNew": true, "views": 417, "votes": 280, "rating": 4.4, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.015Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.015Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000012"}, "name": "AIVA", "slug": "aiva", "description": "An AI music composer that generates original music tracks for various use cases.", "websiteUrl": "https://www.aiva.ai", "category": "AI for Music Generation", "tags": ["AI Music", "Creative Tools", "Soundtracks"], "pricing": {"type": "freemium", "startingPrice": 15}, "features": ["Music Composition", "Customizable Tracks", "Royalty-Free Music"], "logo": "https://logo.clearbit.com/aiva.ai", "status": "published", "isTrending": true, "isNew": false, "views": 150, "votes": 120, "rating": 4.2, "reviews": 120, "__v": 0, "updatedAt": {"$date": "2025-02-15T08:43:36.015Z"}}, {"_id": {"$oid": "000000000000000000000014"}, "name": "Zapier", "slug": "zapier", "description": "An automation tool that connects apps and services to automate workflows.", "websiteUrl": "https://zapier.com", "category": "AI for Automation", "tags": ["AI Automation", "Productivity", "Workflow"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Workflow Automation", "App Integration", "Task Automation"], "logo": "https://logo.clearbit.com/zapier.com", "status": "published", "isTrending": true, "isNew": false, "views": 10, "votes": 500, "rating": 4.6, "reviews": 500, "__v": 0, "updatedAt": {"$date": "2025-04-17T07:28:22.504Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000015"}, "name": "<PERSON><PERSON><PERSON>", "slug": "rytr", "description": "An AI writing assistant that helps users generate high-quality content for blogs, emails, and more.", "websiteUrl": "https://rytr.me", "category": "AI for Content Creation", "tags": ["AI Writing", "Content Creation", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 9}, "features": ["Content Generation", "Tone Customization", "SEO Optimization"], "logo": "https://logo.clearbit.com/rytr.me", "status": "published", "isTrending": true, "isNew": false, "views": 202, "votes": 180, "rating": 4.2, "reviews": 180, "__v": 0, "updatedAt": {"$date": "2025-04-13T02:15:51.053Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000023"}, "name": "Luminar AI", "slug": "luminar-ai", "description": "An AI-powered photo editing tool that automates complex editing tasks.", "websiteUrl": "https://skylum.com/luminar-ai", "category": "AI for Image Editing", "tags": ["AI Photo Editing", "Creative Tools", "Design"], "pricing": {"type": "paid", "startingPrice": 79}, "features": ["Automated Editing", "AI Enhancements", "Creative Filters"], "logo": "https://logo.clearbit.com/skylum.com", "status": "published", "isTrending": true, "isNew": false, "views": 301, "votes": 200, "rating": 4.4, "reviews": 200, "__v": 0, "updatedAt": {"$date": "2025-03-05T02:12:28.264Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000031"}, "name": "SocialBee", "slug": "socialbee", "description": "An AI-powered social media scheduling tool that helps businesses organize and automate their social media posts.", "websiteUrl": "https://socialbee.io", "category": "AI for Social Media", "tags": ["AI Social Media", "Scheduling", "Automation"], "pricing": {"type": "freemium", "startingPrice": 19}, "features": ["Social Media Scheduling", "Content Categorization", "Analytics"], "logo": "https://logo.clearbit.com/socialbee.io", "status": "published", "isTrending": true, "isNew": false, "views": 183, "votes": 130, "rating": 4.2, "reviews": 130, "__v": 0, "updatedAt": {"$date": "2025-04-17T18:13:38.494Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000032"}, "name": "Crowdfire", "slug": "crowdfire", "description": "An AI-powered social media management tool that helps businesses schedule posts, monitor engagement, and grow their audience.", "websiteUrl": "https://www.crowdfireapp.com", "category": "AI for Social Media", "tags": ["AI Social Media", "Management", "Scheduling"], "pricing": {"type": "freemium", "startingPrice": 748}, "features": ["Social Media Scheduling", "Engagement Tracking", "Audience Growth"], "logo": "https://logo.clearbit.com/crowdfireapp.com", "status": "published", "isTrending": true, "isNew": false, "views": 152, "votes": 120, "rating": 4.1, "reviews": 120, "__v": 0, "updatedAt": {"$date": "2025-04-11T11:06:09.506Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000036"}, "name": "Tailwind", "slug": "tailwind", "description": "An AI-powered scheduling tool for Pinterest and Instagram that helps businesses grow their audience.", "websiteUrl": "https://www.tailwindapp.com", "category": "AI for Social Media", "tags": ["AI Social Media", "Pinterest", "Instagram"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Pinterest Scheduling", "Instagram Scheduling", "Analytics"], "logo": "https://logo.clearbit.com/tailwindapp.com", "status": "published", "isTrending": true, "isNew": false, "views": 303, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "updatedAt": {"$date": "2025-03-06T00:24:17.448Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000003e"}, "name": "<PERSON><PERSON><PERSON>", "slug": "beamery", "description": "An AI-powered talent acquisition platform that helps organizations attract, engage, and retain top talent.", "websiteUrl": "https://www.beamery.com", "category": "AI for HR and Recruitment", "tags": ["AI Recruitment", "Talent Acquisition", "HR Tech"], "pricing": {"type": "paid", "startingPrice": 25000}, "features": ["Talent CRM", "Candidate Engagement", "Workforce Planning"], "logo": "https://logo.clearbit.com/beamery.com", "status": "published", "isTrending": true, "isNew": false, "views": 300, "votes": 200, "rating": 4.6, "reviews": 200, "__v": 0, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "00000000000000000000004b"}, "name": "Claude 3", "slug": "claude-3", "description": "Next generation AI assistant with enhanced reasoning and capabilities", "websiteUrl": "https://anthropic.com", "category": "AI Chatbots and Assistants", "tags": ["AI", "Language Model", "Enterprise"], "pricing": {"type": "paid", "startingPrice": 0}, "features": ["Enhanced Reasoning", "Improved Context Understanding", "Advanced Analysis"], "logo": "https://logo.clearbit.com/anthropic.com", "status": "published", "isTrending": false, "isNew": false, "views": 91, "votes": 0, "rating": 0, "reviews": 0, "__v": 0, "updatedAt": {"$date": "2025-04-11T08:25:38.210Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000063"}, "name": "Read Pilot", "slug": "read-pilot", "description": "AI tool that automatically generates Q&A cards from online articles for better comprehension.", "websiteUrl": "https://readpilot.app", "category": "AI for Research", "tags": ["Education", "Research", "Learning"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Q&A Generation", "Article Analysis", "Study Cards"], "logo": "https://ui-avatars.com/api/?name=Read%20Pilot&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 45, "votes": 150, "rating": 4.3, "reviews": 150, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000078"}, "name": "CodeSquire", "slug": "codesquire", "description": "Intelligent code writing assistant that helps developers write better code faster.", "websiteUrl": "https://codesquire.dev", "category": "AI for Coding and Development", "tags": ["Coding", "Development", "Programming"], "pricing": {"type": "freemium", "startingPrice": 15}, "features": ["Code Generation", "Code Review", "Best Practices"], "logo": "https://ui-avatars.com/api/?name=CodeSquire&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 80, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000083"}, "name": "LoveGPT", "slug": "lovegpt", "description": "AI tool for generating personalized conversation suggestions for better relationships.", "websiteUrl": "https://lovegpt.ai", "category": "AI for Self-Improvement", "tags": ["Relationships", "Communication", "Self-help"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Conversation Suggestions", "Relationship Tips", "Personalization"], "logo": "https://ui-avatars.com/api/?name=LoveGPT&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000088"}, "name": "Nanonets", "slug": "nanonets", "description": "AI-powered tool for automating data capture from documents and streamlining manual data entry.", "websiteUrl": "https://nanonets.com", "category": "AI for Productivity", "tags": ["OCR", "Automation", "Data Processing"], "pricing": {"type": "paid", "startingPrice": 499}, "features": ["Document Processing", "Data Extraction", "Workflow Automation"], "logo": "https://logo.clearbit.com/nanonets.com", "status": "published", "isTrending": false, "isNew": true, "views": 120, "votes": 340, "rating": 4.8, "reviews": 340, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "000000000000000000000093"}, "name": "Makelog", "slug": "makelog", "description": "AI-powered platform for quickly sharing and managing product updates and changelogs.", "websiteUrl": "https://makelog.com", "category": "AI for Productivity", "tags": ["Product Updates", "Documentation", "Collaboration"], "pricing": {"type": "freemium", "startingPrice": 19}, "features": ["Changelog Management", "Update Sharing", "Team Collaboration"], "logo": "https://logo.clearbit.com/makelog.com", "status": "published", "isTrending": false, "isNew": true, "views": 55, "votes": 170, "rating": 4.5, "reviews": 170, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "00000000000000000000009a"}, "name": "Speak AI", "slug": "speak-ai", "description": "Advanced speech recognition and transcription platform with multi-language support.", "websiteUrl": "https://speak.ai", "category": "AI for Speech", "tags": ["Speech", "Transcription", "Language"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Speech Recognition", "Transcription", "Language Support"], "logo": "https://logo.clearbit.com/speak.ai", "status": "published", "isTrending": false, "isNew": true, "views": 66, "votes": 190, "rating": 4.5, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000ad"}, "name": "Automata", "slug": "automata", "description": "Versatile tool for repurposing content into various formats efficiently.", "websiteUrl": "https://automata.ai", "category": "AI for Marketing", "tags": ["Content", "Marketing", "Automation"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Content Repurposing", "Format Conversion", "Distribution"], "logo": "https://ui-avatars.com/api/?name=Automata&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000b4"}, "name": "Lemonaid Music", "slug": "lemonaid-music", "description": "Innovative AI-powered platform for music creation and composition.", "websiteUrl": "https://lemonaidmusic.com", "category": "AI for Audio Creation", "tags": ["Music", "Audio", "Creation"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Music Composition", "Sound Design", "Audio Export"], "logo": "https://ui-avatars.com/api/?name=Lemonaid%20Music&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 290, "rating": 4.7, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000f1"}, "name": "MagickPen", "slug": "magickpen-1", "description": "Comprehensive AI writing assistant for various content types.", "websiteUrl": "https://magickpen.ai", "category": "AI for Productivity", "tags": ["Writing", "Content", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Content Writing", "Grammar Check", "Style Suggestions"], "logo": "https://ui-avatars.com/api/?name=MagickPen&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 65, "votes": 200, "rating": 4.5, "reviews": 200, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000111"}, "name": "DiscuroAI", "slug": "discuroai-2", "description": "Platform for rapidly building and testing complex AI workflows and integrations.", "websiteUrl": "https://discuro.ai", "category": "AI for Coding and Development", "tags": ["Development", "AI", "Workflow"], "pricing": {"type": "paid", "startingPrice": 4999}, "features": ["Workflow Building", "AI Integration", "Testing Tools"], "logo": "https://ui-avatars.com/api/?name=DiscuroAI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 92, "votes": 250, "rating": 4.6, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000011f"}, "name": "Notion AI", "slug": "notion-ai-1", "description": "AI-powered writing and productivity assistant integrated with Notion.", "websiteUrl": "https://notion.so", "category": "AI for Productivity", "tags": ["Productivity", "Writing", "Organization"], "pricing": {"type": "paid", "startingPrice": 10}, "features": ["Writing Assistant", "Content Generation", "Task Management"], "logo": "https://logo.clearbit.com/notion.so", "status": "published", "isTrending": false, "isNew": true, "views": 167, "votes": 480, "rating": 4.8, "reviews": 480, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000136"}, "name": "<PERSON><PERSON><PERSON>", "slug": "scenario", "description": "AI game development platform for creating interactive experiences.", "websiteUrl": "https://scenario.gg", "category": "AI for Gaming", "tags": ["Gaming", "Development", "AI"], "pricing": {"type": "paid", "startingPrice": 29}, "features": ["Game Development", "Asset Generation", "World Building"], "logo": "https://logo.clearbit.com/scenario.gg", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000013a"}, "name": "Phind", "slug": "phind", "description": "AI-powered search engine for developers with code examples.", "websiteUrl": "https://phind.com", "category": "AI for Development", "tags": ["Development", "Search", "Programming"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Code Search", "Documentation Search", "Code Examples"], "logo": "https://logo.clearbit.com/phind.com", "status": "published", "isTrending": false, "isNew": true, "views": 162, "votes": 420, "rating": 4.8, "reviews": 420, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000148"}, "name": "Forefront", "slug": "forefront", "description": "Platform for accessing and fine-tuning large language models.", "websiteUrl": "https://forefront.ai", "category": "AI Development", "tags": ["AI", "Development", "Language Models"], "pricing": {"type": "paid", "startingPrice": 29}, "features": ["Model Access", "Fine-tuning", "API Integration"], "logo": "https://logo.clearbit.com/forefront.ai", "status": "published", "isTrending": false, "isNew": true, "views": 89, "votes": 240, "rating": 4.7, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000149"}, "name": "Civitai", "slug": "civitai", "description": "Community-driven platform for sharing and discovering AI art models.", "websiteUrl": "https://civitai.com", "category": "AI for Art", "tags": ["Art", "AI", "Community"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Model Sharing", "Art Generation", "Community"], "logo": "https://logo.clearbit.com/civitai.com", "status": "published", "isTrending": false, "isNew": true, "views": 181, "votes": 420, "rating": 4.7, "reviews": 420, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000000a"}, "name": "Grammarly", "slug": "grammarly", "description": "An AI writing assistant that improves grammar, tone, and clarity in written content.", "websiteUrl": "https://www.grammarly.com", "category": "AI for Text Enhancement", "tags": ["AI Writing", "Productivity", "Grammar"], "pricing": {"type": "freemium", "startingPrice": 12}, "features": ["Grammar Checks", "Tone Adjustments", "Plagiarism Detection"], "logo": "https://logo.clearbit.com/grammarly.com", "status": "published", "isTrending": true, "isNew": false, "views": 7, "votes": 1200, "rating": 4.7, "reviews": 1200, "__v": 0, "updatedAt": {"$date": "2025-04-16T20:27:48.330Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000011"}, "name": "Fireflies.ai", "slug": "fireflies-ai", "description": "An AI meeting assistant that records, transcribes, and summarizes meetings.", "websiteUrl": "https://fireflies.ai", "category": "AI for Productivity", "tags": ["AI Productivity", "Meeting Assistant", "Transcription"], "pricing": {"type": "freemium", "startingPrice": 10}, "features": ["Meeting Transcription", "Summarization", "Task Automation"], "logo": "https://logo.clearbit.com/fireflies.ai", "status": "published", "isTrending": true, "isNew": false, "views": 183, "votes": 150, "rating": 4.3, "reviews": 150, "__v": 0, "updatedAt": {"$date": "2025-04-14T17:42:17.650Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000029"}, "name": "DeepCode", "slug": "deepcode", "description": "An AI-powered code review tool that detects bugs and suggests improvements.", "websiteUrl": "https://www.deepcode.ai", "category": "AI for Coding and Development", "tags": ["AI Coding", "Code Review", "Developer Tools"], "pricing": {"type": "freemium", "startingPrice": 12}, "features": ["Code Review", "Bug Detection", "Code Improvement Suggestions"], "logo": "https://logo.clearbit.com/deepcode.ai", "status": "published", "isTrending": true, "isNew": false, "views": 112, "votes": 140, "rating": 4.3, "reviews": 140, "__v": 0, "updatedAt": {"$date": "2025-03-01T08:44:59.864Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000050"}, "name": "Stable Diffusion 3", "slug": "stable-diffusion-3", "description": "Next generation image generation model with breakthrough quality", "websiteUrl": "https://stability.ai", "category": "AI for Image Generation", "tags": ["AI Art", "Image Generation", "Creative"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Enhanced Image Quality", "Better Control", "Faster Generation"], "logo": "https://logo.clearbit.com/stability.ai", "status": "published", "isTrending": false, "isNew": false, "views": 70, "votes": 0, "rating": 0, "reviews": 0, "__v": 0, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "000000000000000000000053"}, "name": "NeutronField", "slug": "neutronfield", "description": "Marketplace for AI text-to-image prompts, helping artists and creators find and share effective prompts.", "websiteUrl": "https://neutronfield.com", "category": "AI for Inspiration", "tags": ["AI Art", "Prompts", "Marketplace"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Prompt Marketplace", "Community Sharing", "Prompt Testing"], "logo": "https://logo.clearbit.com/neutronfield.com", "status": "published", "isTrending": false, "isNew": true, "views": 50, "votes": 150, "rating": 4.3, "reviews": 150, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.017Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "000000000000000000000057"}, "name": "Typeface", "slug": "typeface", "description": "A sophisticated tool for generating personalized content with brand-specific voice and style.", "websiteUrl": "https://typeface.ai", "category": "AI for Marketing", "tags": ["Marketing", "Branding", "Content"], "pricing": {"type": "paid", "startingPrice": 29}, "features": ["Brand Voice Customization", "Content Generation", "Style Guidelines"], "logo": "https://logo.clearbit.com/typeface.ai", "status": "published", "isTrending": false, "isNew": true, "views": 90, "votes": 250, "rating": 4.6, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.017Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "000000000000000000000058"}, "name": "Figstack", "slug": "figstack", "description": "An AI-powered tool that helps developers quickly understand, document, and optimize their code with intelligent analysis.", "websiteUrl": "https://figstack.com", "category": "AI for Coding and Development", "tags": ["Development", "Code", "Documentation"], "pricing": {"type": "freemium", "startingPrice": 15}, "features": ["Code Analysis", "Documentation Generation", "Performance Optimization"], "logo": "https://logo.clearbit.com/figstack.com", "status": "published", "isTrending": false, "isNew": true, "views": 65, "votes": 180, "rating": 4.5, "reviews": 180, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.017Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "0000000000000000000000b2"}, "name": "Photosonic", "slug": "photosonic", "description": "Powerful online AI image generator with advanced style and composition controls.", "websiteUrl": "https://photosonic.ai", "category": "AI for Image Generation", "tags": ["Art", "Images", "Design"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Image Generation", "Style Control", "Composition Tools"], "logo": "https://logo.clearbit.com/photosonic.ai", "status": "published", "isTrending": false, "isNew": true, "views": 96, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000bd"}, "name": "Toucan", "slug": "toucan", "description": "Versatile AI platform for writing and chatbot content generation.", "websiteUrl": "https://toucan.ai", "category": "AI for Marketing", "tags": ["Content", "Chatbots", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Content Generation", "Chatbot Creation", "Marketing Copy"], "logo": "https://ui-avatars.com/api/?name=Toucan&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 70, "votes": 210, "rating": 4.5, "reviews": 210, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000c3"}, "name": "WordHero", "slug": "wordhero", "description": "Comprehensive AI writing tool with 70+ content generation options.", "websiteUrl": "https://wordhero.co", "category": "AI for Content Creation", "tags": ["Writing", "Content", "Marketing"], "pricing": {"type": "enterprise", "startingPrice": 49}, "features": ["Content Generation", "Multiple Formats", "Template Library"], "logo": "https://logo.clearbit.com/wordhero.co", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 340, "rating": 4.6, "reviews": 340, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000d4"}, "name": "Proface by Avatarize", "slug": "proface-by-avatarize", "description": "Professional AI-powered tool for creating high-quality headshots and profile pictures.", "websiteUrl": "https://proface.ai", "category": "AI for Image Generation", "tags": ["Headshots", "Photos", "Branding"], "pricing": {"type": "freemium", "startingPrice": 1999}, "features": ["Headshot Generation", "Profile Pictures", "Style Customization"], "logo": "https://logo.clearbit.com/proface.ai", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 290, "rating": 4.7, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000d9"}, "name": "Writesonic", "slug": "writesonic-1", "description": "AI writing assistant for creating high-quality content for various purposes.", "websiteUrl": "https://writesonic.com", "category": "AI for Writing", "tags": ["Writing", "Content", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 1267}, "features": ["Content Generation", "Blog Writing", "Marketing Copy"], "logo": "https://logo.clearbit.com/writesonic.com", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 290, "rating": 4.6, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000db"}, "name": "<PERSON><PERSON>", "slug": "tome", "description": "AI-powered storytelling format for creating engaging presentations.", "websiteUrl": "https://tome.app", "category": "AI for Content Creation", "tags": ["Presentations", "Design", "Storytelling"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Presentation Creation", "Story Generation", "Design Assistance"], "logo": "https://logo.clearbit.com/tome.app", "status": "published", "isTrending": false, "isNew": true, "views": 110, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000df"}, "name": "Synthesia", "slug": "synthesia-2", "description": "AI video generation platform for creating professional videos with virtual avatars.", "websiteUrl": "https://synthesia.io", "category": "AI for Video Creation", "tags": ["Video", "Avatars", "Content"], "pricing": {"type": "paid", "startingPrice": 30}, "features": ["Avatar Creation", "Video Generation", "<PERSON><PERSON>t to Video"], "logo": "https://logo.clearbit.com/synthesia.io", "status": "published", "isTrending": false, "isNew": true, "views": 110, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000e6"}, "name": "LearnSmart AI", "slug": "learnsmart-ai-1", "description": "Personalized learning platform that adapts to individual student needs and learning styles.", "websiteUrl": "https://learnsmart.ai", "category": "AI for Education", "tags": ["Education", "Learning", "Personalization"], "pricing": {"type": "freemium", "startingPrice": 1299}, "features": ["Adaptive Learning", "Progress Tracking", "Personalized Content"], "logo": "https://logo.clearbit.com/learnsmart.ai", "status": "published", "isTrending": false, "isNew": true, "views": 96, "votes": 290, "rating": 4.7, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000f7"}, "name": "PolyAI", "slug": "polyai", "description": "Advanced voice assistant platform to automate customer service interactions.", "websiteUrl": "https://poly.ai", "category": "AI for Audio Creation", "tags": ["Voice", "Customer Service", "Automation"], "pricing": {"type": "enterprise", "startingPrice": 0}, "features": ["Voice Automation", "Customer Service", "Natural Language Processing"], "logo": "https://logo.clearbit.com/poly.ai", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 280, "rating": 4.8, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "0000000000000000000000fb"}, "name": "AI Recipe Generator", "slug": "ai-recipe-generator", "description": "Create AI-generated recipes tailored to your available ingredients.", "websiteUrl": "https://airecipes.app", "category": "AI for Fun", "tags": ["Food", "Recipes", "Cooking"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Recipe Generation", "Ingredient Matching", "Cooking Instructions"], "logo": "https://logo.clearbit.com/airecipes.app", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 320, "rating": 4.4, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000143"}, "name": "D-ID", "slug": "d-id", "description": "AI platform for creating and animating digital humans.", "websiteUrl": "https://d-id.com", "category": "AI for Video", "tags": ["Video", "Animation", "Digital Humans"], "pricing": {"type": "paid", "startingPrice": 49}, "features": ["Digital Humans", "Animation", "Voice Synthesis"], "logo": "https://logo.clearbit.com/d-id.com", "status": "published", "isTrending": false, "isNew": true, "views": 88, "votes": 260, "rating": 4.7, "reviews": 260, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000144"}, "name": "Stability AI", "slug": "stability-ai", "description": "Open-source AI company developing advanced image and audio models.", "websiteUrl": "https://stability.ai", "category": "AI Research and Development", "tags": ["AI", "Research", "Open Source"], "pricing": {"type": "enterprise", "startingPrice": 0}, "features": ["Image Generation", "Audio Generation", "Model Development"], "logo": "https://logo.clearbit.com/stability.ai", "status": "published", "isTrending": false, "isNew": true, "views": 151, "votes": 380, "rating": 4.8, "reviews": 380, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000156"}, "name": "Anytype", "slug": "anytype", "description": "Open-source personal knowledge base with AI features.", "websiteUrl": "https://anytype.io", "category": "AI for Productivity", "tags": ["Knowledge", "Open Source", "Privacy"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Knowledge Management", "Note Taking", "AI Integration"], "logo": "https://logo.clearbit.com/anytype.io", "status": "published", "isTrending": false, "isNew": true, "views": 99, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000004"}, "name": "Stable Diffusion", "slug": "stable-diffusion", "description": "An open-source AI model for generating high-quality images from text prompts, widely used in creative projects.", "websiteUrl": "https://stability.ai", "category": "AI for Image Generation", "tags": ["AI Art", "Creative Tools", "Open Source"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Open Source", "Customizable", "High-Quality Images"], "logo": "https://logo.clearbit.com/stability.ai", "status": "published", "isTrending": true, "isNew": false, "views": 403, "votes": 741, "rating": 4.5, "reviews": 741, "__v": 0, "updatedAt": {"$date": "2025-04-16T13:49:41.978Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000008"}, "name": "<PERSON>", "slug": "jasper", "description": "An AI writing assistant for generating SEO-optimized content, including blogs, ads, and emails.", "websiteUrl": "https://www.jasper.ai", "category": "AI for Content Creation", "tags": ["AI Writing", "Marketing", "Productivity"], "pricing": {"type": "paid", "startingPrice": 49}, "features": ["SEO Optimization", "Content Templates", "Brand Voice"], "logo": "https://logo.clearbit.com/jasper.ai", "status": "published", "isTrending": true, "isNew": false, "views": 193, "votes": 236, "rating": 4.5, "reviews": 236, "__v": 0, "updatedAt": {"$date": "2025-04-14T07:33:33.629Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000000e"}, "name": "Writesonic", "slug": "writesonic", "description": "An AI writing assistant for generating blogs, ads, and product descriptions.", "websiteUrl": "https://writesonic.com", "category": "AI for Content Creation", "tags": ["AI Writing", "Content Creation", "Marketing"], "pricing": {"type": "freemium", "startingPrice": 19}, "features": ["Blog Writing", "Ad Copy", "Product Descriptions"], "logo": "https://logo.clearbit.com/writesonic.com", "status": "published", "isTrending": true, "isNew": false, "views": 305, "votes": 210, "rating": 4.3, "reviews": 210, "__v": 0, "updatedAt": {"$date": "2025-04-12T12:59:46.054Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000021"}, "name": "<PERSON>rtex", "slug": "cortex", "description": "An AI-powered content creation platform that helps businesses generate and optimize marketing content.", "websiteUrl": "https://www.meetcortex.com", "category": "AI for Content Creation", "tags": ["AI Content", "Marketing", "Analytics"], "pricing": {"type": "paid", "startingPrice": 99}, "features": ["Content Generation", "Performance Analytics", "Brand Voice"], "logo": "https://logo.clearbit.com/meetcortex.com", "status": "published", "isTrending": true, "isNew": false, "views": 156, "votes": 120, "rating": 4.2, "reviews": 120, "__v": 0, "updatedAt": {"$date": "2025-04-13T08:54:51.411Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000002e"}, "name": "Hootsuite", "slug": "hootsuite", "description": "An AI-powered social media management platform that helps businesses schedule posts, monitor engagement, and analyze performance.", "websiteUrl": "https://www.hootsuite.com", "category": "AI for Social Media", "tags": ["AI Social Media", "Management", "Analytics"], "pricing": {"type": "freemium", "startingPrice": 49}, "features": ["Social Media Scheduling", "Engagement Monitoring", "Analytics"], "logo": "https://logo.clearbit.com/hootsuite.com", "status": "published", "isTrending": true, "isNew": false, "views": 251, "votes": 180, "rating": 4.4, "reviews": 180, "__v": 0, "updatedAt": {"$date": "2025-03-02T19:57:41.405Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000034"}, "name": "Later", "slug": "later", "description": "An AI-powered Instagram scheduling tool that helps businesses plan and publish content on Instagram.", "websiteUrl": "https://later.com", "category": "AI for Social Media", "tags": ["AI Social Media", "Instagram", "Scheduling"], "pricing": {"type": "freemium", "startingPrice": 15}, "features": ["Instagram Scheduling", "Content Calendar", "Analytics"], "logo": "https://logo.clearbit.com/later.com", "status": "published", "isTrending": true, "isNew": false, "views": 193, "votes": 140, "rating": 4.3, "reviews": 140, "__v": 0, "updatedAt": {"$date": "2025-04-16T14:46:57.390Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000037"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "hirevue", "description": "An AI-powered video interviewing platform that helps recruiters assess candidates through video interviews and AI-driven insights.", "websiteUrl": "https://www.hirevue.com", "category": "AI for HR and Recruitment", "tags": ["AI Recruitment", "Video Interviews", "HR Tech"], "pricing": {"type": "paid", "startingPrice": 30000}, "features": ["Video Interviews", "AI Assessments", "Candidate <PERSON><PERSON>"], "logo": "https://logo.clearbit.com/hirevue.com", "status": "published", "isTrending": true, "isNew": false, "views": 252, "votes": 180, "rating": 4.5, "reviews": 180, "__v": 0, "updatedAt": {"$date": "2025-04-11T10:51:42.935Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000038"}, "name": "Pymetrics", "slug": "pymetrics", "description": "An AI-powered candidate assessment platform that uses neuroscience-based games to evaluate candidates' skills and potential.", "websiteUrl": "https://www.pymetrics.com", "category": "AI for HR and Recruitment", "tags": ["AI Recruitment", "Candidate Assessment", "HR Tech"], "pricing": {"type": "paid", "startingPrice": 10000}, "features": ["Neuroscience Games", "Skill Assessment", "Bias Reduction"], "logo": "https://logo.clearbit.com/pymetrics.com", "status": "published", "isTrending": true, "isNew": false, "views": 201, "votes": 160, "rating": 4.4, "reviews": 160, "__v": 0, "updatedAt": {"$date": "2025-03-03T11:31:43.782Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000052"}, "name": "VoiceGPT Pro", "slug": "voicegpt-pro", "description": "Advanced AI voice generation and cloning platform", "websiteUrl": "https://voicegpt.ai", "category": "AI for Voice", "tags": ["AI Voice", "Audio", "Content Creation"], "pricing": {"type": "paid", "startingPrice": 30}, "features": ["Voice Cloning", "Emotion Control", "Multiple Languages"], "logo": "https://ui-avatars.com/api/?name=VoiceGPT%20Pro&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": false, "views": 35, "votes": 0, "rating": 0, "reviews": 0, "__v": 0, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "000000000000000000000060"}, "name": "<PERSON>bot", "slug": "landbot", "description": "A comprehensive platform for automating customer service and support through AI-powered chatbots.", "websiteUrl": "https://landbot.io", "category": "AI Chatbots and Assistants", "tags": ["<PERSON><PERSON><PERSON>", "Customer Service", "Automation"], "pricing": {"type": "paid", "startingPrice": 29}, "features": ["Custom Chatbots", "Support Automation", "Analytics"], "logo": "https://logo.clearbit.com/landbot.io", "status": "published", "isTrending": false, "isNew": true, "views": 80, "votes": 250, "rating": 4.6, "reviews": 250, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000007e"}, "name": "superReply", "slug": "superreply", "description": "AI-powered email reply tool that helps craft professional and effective responses.", "websiteUrl": "https://superreply.ai", "category": "AI for Productivity", "tags": ["Email", "Communication", "Productivity"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Email Replies", "Template Management", "Tone Adjustment"], "logo": "https://logo.clearbit.com/superreply.ai", "status": "published", "isTrending": false, "isNew": true, "views": 60, "votes": 190, "rating": 4.5, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.018Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.018Z"}}, {"_id": {"$oid": "00000000000000000000008b"}, "name": "DatingbyAI", "slug": "<PERSON><PERSON><PERSON>", "description": "AI tool for creating optimized and authentic dating profiles across various platforms.", "websiteUrl": "https://datingbyai.com", "category": "AI for Self-Improvement", "tags": ["Dating", "Profiles", "Personal"], "pricing": {"type": "freemium", "startingPrice": 1499}, "features": ["Profile Generation", "Photo Selection", "Bio Writing"], "logo": "https://logo.clearbit.com/datingbyai.com", "status": "published", "isTrending": false, "isNew": true, "views": 65, "votes": 190, "rating": 4.5, "reviews": 190, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000a8"}, "name": "DiffusionBee", "slug": "diffusionbee", "description": "User-friendly Stable Diffusion interface specifically designed for Mac users.", "websiteUrl": "https://diffusionbee.com", "category": "AI for Image Generation", "tags": ["<PERSON>", "Image Generation", "Stable Diffusion"], "pricing": {"type": "free", "startingPrice": 0}, "features": ["Image Generation", "Mac Integration", "User-Friendly Interface"], "logo": "https://ui-avatars.com/api/?name=DiffusionBee&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 110, "votes": 320, "rating": 4.7, "reviews": 320, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000c7"}, "name": "No-Code AI Model Builder", "slug": "no-code-ai-model-builder", "description": "Create custom OpenAI models without coding using your own data.", "websiteUrl": "https://nocode-ai.dev", "category": "AI for Development", "tags": ["No-Code", "AI Development", "Machine Learning"], "pricing": {"type": "paid", "startingPrice": 99}, "features": ["Model Customization", "No-Code Interface", "Data Training"], "logo": "https://ui-avatars.com/api/?name=No-Code%20AI%20Model%20Builder&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 47, "votes": 170, "rating": 4.6, "reviews": 170, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "0000000000000000000000c8"}, "name": "Spatial.ai", "slug": "spatial-ai-1", "description": "Advanced platform for predicting and influencing customer behavior through AI analysis.", "websiteUrl": "https://spatial.ai", "category": "AI for Marketing", "tags": ["Analytics", "Customer Behavior", "Research"], "pricing": {"type": "paid", "startingPrice": 299}, "features": ["Behavior Analysis", "Prediction Models", "Customer Insights"], "logo": "https://logo.clearbit.com/spatial.ai", "status": "published", "isTrending": false, "isNew": true, "views": 82, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000107"}, "name": "ResearchGPT", "slug": "researchgpt-2", "description": "AI research assistant for academic and scientific research.", "websiteUrl": "https://researchgpt.ai", "category": "AI for Research", "tags": ["Research", "Academic", "Science"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Literature Review", "Citation Management", "Research Analysis"], "logo": "https://ui-avatars.com/api/?name=ResearchGPT&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 290, "rating": 4.6, "reviews": 290, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.021Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.021Z"}}, {"_id": {"$oid": "000000000000000000000124"}, "name": "Descript", "slug": "descript-2", "description": "AI-powered video and audio editing platform with transcription.", "websiteUrl": "https://descript.com", "category": "AI for Video and Audio", "tags": ["Video", "Audio", "Editing"], "pricing": {"type": "freemium", "startingPrice": 12}, "features": ["Video Editing", "Audio Editing", "Transcription"], "logo": "https://logo.clearbit.com/descript.com", "status": "published", "isTrending": false, "isNew": true, "views": 153, "votes": 380, "rating": 4.7, "reviews": 380, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000139"}, "name": "Mintlify", "slug": "mintlify", "description": "AI documentation writer and generator for developers.", "websiteUrl": "https://mintlify.com", "category": "AI for Documentation", "tags": ["Documentation", "Development", "AI"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Documentation Generation", "Code Documentation", "API Docs"], "logo": "https://logo.clearbit.com/mintlify.com", "status": "published", "isTrending": false, "isNew": true, "views": 92, "votes": 280, "rating": 4.7, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000013f"}, "name": "<PERSON><PERSON>", "slug": "<PERSON><PERSON>", "description": "AI video generation platform for creating stunning visual content.", "websiteUrl": "https://kaiber.ai", "category": "AI for Video", "tags": ["Video", "AI", "Creative"], "pricing": {"type": "paid", "startingPrice": 10}, "features": ["Video Generation", "Style Transfer", "Visual Effects"], "logo": "https://logo.clearbit.com/kaiber.ai", "status": "published", "isTrending": false, "isNew": true, "views": 100, "votes": 280, "rating": 4.7, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000020"}, "name": "Hootsuite Insights", "slug": "hootsuite-insights", "description": "An AI-powered social media analytics tool that helps businesses track and analyze their social media performance.", "websiteUrl": "https://www.hootsuite.com", "category": "AI for Social Media", "tags": ["AI Social Media", "Analytics", "Marketing"], "pricing": {"type": "paid", "startingPrice": 49}, "features": ["Social Media Analytics", "Sentiment Analysis", "Trend Tracking"], "logo": "https://logo.clearbit.com/hootsuite.com", "status": "published", "isTrending": true, "isNew": false, "views": 203, "votes": 160, "rating": 4.3, "reviews": 160, "__v": 0, "updatedAt": {"$date": "2025-04-13T14:36:08.225Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000024"}, "name": "TabNine", "slug": "tabnine", "description": "An AI-powered code completion tool that supports multiple programming languages.", "websiteUrl": "https://www.tabnine.com", "category": "AI for Coding and Development", "tags": ["AI Coding", "Developer Tools", "Code Completion"], "pricing": {"type": "freemium", "startingPrice": 12}, "features": ["Code Completion", "Multi-Language Support", "Context-Aware Suggestions"], "logo": "https://logo.clearbit.com/tabnine.com", "status": "published", "isTrending": true, "isNew": false, "views": 123, "votes": 180, "rating": 4.5, "reviews": 180, "__v": 0, "updatedAt": {"$date": "2025-02-24T23:46:34.016Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000026"}, "name": "Codeium", "slug": "codeium", "description": "An AI-powered code generation and optimization tool for developers.", "websiteUrl": "https://www.codeium.com", "category": "AI for Coding and Development", "tags": ["AI Coding", "Developer Tools", "Code Optimization"], "pricing": {"type": "freemium", "startingPrice": 15}, "features": ["Code Generation", "Code Optimization", "Multi-Language Support"], "logo": "https://logo.clearbit.com/codeium.com", "status": "published", "isTrending": true, "isNew": false, "views": 94, "votes": 130, "rating": 4.3, "reviews": 130, "__v": 0, "updatedAt": {"$date": "2025-04-12T02:52:33.858Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000055"}, "name": "Travelmoji", "slug": "travel<PERSON>ji", "description": "An AI-powered travel planning tool that creates personalized itineraries and recommendations.", "websiteUrl": "https://travelmoji.ai", "category": "AI for Self-Improvement", "tags": ["Travel", "Planning", "AI Assistant"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Personalized Itineraries", "Local Recommendations", "Travel Planning"], "logo": "https://ui-avatars.com/api/?name=Travelmoji&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 40, "votes": 120, "rating": 4.2, "reviews": 120, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.017Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.017Z"}}, {"_id": {"$oid": "000000000000000000000089"}, "name": "TweePT3", "slug": "tweept3", "description": "Automated tweet writing assistant powered by advanced language models.", "websiteUrl": "https://tweept3.ai", "category": "AI for Social Media", "tags": ["Twitter", "Social Media", "Content"], "pricing": {"type": "freemium", "startingPrice": 999}, "features": ["Tweet Generation", "Engagement Optimization", "Scheduling"], "logo": "https://ui-avatars.com/api/?name=TweePT3&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 50, "votes": 160, "rating": 4.4, "reviews": 160, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "00000000000000000000008c"}, "name": "Contentinator", "slug": "contentinator", "description": "Figma plugin for generating content, images, and copywriting with AI assistance.", "websiteUrl": "https://contentinator.com", "category": "AI for Design", "tags": ["Figma", "Design", "Content"], "pricing": {"type": "paid", "startingPrice": 1999}, "features": ["Content Generation", "Image Creation", "Copywriting"], "logo": "https://ui-avatars.com/api/?name=Contentinator&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 55, "votes": 170, "rating": 4.4, "reviews": 170, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000af"}, "name": "Delibr AI", "slug": "delibr-ai", "description": "AI-powered assistant helping product managers create high-quality documents efficiently.", "websiteUrl": "https://delibr.ai", "category": "AI for Productivity", "tags": ["Product Management", "Documentation", "Productivity"], "pricing": {"type": "paid", "startingPrice": 3499}, "features": ["Document Creation", "Product Management", "Collaboration"], "logo": "https://ui-avatars.com/api/?name=Delibr%20AI&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 80, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.019Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.019Z"}}, {"_id": {"$oid": "0000000000000000000000c4"}, "name": "On-Page.ai", "slug": "on-page-ai", "description": "Complete SEO suite with AI writing, detection, and optimization tools.", "websiteUrl": "https://on-page.ai", "category": "AI for Marketing", "tags": ["SEO", "Marketing", "Content"], "pricing": {"type": "paid", "startingPrice": 79}, "features": ["SEO Optimization", "Content Writing", "Link Building"], "logo": "https://logo.clearbit.com/on-page.ai", "status": "published", "isTrending": false, "isNew": true, "views": 75, "votes": 280, "rating": 4.7, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "0000000000000000000000ca"}, "name": "Peppertype", "slug": "peppertype-1", "description": "AI-powered tool for generating marketing sales copy and engaging blog content.", "websiteUrl": "https://peppertype.ai", "category": "AI for Content Creation", "tags": ["Marketing", "Content", "Copywriting"], "pricing": {"type": "freemium", "startingPrice": 2499}, "features": ["Sales Copy", "Blog Writing", "Content Generation"], "logo": "https://logo.clearbit.com/peppertype.ai", "status": "published", "isTrending": false, "isNew": true, "views": 85, "votes": 260, "rating": 4.6, "reviews": 260, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.020Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.020Z"}}, {"_id": {"$oid": "000000000000000000000114"}, "name": "Rewind", "slug": "rewind-1", "description": "Save anything, including conversations and make them searchable with AI.", "websiteUrl": "https://rewind.ai", "category": "AI for Productivity", "tags": ["Productivity", "Search", "Archive"], "pricing": {"type": "paid", "startingPrice": 2999}, "features": ["Conversation Storage", "Smart Search", "History Tracking"], "logo": "https://logo.clearbit.com/rewind.ai", "status": "published", "isTrending": false, "isNew": true, "views": 77, "votes": 240, "rating": 4.6, "reviews": 240, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000115"}, "name": "Optimo", "slug": "optimo-1", "description": "Comprehensive AI tool for marketing-related tasks and campaign optimization.", "websiteUrl": "https://optimo.ai", "category": "AI for Marketing", "tags": ["Marketing", "Analytics", "Optimization"], "pricing": {"type": "paid", "startingPrice": 4999}, "features": ["Campaign Optimization", "Marketing Analytics", "Performance Tracking"], "logo": "https://ui-avatars.com/api/?name=Optimo&background=6366f1&color=fff&bold=true&format=svg", "status": "published", "isTrending": false, "isNew": true, "views": 95, "votes": 310, "rating": 4.7, "reviews": 310, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}}, {"_id": {"$oid": "000000000000000000000131"}, "name": "Scribe", "slug": "scribe", "description": "AI tool for automatically creating step-by-step guides from screen recordings.", "websiteUrl": "https://scribe.how", "category": "AI for Documentation", "tags": ["Documentation", "Productivity", "Training"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Process Documentation", "Screen Recording", "Guide Creation"], "logo": "https://logo.clearbit.com/scribe.how", "status": "published", "isTrending": false, "isNew": true, "views": 93, "votes": 280, "rating": 4.6, "reviews": 280, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.022Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.022Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "000000000000000000000138"}, "name": "Tabnine", "slug": "tabnine-1", "description": "AI code completion tool for multiple programming languages.", "websiteUrl": "https://tabnine.com", "category": "AI for Development", "tags": ["Development", "Coding", "AI"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Code Completion", "Code Generation", "Language Support"], "logo": "https://logo.clearbit.com/tabnine.com", "status": "published", "isTrending": false, "isNew": true, "views": 152, "votes": 420, "rating": 4.6, "reviews": 420, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}, "isTopRated": false, "isUpcoming": false}, {"_id": {"$oid": "00000000000000000000015c"}, "name": "Matter", "slug": "matter", "description": "AI-powered reading app for articles and newsletters.", "websiteUrl": "https://getmatter.app", "category": "AI for Education", "tags": ["Reading", "Knowledge", "AI"], "pricing": {"type": "freemium", "startingPrice": 0}, "features": ["Article Reading", "AI Summary", "Knowledge Management"], "logo": "https://logo.clearbit.com/getmatter.app", "status": "published", "isTrending": false, "isNew": true, "views": 90, "votes": 260, "rating": 4.7, "reviews": 260, "__v": 0, "createdAt": {"$date": "2025-02-15T08:43:36.023Z"}, "updatedAt": {"$date": "2025-02-15T08:43:36.023Z"}}, {"_id": {"$oid": "67b08f0cbd2ee5260a932396"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "betterlearn", "description": "Ai for Education- Democratising Education with Ai tool such as BetterLearn", "websiteUrl": "https://betterlearn.com", "category": "AI Education", "tags": ["AI Education"], "pricing": {"type": "free"}, "features": ["Fre Free free"], "status": "published", "isTrending": false, "isNew": false, "isUpcoming": false, "isTopRated": false, "views": 1, "votes": 0, "rating": 0, "reviews": 0, "updatedAt": {"$date": "2025-04-12T06:54:42.834Z"}, "__v": 0}, {"_id": {"$oid": "67b092bbefff91e6707e3a4b"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "betterlearn-643", "description": "BetterLearn is revolutionizing the way we learn, connecting students and educators worldwide through innovative technology and collaborative learning experiences.\n\n", "websiteUrl": "https://betterlearn.us", "category": "AI Education", "tags": ["AI Education"], "pricing": {"type": "freemium"}, "features": ["Fre Free free"], "status": "approved", "isTrending": false, "isNew": false, "isUpcoming": false, "isTopRated": false, "views": 0, "votes": 0, "rating": 0, "reviews": 0, "updatedAt": {"$date": "2025-02-15T13:14:45.567Z"}, "__v": 0}, {"_id": {"$oid": "67f8fff61c4c32239ea3c9d1"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "betterlearn-687", "description": "as<PERSON><PERSON><PERSON> asdas<PERSON> s<PERSON><PERSON>", "websiteUrl": "https://growstack.ai", "category": "AI Education", "tags": ["AI Education"], "pricing": {"type": "paid"}, "features": ["Fre Free free"], "status": "rejected", "isTrending": false, "isNew": false, "isUpcoming": false, "isTopRated": false, "views": 0, "votes": 0, "rating": 0, "reviews": 0, "updatedAt": {"$date": "2025-04-11T11:42:09.164Z"}, "__v": 0}, {"_id": {"$oid": "67f901ab1c4c32239ea3ca0b"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "betterlearn-979", "description": "asdasd asdasdsadasd", "websiteUrl": "https://growstack.ai", "category": "AI Healthcare", "tags": ["AI Healthcare"], "pricing": {"type": "paid"}, "features": ["https://growstack.ai"], "status": "rejected", "isTrending": false, "isNew": false, "isUpcoming": false, "isTopRated": false, "views": 0, "votes": 0, "rating": 0, "reviews": 0, "updatedAt": {"$date": "2025-04-11T11:49:07.673Z"}, "__v": 0}, {"_id": {"$oid": "67f91fa91c4c32239ea3cc65"}, "name": "Test Tool", "slug": "test-tool", "description": "Testinging tool", "websiteUrl": "https://test.com", "category": "AI Finance", "tags": ["AI Finance"], "pricing": {"type": "free"}, "features": ["test"], "status": "approved", "isTrending": false, "isNew": false, "isUpcoming": false, "isTopRated": false, "views": 0, "votes": 0, "rating": 0, "reviews": 0, "updatedAt": {"$date": "2025-04-12T04:27:12.194Z"}, "__v": 0}, {"_id": {"$oid": "67fa0e0c1c4c32239ea3d329"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "betterlearn-949", "description": "BetterLearn is a Learning Ai tools", "websiteUrl": "https://betterlearn.com", "category": "AI Education", "tags": ["AI Education"], "pricing": {"type": "freemium"}, "features": ["Access to 40+ Tools", "Separate Educator Panel"], "status": "approved", "isTrending": false, "isNew": false, "isUpcoming": false, "isTopRated": false, "views": 0, "votes": 0, "rating": 0, "reviews": 0, "updatedAt": {"$date": "2025-04-12T06:54:26.777Z"}, "__v": 0}, {"_id": {"$oid": "67fc8b3a1c4c32239ea3dc8a"}, "name": "Sinatra AI", "slug": "sinatra-ai", "description": "Sinatra AI is an AI generator for poop lovers.", "websiteUrl": "https://google.com/", "category": "AI Art & Design", "tags": ["AI Art & Design"], "pricing": {"type": "paid"}, "features": ["tru475"], "status": "approved", "isTrending": false, "isNew": false, "isUpcoming": false, "isTopRated": false, "views": 0, "votes": 0, "rating": 0, "reviews": 0, "updatedAt": {"$date": "2025-04-14T04:14:22.602Z"}, "__v": 0}]