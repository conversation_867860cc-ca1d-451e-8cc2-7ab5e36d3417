<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Reference - Mindrix Documentation</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="documentation-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>Mindrix</h2>
                <p>Documentation</p>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="getting-started.html"><i class="fas fa-rocket"></i> Getting Started</a></li>
                    <li>
                        <a href="installation.html"><i class="fas fa-download"></i> Installation</a>
                        <ul class="submenu">
                            <li><a href="installation.html#frontend">Frontend Setup</a></li>
                            <li><a href="installation.html#backend">Backend Setup</a></li>
                            <li><a href="installation.html#env">Environment Variables</a></li>
                        </ul>
                    </li>
                    <li><a href="user-guide.html"><i class="fas fa-book"></i> User Guide</a></li>
                    <li><a href="api-reference.html" class="active"><i class="fas fa-code"></i> API Reference</a></li>
                    <li><a href="faq.html"><i class="fas fa-question-circle"></i> FAQ</a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <p>&copy; 2023 Mindrix</p>
            </div>
        </aside>

        <main class="content">
            <div class="content-header">
                <h1>API Reference</h1>
                <div class="search-container">
                    <input type="text" placeholder="Search documentation...">
                    <button><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="content-body">
                <section class="api-introduction">
                    <h2>Introduction</h2>
                    <p>The Mindrix inder API allows developers to access and integrate the data and functionality of the platform into their own applications. This reference provides information about the available endpoints, request parameters, response formats, and authentication requirements.</p>
                    
                    <h3>Base URL</h3>
                    <pre><code>https://api.aitoolfinder.com/v1</code></pre>
                    
                    <h3>Authentication</h3>
                    <p>All API requests require authentication using an API key. You can obtain an API key from your account settings on the AI Tool Finder platform.</p>
                    <p>Include your API key in the request headers as follows:</p>
                    <pre><code>Authorization: Bearer YOUR_API_KEY</code></pre>
                </section>

                <section class="api-tools">
                    <h2>Tools Endpoints</h2>
                    <p>These endpoints allow you to interact with AI tool data on the platform.</p>
                    
                    <div class="api-endpoint">
                        <div class="endpoint-header">
                            <span class="http-method method-get">GET</span>
                            <span class="endpoint-url">/tools</span>
                        </div>
                        <div class="endpoint-body">
                            <h4>List All Tools</h4>
                            <p>Returns a paginated list of AI tools based on the provided filters.</p>
                            
                            <h5>Query Parameters</h5>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>page</td>
                                        <td>integer</td>
                                        <td>No</td>
                                        <td>Page number (default: 1)</td>
                                    </tr>
                                    <tr>
                                        <td>limit</td>
                                        <td>integer</td>
                                        <td>No</td>
                                        <td>Number of results per page (default: 20, max: 100)</td>
                                    </tr>
                                    <tr>
                                        <td>category</td>
                                        <td>string</td>
                                        <td>No</td>
                                        <td>Filter by category slug</td>
                                    </tr>
                                    <tr>
                                        <td>search</td>
                                        <td>string</td>
                                        <td>No</td>
                                        <td>Search term to filter results</td>
                                    </tr>
                                    <tr>
                                        <td>sort</td>
                                        <td>string</td>
                                        <td>No</td>
                                        <td>Sort field (created_at, rating, views, default: created_at)</td>
                                    </tr>
                                    <tr>
                                        <td>order</td>
                                        <td>string</td>
                                        <td>No</td>
                                        <td>Sort order (asc, desc, default: desc)</td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <h5>Response</h5>
                            <pre><code>{
  "data": [
    {
      "id": "tool_123abc",
      "name": "Example AI Tool",
      "slug": "example-ai-tool",
      "description": "This is an example AI tool description.",
      "shortDescription": "Short description text",
      "category": {
        "id": "cat_456def",
        "name": "Content Creation",
        "slug": "content-creation"
      },
      "tags": ["writing", "content", "ai"],
      "rating": 4.5,
      "reviewCount": 120,
      "pricing": {
        "type": "freemium",
        "startingPrice": 0,
        "hasFreeVersion": true
      },
      "website": "https://example-ai-tool.com",
      "createdAt": "2023-01-15T12:00:00Z",
      "updatedAt": "2023-05-20T15:30:00Z"
    },
    // More tools...
  ],
  "meta": {
    "currentPage": 1,
    "totalPages": 25,
    "totalCount": 500,
    "limit": 20
  }
}</code></pre>
                        </div>
                    </div>
                    
                    <div class="api-endpoint">
                        <div class="endpoint-header">
                            <span class="http-method method-get">GET</span>
                            <span class="endpoint-url">/tools/{slug}</span>
                        </div>
                        <div class="endpoint-body">
                            <h4>Get Tool Details</h4>
                            <p>Returns detailed information about a specific AI tool.</p>
                            
                            <h5>Path Parameters</h5>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>slug</td>
                                        <td>string</td>
                                        <td>Yes</td>
                                        <td>The unique slug identifier of the tool</td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <h5>Response</h5>
                            <pre><code>{
  "data": {
    "id": "tool_123abc",
    "name": "Example AI Tool",
    "slug": "example-ai-tool",
    "description": "This is a detailed description of the example AI tool with all its features and capabilities.",
    "shortDescription": "Short description text",
    "category": {
      "id": "cat_456def",
      "name": "Content Creation",
      "slug": "content-creation"
    },
    "subcategory": {
      "id": "subcat_789ghi",
      "name": "Article Writing",
      "slug": "article-writing"
    },
    "tags": ["writing", "content", "ai", "articles", "blog"],
    "features": [
      "AI-powered content generation",
      "SEO optimization",
      "Grammar checking",
      "Multiple language support"
    ],
    "rating": 4.5,
    "reviewCount": 120,
    "pricing": {
      "type": "freemium",
      "startingPrice": 0,
      "hasFreeVersion": true,
      "tiers": [
        {
          "name": "Free",
          "price": 0,
          "billingPeriod": "monthly",
          "features": ["Basic content generation", "5 articles per month"]
        },
        {
          "name": "Pro",
          "price": 29.99,
          "billingPeriod": "monthly",
          "features": ["Advanced content generation", "Unlimited articles", "SEO tools"]
        }
      ]
    },
    "website": "https://example-ai-tool.com",
    "documentation": "https://docs.example-ai-tool.com",
    "github": "https://github.com/example-ai-tool",
    "twitter": "https://twitter.com/exampleaitool",
    "creator": {
      "id": "user_012jkl",
      "name": "AI Tool Labs",
      "website": "https://aitoollabs.com"
    },
    "screenshots": [
      {
        "url": "https://aitoolfinder.com/images/screenshots/example-tool-1.png",
        "caption": "Dashboard view"
      },
      {
        "url": "https://aitoolfinder.com/images/screenshots/example-tool-2.png",
        "caption": "Content editor"
      }
    ],
    "createdAt": "2023-01-15T12:00:00Z",
    "updatedAt": "2023-05-20T15:30:00Z"
  }
}</code></pre>
                        </div>
                    </div>
                    
                    <div class="api-endpoint">
                        <div class="endpoint-header">
                            <span class="http-method method-get">GET</span>
                            <span class="endpoint-url">/tools/{slug}/reviews</span>
                        </div>
                        <div class="endpoint-body">
                            <h4>Get Tool Reviews</h4>
                            <p>Returns a paginated list of reviews for a specific AI tool.</p>
                            
                            <h5>Path Parameters</h5>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>slug</td>
                                        <td>string</td>
                                        <td>Yes</td>
                                        <td>The unique slug identifier of the tool</td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <h5>Query Parameters</h5>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>page</td>
                                        <td>integer</td>
                                        <td>No</td>
                                        <td>Page number (default: 1)</td>
                                    </tr>
                                    <tr>
                                        <td>limit</td>
                                        <td>integer</td>
                                        <td>No</td>
                                        <td>Number of results per page (default: 20, max: 50)</td>
                                    </tr>
                                    <tr>
                                        <td>sort</td>
                                        <td>string</td>
                                        <td>No</td>
                                        <td>Sort field (created_at, rating, default: created_at)</td>
                                    </tr>
                                    <tr>
                                        <td>order</td>
                                        <td>string</td>
                                        <td>No</td>
                                        <td>Sort order (asc, desc, default: desc)</td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <h5>Response</h5>
                            <pre><code>{
  "data": [
    {
      "id": "review_345mno",
      "rating": 5,
      "title": "Amazing tool for content creation",
      "content": "This tool has completely transformed how I create content. Highly recommended!",
      "author": {
        "id": "user_678pqr",
        "name": "Jane Smith",
        "avatar": "https://aitoolfinder.com/images/avatars/jane-smith.png"
      },
      "upvotes": 24,
      "downvotes": 2,
      "createdAt": "2023-05-10T09:15:00Z"
    },
    // More reviews...
  ],
  "meta": {
    "currentPage": 1,
    "totalPages": 6,
    "totalCount": 120,
    "limit": 20
  }
}</code></pre>
                        </div>
                    </div>
                    
                    <div class="api-endpoint">
                        <div class="endpoint-header">
                            <span class="http-method method-post">POST</span>
                            <span class="endpoint-url">/tools</span>
                        </div>
                        <div class="endpoint-body">
                            <h4>Submit a Tool</h4>
                            <p>Submits a new AI tool to the platform. This endpoint requires authentication with an account that has tool submission permissions.</p>
                            
                            <h5>Request Body</h5>
                            <pre><code>{
  "name": "New AI Tool",
  "description": "Detailed description of the new AI tool.",
  "shortDescription": "Short description text",
  "categoryId": "cat_456def",
  "subcategoryId": "subcat_789ghi",
  "tags": ["writing", "content", "ai"],
  "features": [
    "AI-powered content generation",
    "SEO optimization",
    "Grammar checking"
  ],
  "pricing": {
    "type": "freemium",
    "startingPrice": 0,
    "hasFreeVersion": true,
    "tiers": [
      {
        "name": "Free",
        "price": 0,
        "billingPeriod": "monthly",
        "features": ["Basic content generation", "5 articles per month"]
      },
      {
        "name": "Pro",
        "price": 29.99,
        "billingPeriod": "monthly",
        "features": ["Advanced content generation", "Unlimited articles", "SEO tools"]
      }
    ]
  },
  "website": "https://new-ai-tool.com",
  "documentation": "https://docs.new-ai-tool.com",
  "github": "https://github.com/new-ai-tool",
  "twitter": "https://twitter.com/newaitool"
}</code></pre>
                            
                            <h5>Response</h5>
                            <pre><code>{
  "data": {
    "id": "tool_567stu",
    "name": "New AI Tool",
    "slug": "new-ai-tool",
    "status": "pending_review",
    "message": "Your tool has been submitted and is pending review."
  }
}</code></pre>
                        </div>
                    </div>
                </section>

                <section class="api-categories">
                    <h2>Categories Endpoints</h2>
                    <p>These endpoints allow you to retrieve information about the categories in the platform.</p>
                    
                    <div class="api-endpoint">
                        <div class="endpoint-header">
                            <span class="http-method method-get">GET</span>
                            <span class="endpoint-url">/categories</span>
                        </div>
                        <div class="endpoint-body">
                            <h4>List All Categories</h4>
                            <p>Returns a list of all categories and subcategories available on the platform.</p>
                            
                            <h5>Response</h5>
                            <pre><code>{
  "data": [
    {
      "id": "cat_456def",
      "name": "Content Creation",
      "slug": "content-creation",
      "description": "AI tools for creating various types of content",
      "icon": "pencil",
      "subcategories": [
        {
          "id": "subcat_789ghi",
          "name": "Article Writing",
          "slug": "article-writing",
          "description": "Tools specifically for writing articles and blog posts"
        },
        {
          "id": "subcat_901jkl",
          "name": "Image Generation",
          "slug": "image-generation",
          "description": "Tools for creating images and graphics using AI"
        }
      ]
    },
    {
      "id": "cat_234abc",
      "name": "Data Analysis",
      "slug": "data-analysis",
      "description": "AI tools for analyzing and visualizing data",
      "icon": "chart-bar",
      "subcategories": [
        {
          "id": "subcat_345def",
          "name": "Predictive Analytics",
          "slug": "predictive-analytics",
          "description": "Tools for predicting future trends from data"
        },
        {
          "id": "subcat_456ghi",
          "name": "Data Visualization",
          "slug": "data-visualization",
          "description": "Tools for creating visual representations of data"
        }
      ]
    }
    // More categories...
  ]
}</code></pre>
                        </div>
                    </div>
                    
                    <div class="api-endpoint">
                        <div class="endpoint-header">
                            <span class="http-method method-get">GET</span>
                            <span class="endpoint-url">/categories/{slug}</span>
                        </div>
                        <div class="endpoint-body">
                            <h4>Get Category Details</h4>
                            <p>Returns detailed information about a specific category, including its subcategories and top tools.</p>
                            
                            <h5>Path Parameters</h5>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>slug</td>
                                        <td>string</td>
                                        <td>Yes</td>
                                        <td>The unique slug identifier of the category</td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <h5>Response</h5>
                            <pre><code>{
  "data": {
    "id": "cat_456def",
    "name": "Content Creation",
    "slug": "content-creation",
    "description": "AI tools for creating various types of content",
    "icon": "pencil",
    "subcategories": [
      {
        "id": "subcat_789ghi",
        "name": "Article Writing",
        "slug": "article-writing",
        "description": "Tools specifically for writing articles and blog posts"
      },
      {
        "id": "subcat_901jkl",
        "name": "Image Generation",
        "slug": "image-generation",
        "description": "Tools for creating images and graphics using AI"
      }
    ],
    "topTools": [
      {
        "id": "tool_123abc",
        "name": "Example AI Tool",
        "slug": "example-ai-tool",
        "shortDescription": "Short description text",
        "rating": 4.5,
        "reviewCount": 120
      },
      // More top tools...
    ],
    "toolCount": 45
  }
}</code></pre>
                        </div>
                    </div>
                </section>

                <section class="api-user">
                    <h2>User Endpoints</h2>
                    <p>These endpoints allow you to access and manage user data. Most of these endpoints require authentication with user-specific permissions.</p>
                    
                    <div class="api-endpoint">
                        <div class="endpoint-header">
                            <span class="http-method method-get">GET</span>
                            <span class="endpoint-url">/user/profile</span>
                        </div>
                        <div class="endpoint-body">
                            <h4>Get User Profile</h4>
                            <p>Returns the profile information of the authenticated user.</p>
                            
                            <h5>Response</h5>
                            <pre><code>{
  "data": {
    "id": "user_678pqr",
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "avatar": "https://aitoolfinder.com/images/avatars/jane-smith.png",
    "bio": "AI enthusiast and software developer",
    "website": "https://janesmith.dev",
    "twitter": "https://twitter.com/janesmith",
    "github": "https://github.com/janesmith",
    "joined": "2023-01-05T10:30:00Z",
    "toolsSubmitted": 3,
    "reviewsWritten": 12
  }
}</code></pre>
                        </div>
                    </div>
                    
                    <div class="api-endpoint">
                        <div class="endpoint-header">
                            <span class="http-method method-get">GET</span>
                            <span class="endpoint-url">/user/favorites</span>
                        </div>
                        <div class="endpoint-body">
                            <h4>Get User Favorites</h4>
                            <p>Returns a list of tools that the authenticated user has saved as favorites.</p>
                            
                            <h5>Query Parameters</h5>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>page</td>
                                        <td>integer</td>
                                        <td>No</td>
                                        <td>Page number (default: 1)</td>
                                    </tr>
                                    <tr>
                                        <td>limit</td>
                                        <td>integer</td>
                                        <td>No</td>
                                        <td>Number of results per page (default: 20, max: 50)</td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <h5>Response</h5>
                            <pre><code>{
  "data": [
    {
      "id": "tool_123abc",
      "name": "Example AI Tool",
      "slug": "example-ai-tool",
      "shortDescription": "Short description text",
      "category": {
        "id": "cat_456def",
        "name": "Content Creation",
        "slug": "content-creation"
      },
      "rating": 4.5,
      "addedToFavoritesAt": "2023-03-15T14:20:00Z"
    },
    // More favorite tools...
  ],
  "meta": {
    "currentPage": 1,
    "totalPages": 2,
    "totalCount": 25,
    "limit": 20
  }
}</code></pre>
                        </div>
                    </div>
                </section>

                <section class="api-errors">
                    <h2>Error Handling</h2>
                    <p>The API uses conventional HTTP response codes to indicate the success or failure of an API request.</p>
                    <ul>
                        <li><strong>2xx:</strong> Success</li>
                        <li><strong>4xx:</strong> Client errors (e.g., invalid request, authentication issues)</li>
                        <li><strong>5xx:</strong> Server errors</li>
                    </ul>
                    
                    <h3>Error Response Format</h3>
                    <pre><code>{
  "error": {
    "code": "invalid_request",
    "message": "The request was unacceptable, often due to missing a required parameter.",
    "details": ["The 'name' field is required."]
  }
}</code></pre>
                    
                    <h3>Common Error Codes</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>invalid_request</td>
                                <td>The request was malformed or missing required parameters</td>
                            </tr>
                            <tr>
                                <td>authentication_required</td>
                                <td>The request requires authentication</td>
                            </tr>
                            <tr>
                                <td>invalid_token</td>
                                <td>The API key or token provided is invalid</td>
                            </tr>
                            <tr>
                                <td>forbidden</td>
                                <td>You do not have permission to access this resource</td>
                            </tr>
                            <tr>
                                <td>not_found</td>
                                <td>The requested resource does not exist</td>
                            </tr>
                            <tr>
                                <td>rate_limit_exceeded</td>
                                <td>You have exceeded the rate limit for API requests</td>
                            </tr>
                            <tr>
                                <td>server_error</td>
                                <td>An error occurred on the server while processing the request</td>
                            </tr>
                        </tbody>
                    </table>
                </section>

                <section class="api-rate-limiting">
                    <h2>Rate Limiting</h2>
                    <p>The API implements rate limiting to ensure fair usage and system stability. Rate limits depend on your account tier:</p>
                    <ul>
                        <li><strong>Free accounts:</strong> 100 requests per hour</li>
                        <li><strong>Developer accounts:</strong> 1,000 requests per hour</li>
                        <li><strong>Business accounts:</strong> 10,000 requests per hour</li>
                    </ul>
                    
                    <p>Rate limit information is included in the response headers:</p>
                    <pre><code>X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********</code></pre>
                    
                    <p>When you exceed the rate limit, you'll receive a 429 Too Many Requests response with a Retry-After header indicating when you can resume making requests.</p>
                </section>

                <section class="api-sdk">
                    <h2>Client Libraries</h2>
                    <p>We provide official client libraries in several languages to simplify API integration:</p>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <i class="fab fa-js"></i>
                            <h4>JavaScript</h4>
                            <p>JavaScript/TypeScript client for Node.js and browser applications.</p>
                            <a href="https://github.com/aitoolfinder/javascript-sdk" target="_blank">View on GitHub</a>
                        </div>
                        <div class="feature-card">
                            <i class="fab fa-python"></i>
                            <h4>Python</h4>
                            <p>Python client library with async support.</p>
                            <a href="https://github.com/aitoolfinder/python-sdk" target="_blank">View on GitHub</a>
                        </div>
                        <div class="feature-card">
                            <i class="fab fa-php"></i>
                            <h4>PHP</h4>
                            <p>PHP client library compatible with PHP 7.4 and above.</p>
                            <a href="https://github.com/aitoolfinder/php-sdk" target="_blank">View on GitHub</a>
                        </div>
                        <div class="feature-card">
                            <i class="fab fa-java"></i>
                            <h4>Java</h4>
                            <p>Java client library for JVM-based applications.</p>
                            <a href="https://github.com/aitoolfinder/java-sdk" target="_blank">View on GitHub</a>
                        </div>
                    </div>
                </section>

                <section class="api-support">
                    <h2>API Support</h2>
                    <p>If you have questions or need assistance with the API, you have several support options:</p>
                    <ul>
                        <li>Review our <a href="https://github.com/aitoolfinder/api-examples" target="_blank">API examples repository</a> for code samples</li>
                        <li>Join our <a href="https://discord.gg/aitoolfinder" target="_blank">Developer Discord community</a></li>
                        <li>Submit issues on the <a href="https://github.com/aitoolfinder/api-docs/issues" target="_blank">API documentation repository</a></li>
                        <li>Contact us directly at <a href="mailto:<EMAIL>"><EMAIL></a> for urgent matters</li>
                    </ul>
                </section>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html> 