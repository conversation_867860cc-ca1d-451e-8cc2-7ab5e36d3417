<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAQ - Mindrix Documentation</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .faq-item {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .faq-question {
            padding: 1rem;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid transparent;
            transition: all 0.3s ease;
        }
        
        .faq-question:hover {
            background-color: rgba(58, 134, 255, 0.05);
        }
        
        .faq-question i {
            transition: transform 0.3s ease;
        }
        
        .faq-answer {
            padding: 0 1rem;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .faq-item.active .faq-question {
            border-bottom: 1px solid var(--border-color);
        }
        
        .faq-item.active .faq-question i {
            transform: rotate(180deg);
        }
        
        .faq-item.active .faq-answer {
            padding: 1rem;
            max-height: 500px;
        }
        
        .faq-categories {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 2rem;
        }
        
        .faq-category {
            padding: 0.5rem 1rem;
            background-color: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .faq-category:hover, .faq-category.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="documentation-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>Mindrix</h2>
                <p>Documentation</p>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="getting-started.html"><i class="fas fa-rocket"></i> Getting Started</a></li>
                    <li>
                        <a href="installation.html"><i class="fas fa-download"></i> Installation</a>
                        <ul class="submenu">
                            <li><a href="installation.html#frontend">Frontend Setup</a></li>
                            <li><a href="installation.html#backend">Backend Setup</a></li>
                            <li><a href="installation.html#env">Environment Variables</a></li>
                        </ul>
                    </li>
                    <li><a href="user-guide.html"><i class="fas fa-book"></i> User Guide</a></li>
                    <li><a href="api-reference.html"><i class="fas fa-code"></i> API Reference</a></li>
                    <li><a href="faq.html" class="active"><i class="fas fa-question-circle"></i> FAQ</a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <p>&copy; 2023 Mindrix</p>
            </div>
        </aside>

        <main class="content">
            <div class="content-header">
                <h1>Frequently Asked Questions</h1>
                <div class="search-container">
                    <input type="text" placeholder="Search documentation...">
                    <button><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="content-body">
                <section class="faq-intro">
                    <h2>Common Questions</h2>
                    <p>Browse through these frequently asked questions to find quick answers to common queries about the AI Tool Finder platform.</p>
                    
                    <div class="alert info">
                        <strong>Updated for v1.0.4:</strong> New questions added regarding logo uploads, site customization, social media integration, and more.
                    </div>
                    
                    <div class="faq-categories">
                        <div class="faq-category active" data-category="all">All Questions</div>
                        <div class="faq-category" data-category="general">General</div>
                        <div class="faq-category" data-category="account">Account & Settings</div>
                        <div class="faq-category" data-category="tools">Tools & Submissions</div>
                        <div class="faq-category" data-category="technical">Technical</div>
                        <div class="faq-category" data-category="v104">New in v1.0.4</div>
                    </div>
                </section>

                <section class="faq-list">
                    <!-- General Questions -->
                    <div class="faq-item" data-category="general">
                        <div class="faq-question">
                            <span>What is Mindrix?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Mindrix is a comprehensive platform designed to help users discover, evaluate, and manage AI tools for various purposes. Our platform makes it easy to find the right AI solutions for specific tasks, compare different options, and keep track of your favorite tools.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="general">
                        <div class="faq-question">
                            <span>Is Mindrix free to use?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Yes, Mindrix is free for basic usage, including browsing, searching, and saving favorite tools. We also offer premium features for tool creators and organizations that want enhanced visibility and analytics.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="general">
                        <div class="faq-question">
                            <span>How are tools categorized on the platform?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Tools are organized into a hierarchical category system, including primary categories (like Content Creation, Data Analysis, etc.), subcategories for more specific classifications, and tags for cross-category searching. This structure helps users navigate through the vast landscape of AI tools more efficiently.</p>
                        </div>
                    </div>
                    
                    <!-- Account & Settings Questions -->
                    <div class="faq-item" data-category="account">
                        <div class="faq-question">
                            <span>How do I create an account?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>To create an account, click the "Sign Up" button in the top right corner of the homepage. You can register using your email address or through social login options like Google or GitHub. Follow the prompts to complete your profile setup.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="account">
                        <div class="faq-question">
                            <span>How can I change my account password?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>To change your password, log in to your account and navigate to your profile settings. Look for the "Security" or "Password" section, where you can enter your current password and set a new one. You can also use the "Forgot Password" option on the login page if you can't remember your current password.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="account">
                        <div class="faq-question">
                            <span>Can I delete my account?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Yes, you can delete your account at any time. Go to your account settings and look for the "Delete Account" option, usually found at the bottom of the settings page. You'll be asked to confirm this action, as it permanently removes your profile, saved tools, and submission history.</p>
                        </div>
                    </div>
                    
                    <!-- Tools & Submissions Questions -->
                    <div class="faq-item" data-category="tools">
                        <div class="faq-question">
                            <span>How can I submit my AI tool to the platform?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>To submit a tool, log in to your account and navigate to your dashboard. Click on the "Submit Tool" button and follow the guided submission process. You'll need to provide details like the tool name, description, category, features, pricing, and relevant links. Our team will review your submission before publishing it on the platform.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="tools">
                        <div class="faq-question">
                            <span>How are tool ratings calculated?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Tool ratings are calculated based on user reviews, with each review providing a rating from 1 to 5 stars. The overall rating is the weighted average of all reviews, with recent reviews having slightly more influence than older ones. We also implement measures to prevent spam or manipulated ratings.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="tools">
                        <div class="faq-question">
                            <span>Can I update my submitted tool's information?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Yes, as a tool owner, you can update your tool's information at any time. Go to your dashboard, find the tool you want to edit in the "My Submissions" section, and click the "Edit" button. Make your changes and submit them for review. Most updates are approved quickly, but substantial changes may require additional review.</p>
                        </div>
                    </div>
                    
                    <!-- New v1.0.4 Questions -->
                    <h3 class="faq-subcategory" data-category="v104">Image & Logo Management</h3>
                    
                    <div class="faq-item" data-category="v104 technical">
                        <div class="faq-question">
                            <span>Why can't I upload SVG files for my logo anymore?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>As of version 1.0.4, we've removed SVG support for logos and favicons to improve compatibility and security. Only PNG and JPG formats are now supported. If you need a transparent background, we recommend using PNG format. This change helps ensure consistent display across different browsers and devices.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="v104 technical">
                        <div class="faq-question">
                            <span>I'm getting a 500 error when uploading logos. How can I fix this?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>If you're experiencing 500 errors during logo uploads, here are some troubleshooting steps:</p>
                            <ol>
                                <li>Ensure you're using only PNG or JPG format (SVG is no longer supported)</li>
                                <li>Check that your file size is under 10MB</li>
                                <li>Verify your Cloudinary credentials are correct in your backend .env file</li>
                                <li>Try uploading a different image to see if the issue is specific to one file</li>
                                <li>Check server logs for more specific error details</li>
                            </ol>
                            <p>If Cloudinary is not configured correctly, the system will automatically use local storage as a fallback.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="v104 account">
                        <div class="faq-question">
                            <span>What's the difference between default, light, and dark mode logos?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>In version 1.0.4, we've added support for multiple logo variants:</p>
                            <ul>
                                <li><strong>Default Logo:</strong> The main site logo used when no specific mode is active</li>
                                <li><strong>Light Mode Logo:</strong> Shown when the site is in light mode (typically a darker logo for contrast)</li>
                                <li><strong>Dark Mode Logo:</strong> Shown when the site is in dark mode (typically a lighter logo for contrast)</li>
                            </ul>
                            <p>This allows for better branding consistency across different color schemes. You can upload different logos for each mode in Site Settings > Appearance.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="v104 account">
                        <div class="faq-question">
                            <span>Can I hide the site name and just show the logo?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Yes, version 1.0.4 adds an option to hide the site name text and only show the logo:</p>
                            <ol>
                                <li>Go to Admin Dashboard > Site Settings > Appearance</li>
                                <li>Under the logo settings, you'll find a toggle option labeled "Show site name next to logo"</li>
                                <li>Turn this toggle off to hide the site name text</li>
                                <li>Save your changes</li>
                            </ol>
                            <p>This is particularly useful when your logo already includes the site name or when you want a cleaner navigation bar. You can configure this independently for default, light, and dark mode logos.</p>
                        </div>
                    </div>
                    
                    <h3 class="faq-subcategory" data-category="v104">Cloudinary Integration</h3>
                    
                    <div class="faq-item" data-category="v104 technical">
                        <div class="faq-question">
                            <span>What is Cloudinary and do I need it for image uploads?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Cloudinary is a cloud-based image and video management service that we use for storing uploaded images. While it's recommended for production environments, it's not strictly required. In version 1.0.4, we've added automatic fallback to local storage when Cloudinary is not configured. You'll see a message indicating where your files are stored (Cloudinary or local storage) after each upload.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="v104 technical">
                        <div class="faq-question">
                            <span>How does the Cloudinary integration work in Mindrix?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>In version 1.0.4, Cloudinary integration works as follows:</p>
                            <ul>
                                <li>When configured, all image uploads (logos, favicons, etc.) are automatically stored in your Cloudinary account</li>
                                <li>This provides better performance, image optimization, and CDN delivery</li>
                                <li>If Cloudinary credentials are missing or invalid, the system automatically falls back to local storage</li>
                                <li>Images are validated before upload (format, size, dimensions)</li>
                                <li>After upload, the system displays where the file is stored (Cloudinary or local storage)</li>
                            </ul>
                            <p>This hybrid approach ensures uploads always work regardless of your configuration while providing better performance when Cloudinary is properly set up.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="v104 technical">
                        <div class="faq-question">
                            <span>What exactly do I need to configure in Cloudinary?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>For Mindrix's Cloudinary integration, you need these three pieces of information:</p>
                            <pre><code># Example configuration
CLOUDINARY_CLOUD_NAME=mycompany
CLOUDINARY_API_KEY=123456789012345
CLOUDINARY_API_SECRET=abcdefghijklmnopqrstuvwxyz123456</code></pre>
                            <p>You can find these values in your Cloudinary dashboard after signing up. No additional configuration is needed in the Cloudinary platform itself. The free tier (25GB storage, 25GB bandwidth) is sufficient for most installations.</p>
                            <p>See the <a href="installation.html#env">Installation Guide</a> for detailed setup instructions.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="v104 technical">
                        <div class="faq-question">
                            <span>What happens if I don't set up Cloudinary?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>If you don't set up Cloudinary, the system will automatically use local storage for all image uploads. This means:</p>
                            <ul>
                                <li>All files will be stored on your server instead of in the cloud</li>
                                <li>You'll see a message indicating "Stored in local storage" after uploads</li>
                                <li>Local storage works perfectly fine for most installations</li>
                                <li>For high-traffic sites, you might see better performance with Cloudinary</li>
                                <li>You can always add Cloudinary later - just update your .env file with the credentials</li>
                            </ul>
                            <p>The automatic fallback to local storage is a new feature in v1.0.4 that ensures uploads always work regardless of your cloud configuration.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="v104 technical">
                        <div class="faq-question">
                            <span>How do I know if my site is using Cloudinary or local storage?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>When you upload images in version 1.0.4, the system will display a message indicating where the file was stored:</p>
                            <ul>
                                <li>If you see "Successfully uploaded to Cloudinary", your Cloudinary integration is working</li>
                                <li>If you see "Stored in local storage", the system is using local storage (either because Cloudinary isn't configured or there was an issue with your Cloudinary credentials)</li>
                            </ul>
                            <p>You can also check the URLs of your images - Cloudinary URLs typically contain <code>res.cloudinary.com</code> in the path.</p>
                        </div>
                    </div>
                    
                    <h3 class="faq-subcategory" data-category="v104">Site Customization & Social Media</h3>
                    
                    <div class="faq-item" data-category="v104 account">
                        <div class="faq-question">
                            <span>How do I set up social media links in the footer?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>As an administrator, you can now add social media links that appear in the site footer:</p>
                            <ol>
                                <li>Go to the Admin Dashboard</li>
                                <li>Navigate to Site Settings > Social Media</li>
                                <li>Enter the full URLs for any platforms you want to display (Twitter/X, Facebook, Instagram, LinkedIn, GitHub)</li>
                                <li>Leave any field blank to hide that social media icon</li>
                                <li>Click "Save Settings" to update the footer</li>
                            </ol>
                            <p>This feature is new in version 1.0.4 and helps increase your platform's social media presence.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="v104 account">
                        <div class="faq-question">
                            <span>Can I add custom CSS or JavaScript to my site?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Yes, version 1.0.4 adds the ability to inject custom CSS and JavaScript:</p>
                            <ol>
                                <li>Go to Admin Dashboard > Site Settings > Custom Code</li>
                                <li>Add your custom CSS in the CSS editor</li>
                                <li>Add your custom JavaScript in the JavaScript editor</li>
                                <li>Save your changes</li>
                            </ol>
                            <p>This feature allows for advanced customization without modifying the core code. Use it carefully, as improper code can affect site functionality. We recommend testing any changes in a development environment first.</p>
                        </div>
                    </div>
                    
                    <h3 class="faq-subcategory" data-category="v104">Analytics & SEO</h3>
                    
                    <div class="faq-item" data-category="v104 technical">
                        <div class="faq-question">
                            <span>How do I set up Google Analytics with Mindrix?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Version 1.0.4 includes Google Analytics 4 integration. To set it up:</p>
                            <ol>
                                <li>Create a Google Analytics 4 property in your Google Analytics account</li>
                                <li>Obtain your Measurement ID (format: G-XXXXXXXXXX)</li>
                                <li>In the Admin Dashboard, go to Site Settings > Analytics</li>
                                <li>Enter your Measurement ID and save</li>
                            </ol>
                            <p>Once configured, page views will be automatically tracked across your application. You can view the analytics data in your Google Analytics dashboard.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="v104 technical">
                        <div class="faq-question">
                            <span>How do I customize meta tags for better SEO?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Version 1.0.4 introduces enhanced meta tags management:</p>
                            <ol>
                                <li>Go to Admin Dashboard > Site Settings > Meta Tags</li>
                                <li>Set up your SEO meta tags:
                                    <ul>
                                        <li>Default page title format</li>
                                        <li>Meta description</li>
                                        <li>Open Graph title and description (for social sharing)</li>
                                        <li>Upload an Open Graph image (shown when your site is shared on social media)</li>
                                    </ul>
                                </li>
                                <li>Save your changes</li>
                            </ol>
                            <p>These settings improve how your site appears in search results and social media shares, potentially increasing traffic.</p>
                        </div>
                    </div>
                    
                    <!-- Technical Questions -->
                    <div class="faq-item" data-category="technical">
                        <div class="faq-question">
                            <span>What technologies does AI Tool Finder use?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>AI Tool Finder is built using modern web technologies, with a React frontend and a Node.js backend. We use MongoDB for data storage, and our API follows RESTful design principles. The platform also employs various AI techniques for smart search and recommendations.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="technical">
                        <div class="faq-question">
                            <span>Is there an API available for developers?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Yes, we offer a comprehensive API that allows developers to integrate AI Tool Finder data into their applications. The API provides endpoints for retrieving tool information, categories, and other relevant data. For access to the API, you'll need to register for an API key through your account settings. Check our <a href="api-reference.html">API Reference</a> for detailed documentation.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item" data-category="technical">
                        <div class="faq-question">
                            <span>How can I set up my own instance of AI Tool Finder?</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>To set up your own instance, you'll need to deploy both the frontend and backend components. Our <a href="installation.html">Installation Guide</a> provides step-by-step instructions for this process. You'll need a MongoDB database, a Node.js environment, and optionally a Vercel account for frontend deployment. The codebase is designed to be easily customizable to fit your specific requirements.</p>
                        </div>
                    </div>
                </section>

                <section class="still-need-help">
                    <h2>Still Need Help?</h2>
                    <p>If you couldn't find the answer to your question in this FAQ, you have several options:</p>
                    <ul>
                        <li>Check our comprehensive <a href="user-guide.html">User Guide</a> for detailed information</li>
                        <li>Explore the <a href="installation.html">Installation Guide</a> for technical setup questions</li>
                        <li>Contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li>Join our community forum to ask questions and get help from other users</li>
                    </ul>
                </section>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // FAQ toggling
            const faqItems = document.querySelectorAll('.faq-item');
            
            faqItems.forEach(function(item) {
                const question = item.querySelector('.faq-question');
                
                question.addEventListener('click', function() {
                    item.classList.toggle('active');
                });
            });
            
            // FAQ category filtering
            const categoryButtons = document.querySelectorAll('.faq-category');
            
            categoryButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    // Update active state on buttons
                    categoryButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    
                    const category = button.getAttribute('data-category');
                    
                    faqItems.forEach(function(item) {
                        if (category === 'all' || item.getAttribute('data-category') === category) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
</body>
</html> 