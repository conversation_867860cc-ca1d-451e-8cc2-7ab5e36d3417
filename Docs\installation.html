<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation - AI Tool Finder Documentation</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="documentation-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>AI Tool Finder</h2>
                <p>Documentation</p>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="getting-started.html"><i class="fas fa-rocket"></i> Getting Started</a></li>
                    <li>
                        <a href="installation.html" class="active"><i class="fas fa-download"></i> Installation</a>
                        <ul class="submenu">
                            <li><a href="#frontend">Frontend Setup</a></li>
                            <li><a href="#backend">Backend Setup</a></li>
                            <li><a href="#env">Environment Variables</a></li>
                        </ul>
                    </li>
                    <li><a href="user-guide.html"><i class="fas fa-book"></i> User Guide</a></li>
                    <li><a href="api-reference.html"><i class="fas fa-code"></i> API Reference</a></li>
                    <li><a href="faq.html"><i class="fas fa-question-circle"></i> FAQ</a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <p>&copy; 2023 AI Tool Finder</p>
            </div>
        </aside>

        <main class="content">
            <div class="content-header">
                <h1>Installation Guide</h1>
                <div class="search-container">
                    <input type="text" placeholder="Search documentation...">
                    <button><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="content-body">
                <section class="installation-overview">
                    <h2>Overview</h2>
                    <p>This guide will walk you through the process of setting up and deploying the AI Tool Finder platform. The application consists of two primary components:</p>
                    <ul>
                        <li><strong>Frontend:</strong> A React-based web interface that users interact with.</li>
                        <li><strong>Backend:</strong> A Node.js server that handles data processing, storage, and API endpoints.</li>
                    </ul>
                    <p>Follow the steps below to set up and deploy both components of the application.</p>
                </section>

                <section id="prerequisites" class="installation-prerequisites">
                    <h2>Prerequisites</h2>
                    <p>Before you begin, ensure you have the following:</p>
                    <ul>
                        <li>Node.js (v14.x or later) and npm (v6.x or later) installed</li>
                        <li>Git for version control</li>
                        <li>MongoDB database (local or cloud-based like MongoDB Atlas)</li>
                        <li>Vercel account (for frontend deployment)</li>
                        <li>GitHub account (recommended for source control)</li>
                        <li>Clerk account for authentication services</li>
                        <li>Cloudinary account for image storage (recommended but optional - local storage fallback available)</li>
                        <li>Basic knowledge of JavaScript and React</li>
                    </ul>

                    <div class="alert info">
                        <strong>Version 1.0.4 Update:</strong> Logo and favicon uploads now support only PNG and JPG formats for improved compatibility. SVG support has been removed. The system will automatically use Cloudinary for storage if configured, or fall back to local storage.
                    </div>
                </section>

                <section id="frontend" class="installation-frontend">
                    <h2>Frontend Deployment</h2>
                    <p>The AI Tool Finder frontend is built with React and can be deployed on Vercel for optimal performance. Follow the steps below to set up and deploy the frontend.</p>
                    
                    <div class="video-container">
                        <h3>Frontend Deployment on Vercel</h3>
                        <p>Watch this tutorial for a step-by-step guide on deploying the frontend to Vercel:</p>
                        <iframe width="560" height="315" src="https://www.youtube.com/embed/2vynWpPTmek?si=aVpbIF0uFxnrYf0c" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                    </div>

                    <div class="installation-steps">
                        <h3>Manual Setup Steps</h3>
                        
                        <div class="step-card">
                            <h4><span class="step-number">1</span> Clone the Repository</h4>
                            <p>Clone the frontend repository to your local machine:</p>
                            <pre><code>git clone https://github.com/yourusername/ai-tool-finder-frontend.git
cd ai-tool-finder-frontend</code></pre>
                        </div>
                        
                        <div class="step-card">
                            <h4><span class="step-number">2</span> Install Dependencies</h4>
                            <p>Install the required dependencies:</p>
                            <pre><code>npm install</code></pre>
                        </div>
                        
                        <div class="step-card">
                            <h4><span class="step-number">3</span> Configure Environment Variables</h4>
                            <p>Create a <code>.env</code> file in the root directory based on the <code>.env.example</code> file. See the <a href="#env">Environment Variables</a> section for details.</p>
                        </div>
                        
                        <div class="step-card">
                            <h4><span class="step-number">4</span> Build the Application</h4>
                            <p>Build the frontend application:</p>
                            <pre><code>npm run build</code></pre>
                            <p>This will create a <code>dist</code> directory with the built application.</p>
                        </div>
                        
                        <div class="step-card">
                            <h4><span class="step-number">5</span> Deploy to Vercel</h4>
                            <p>The simplest way to deploy is using the Vercel CLI:</p>
                            <pre><code>npm install -g vercel
vercel login
vercel</code></pre>
                            <p>Follow the prompts to complete the deployment.</p>
                        </div>
                    </div>
                </section>

                <section id="backend" class="installation-backend">
                    <h2>Backend Deployment</h2>
                    <p>The AI Tool Finder backend is a Node.js application that can be deployed to various platforms. Follow these steps to set up and deploy the backend.</p>
                    
                    <div class="video-container">
                        <h3>Backend Deployment Tutorial</h3>
                        <p>Watch this tutorial for a detailed walkthrough of the backend deployment process:</p>
                        <iframe width="560" height="315" src="https://www.youtube.com/embed/ohJ3vg17q-0?si=CUvYQd0lIGu45gRF" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                    </div>

                    <div class="installation-steps">
                        <h3>Manual Setup Steps</h3>
                        
                        <div class="step-card">
                            <h4><span class="step-number">1</span> Clone the Repository</h4>
                            <p>Clone the backend repository to your local machine:</p>
                            <pre><code>git clone https://github.com/yourusername/ai-tool-finder-backend.git
cd ai-tool-finder-backend</code></pre>
                        </div>
                        
                        <div class="step-card">
                            <h4><span class="step-number">2</span> Install Dependencies</h4>
                            <p>Install the required dependencies:</p>
                            <pre><code>npm install</code></pre>
                        </div>
                        
                        <div class="step-card">
                            <h4><span class="step-number">3</span> Configure Environment Variables</h4>
                            <p>Create a <code>.env</code> file in the root directory based on the <code>.env.example</code> file. See the <a href="#env">Environment Variables</a> section for details.</p>
                        </div>
                        
                        <div class="step-card">
                            <h4><span class="step-number">4</span> Build the Application</h4>
                            <p>Build the backend application:</p>
                            <pre><code>npm run build</code></pre>
                        </div>
                        
                        <div class="step-card">
                            <h4><span class="step-number">5</span> Deploy the Backend</h4>
                            <p>You can deploy the backend to various platforms such as:</p>
                            <ul>
                                <li><strong>Heroku:</strong> Use the Heroku CLI or GitHub integration</li>
                                <li><strong>DigitalOcean:</strong> Set up a Droplet or App Platform</li>
                                <li><strong>AWS:</strong> Deploy to EC2, Elastic Beanstalk, or Lambda</li>
                                <li><strong>Google Cloud:</strong> Deploy to App Engine or Cloud Run</li>
                            </ul>
                            <p>Each platform has its own deployment process. Refer to the platform's documentation for specific instructions.</p>
                        </div>
                    </div>
                </section>

                <section id="env" class="installation-env">
                    <h2>Environment Variables Setup</h2>
                    <p>Environment variables are crucial for the secure and proper functioning of the AI Tool Finder platform. This section explains how to set up and manage these variables.</p>
                    
                    <div class="video-container">
                        <h3>Setting Up Environment Variables</h3>
                        <p>Watch this tutorial for a comprehensive guide on configuring environment variables:</p>
                        <iframe width="560" height="315" src="https://www.youtube.com/embed/qvbJP5lO1Rs?si=I2xk0V-c04jv208b" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                    </div>

                    <div class="installation-steps">
                        <h3>Frontend Environment Variables</h3>
                        <p>Create a <code>.env</code> file in the frontend root directory with the following variables (based on <code>.env.example</code>):</p>
                        <pre><code># Clerk Authentication Configuration
VITE_CLERK_SECRET_KEY=Your_Clerk_Secret_Key
VITE_CLERK_PUBLISHABLE_KEY=Your_Clerk_Publishable_Key

# Server Configuration
PORT=3001

# Frontend Configuration
VITE_API_URL=Your_Backend_URL
VITE_CLERK_SIGN_IN_URL=/sign-in
VITE_CLERK_SIGN_UP_URL=/sign-up
VITE_CLERK_AFTER_SIGN_IN_URL=/
VITE_CLERK_AFTER_SIGN_UP_URL=/

# Google Analytics (New in v1.0.4)
VITE_GA_MEASUREMENT_ID=Your_GA4_Measurement_ID

# MongoDB Configuration
MONGODB_URI=Your_MongoDB_URI

# NOTE: Replace all placeholder values with your actual API keys and configuration
# DO NOT commit your actual API keys to version control</code></pre>

                        <h3>Backend Environment Variables</h3>
                        <p>Create a <code>.env</code> file in the backend root directory with the following variables (based on <code>.env.example</code>):</p>
                        <pre><code># Server Configuration
PORT=5000
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=Your_MongoDB_URI

# Clerk Authentication
CLERK_SECRET_KEY=Your_Clerk_Secret_Key

# Cloudinary Configuration (New in v1.0.4)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Logging and Security
LOG_LEVEL=info
ENABLE_RATE_LIMIT=true
CORS_ORIGIN=Your_Frontend_URL

# NOTE: Replace all placeholder values with your actual API keys and configuration
# DO NOT commit your actual API keys to version control</code></pre>

                        <h4>Cloudinary Configuration Example</h4>
                        <p>Here's an example of how your Cloudinary configuration might look with actual values:</p>
                        <pre><code># Cloudinary Configuration Example
CLOUDINARY_CLOUD_NAME=mycompany
CLOUDINARY_API_KEY=***************
CLOUDINARY_API_SECRET=abcdefghijklmnopqrstuvwxyz123456</code></pre>

                        <p>To find these values in your Cloudinary account:</p>
                        <ol>
                            <li>Log in to your Cloudinary account at <a href="https://cloudinary.com/console" target="_blank">cloudinary.com/console</a></li>
                            <li>The Dashboard home page displays your Cloud name, API Key, and API Secret</li>
                            <li>You can also find these under Settings > Access Keys</li>
                        </ol>
                        
                        <div class="alert info">
                            <p><strong>Testing Your Cloudinary Configuration:</strong> After setting up your Cloudinary credentials, you can test they're working properly by going to Admin Settings > Appearance and attempting to upload a logo or favicon. The system will tell you whether it's using Cloudinary or local storage.</p>
                        </div>
                    </div>

                    <div class="step-card">
                        <h4>Setting Up Cloudinary (New in v1.0.4)</h4>
                        <p>Cloudinary is used for image storage in AI Tool Finder. Follow these steps to set up your Cloudinary account:</p>
                        <ol>
                            <li>Sign up for a free Cloudinary account at <a href="https://cloudinary.com" target="_blank">cloudinary.com</a></li>
                            <li>After logging in, find your account details in the Dashboard:</li>
                            <ul>
                                <li>Cloud name</li>
                                <li>API Key</li>
                                <li>API Secret</li>
                            </ul>
                            <li>Add these credentials to your backend <code>.env</code> file as shown above</li>
                        </ol>
                        <p><strong>Note:</strong> If Cloudinary credentials are missing or invalid, the system will automatically fall back to using local storage for all uploads. You'll see a message indicating where your files are being stored after each upload.</p>
                    </div>

                    <div class="step-card">
                        <h4>Securing Environment Variables</h4>
                        <p>Always follow these best practices for environment variables:</p>
                        <ul>
                            <li>Never commit <code>.env</code> files to your repository</li>
                            <li>Add <code>.env</code> to your <code>.gitignore</code> file</li>
                            <li>Use different environment variables for development, testing, and production</li>
                            <li>Regularly rotate secrets and API keys</li>
                            <li>Restrict environment variable access to only those who need it</li>
                        </ul>
                    </div>
                </section>

                <section class="troubleshooting">
                    <h2>Troubleshooting</h2>
                    <p>If you encounter issues during the installation or deployment process, try these common solutions:</p>
                    
                    <div class="step-card">
                        <h4>Common Frontend Issues</h4>
                        <ul>
                            <li><strong>Build failures:</strong> Check for syntax errors or missing dependencies</li>
                            <li><strong>API connection errors:</strong> Verify your environment variables and CORS configuration</li>
                            <li><strong>Routing issues:</strong> Ensure your hosting platform is configured for single-page applications</li>
                            <li><strong>Authentication errors:</strong> Verify your Clerk API keys and configuration</li>
                        </ul>
                    </div>
                    
                    <div class="step-card">
                        <h4>Image Upload Issues (New in v1.0.4)</h4>
                        <ul>
                            <li><strong>500 errors on upload:</strong> Check your Cloudinary configuration and ensure API keys are correct</li>
                            <li><strong>Format errors:</strong> Only PNG and JPG formats are supported. SVG files are no longer accepted</li>
                            <li><strong>Size limitations:</strong> Ensure uploaded images are under the size limit (10MB)</li>
                            <li><strong>Local storage fallback:</strong> If you see a message about local storage being used, this is normal when Cloudinary is not configured</li>
                            <li><strong>Preview issues:</strong> If logo previews don't appear, try refreshing the page or uploading a different image</li>
                        </ul>
                    </div>
                    
                    <div class="step-card">
                        <h4>Common Backend Issues</h4>
                        <ul>
                            <li><strong>Database connection errors:</strong> Verify your MongoDB connection string and network access</li>
                            <li><strong>Authentication failures:</strong> Check your Clerk API key and configuration</li>
                            <li><strong>Server crashes:</strong> Examine logs for unhandled exceptions or resource limitations</li>
                            <li><strong>CORS issues:</strong> Ensure your CORS_ORIGIN is properly set to your frontend URL</li>
                        </ul>
                    </div>
                    
                    <p>For more detailed troubleshooting, refer to the <a href="faq.html">FAQ section</a> or contact our support team.</p>
                </section>

                <section class="next-steps">
                    <h2>Next Steps</h2>
                    <p>After successfully deploying both the frontend and backend, you should:</p>
                    <ul>
                        <li>Create an admin account to manage the platform</li>
                        <li>Configure the initial categories and settings</li>
                        <li>Test all functionality to ensure everything works as expected</li>
                        <li>Set up monitoring and alerts to stay informed about system health</li>
                        <li>Implement regular backups of your database</li>
                    </ul>
                    <p>Continue to the <a href="user-guide.html">User Guide</a> to learn how to use and manage the AI Tool Finder platform effectively.</p>
                </section>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html> 