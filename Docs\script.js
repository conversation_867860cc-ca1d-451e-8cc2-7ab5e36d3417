// Mobile menu toggle
document.addEventListener('DOMContentLoaded', function() {
    // Create mobile menu toggle
    const contentElement = document.querySelector('.content');
    
    if (contentElement) {
        const menuToggle = document.createElement('button');
        menuToggle.className = 'menu-toggle';
        menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
        document.body.appendChild(menuToggle);
        
        const sidebar = document.querySelector('.sidebar');
        
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            
            if (sidebar.classList.contains('active')) {
                menuToggle.innerHTML = '<i class="fas fa-times"></i>';
            } else {
                menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
            }
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const isMenuToggle = event.target.closest('.menu-toggle');
            const isSidebar = event.target.closest('.sidebar');
            
            if (!isMenuToggle && !isSidebar && sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
                menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
            }
        });
    }
    
    // Search functionality
    const searchInput = document.querySelector('.search-container input');
    const searchButton = document.querySelector('.search-container button');
    
    if (searchInput && searchButton) {
        searchButton.addEventListener('click', function() {
            performSearch(searchInput.value);
        });
        
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch(searchInput.value);
            }
        });
    }
    
    // Submenu toggle for mobile
    const subMenuParents = document.querySelectorAll('.sidebar-nav li');
    
    subMenuParents.forEach(function(parent) {
        const submenu = parent.querySelector('.submenu');
        
        if (submenu) {
            const link = parent.querySelector('a');
            
            if (window.innerWidth <= 767) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const style = window.getComputedStyle(submenu);
                    
                    if (style.display === 'none') {
                        submenu.style.display = 'block';
                    } else {
                        submenu.style.display = 'none';
                    }
                });
            }
        }
    });
    
    // Code block copy functionality
    const codeBlocks = document.querySelectorAll('pre');
    
    codeBlocks.forEach(function(block) {
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-button';
        copyButton.innerHTML = '<i class="fas fa-copy"></i>';
        copyButton.title = 'Copy to clipboard';
        copyButton.style.position = 'absolute';
        copyButton.style.top = '0.5rem';
        copyButton.style.right = '0.5rem';
        copyButton.style.padding = '0.25rem 0.5rem';
        copyButton.style.fontSize = '0.8rem';
        copyButton.style.zIndex = '10';
        
        // Ensure the code block's position is relative
        block.style.position = 'relative';
        block.appendChild(copyButton);
        
        copyButton.addEventListener('click', function() {
            const code = block.querySelector('code') || block;
            const textToCopy = code.textContent;
            
            navigator.clipboard.writeText(textToCopy).then(function() {
                copyButton.innerHTML = '<i class="fas fa-check"></i>';
                setTimeout(function() {
                    copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                copyButton.innerHTML = '<i class="fas fa-times"></i>';
                setTimeout(function() {
                    copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                }, 2000);
            });
        });
    });
    
    // Highlight current page in navigation
    highlightCurrentPage();
    
    // Initialize image lightbox if any
    initImageLightbox();
});

// Search functionality
function performSearch(query) {
    if (!query.trim()) return;
    
    query = query.toLowerCase();
    const contentSections = document.querySelectorAll('.content-body section, .content-body h2, .content-body h3, .content-body p');
    let results = [];
    
    contentSections.forEach(function(section) {
        const text = section.textContent.toLowerCase();
        if (text.includes(query)) {
            results.push(section);
        }
    });
    
    if (results.length > 0) {
        // Scroll to first result
        results[0].scrollIntoView({ behavior: 'smooth' });
        
        // Highlight results
        clearHighlights();
        
        results.forEach(function(section) {
            highlightText(section, query);
        });
    } else {
        alert('No results found for "' + query + '"');
    }
}

function clearHighlights() {
    const highlighted = document.querySelectorAll('.search-highlight');
    highlighted.forEach(function(el) {
        const parent = el.parentNode;
        parent.replaceChild(document.createTextNode(el.textContent), el);
        parent.normalize();
    });
}

function highlightText(element, query) {
    const nodes = element.childNodes;
    
    for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];
        
        if (node.nodeType === Node.TEXT_NODE) {
            const text = node.nodeValue;
            const lowercaseText = text.toLowerCase();
            const index = lowercaseText.indexOf(query);
            
            if (index >= 0) {
                const before = text.substring(0, index);
                const match = text.substring(index, index + query.length);
                const after = text.substring(index + query.length);
                
                const span = document.createElement('span');
                span.className = 'search-highlight';
                span.style.backgroundColor = 'yellow';
                span.appendChild(document.createTextNode(match));
                
                const fragment = document.createDocumentFragment();
                fragment.appendChild(document.createTextNode(before));
                fragment.appendChild(span);
                fragment.appendChild(document.createTextNode(after));
                
                element.replaceChild(fragment, node);
                return; // Found one match, no need to continue
            }
        } else if (node.nodeType === Node.ELEMENT_NODE && !['SCRIPT', 'STYLE'].includes(node.tagName)) {
            highlightText(node, query);
        }
    }
}

// Highlight current page in navigation
function highlightCurrentPage() {
    const currentPath = window.location.pathname;
    const filename = currentPath.substring(currentPath.lastIndexOf('/') + 1);
    
    const navLinks = document.querySelectorAll('.sidebar-nav a');
    
    navLinks.forEach(function(link) {
        const href = link.getAttribute('href');
        if (href === filename || (filename === '' && href === 'index.html')) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// Image lightbox functionality
function initImageLightbox() {
    const images = document.querySelectorAll('.content-body img:not(.no-lightbox)');
    
    if (images.length === 0) return;
    
    // Create lightbox container
    const lightbox = document.createElement('div');
    lightbox.className = 'lightbox';
    lightbox.style.display = 'none';
    lightbox.style.position = 'fixed';
    lightbox.style.top = '0';
    lightbox.style.left = '0';
    lightbox.style.width = '100%';
    lightbox.style.height = '100%';
    lightbox.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    lightbox.style.zIndex = '1000';
    lightbox.style.alignItems = 'center';
    lightbox.style.justifyContent = 'center';
    
    const lightboxImage = document.createElement('img');
    lightboxImage.style.maxWidth = '90%';
    lightboxImage.style.maxHeight = '90%';
    lightboxImage.style.boxShadow = '0 0 20px rgba(0, 0, 0, 0.5)';
    
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '<i class="fas fa-times"></i>';
    closeButton.style.position = 'absolute';
    closeButton.style.top = '1rem';
    closeButton.style.right = '1rem';
    closeButton.style.backgroundColor = 'transparent';
    closeButton.style.color = '#fff';
    closeButton.style.fontSize = '1.5rem';
    closeButton.style.border = 'none';
    closeButton.style.cursor = 'pointer';
    
    lightbox.appendChild(lightboxImage);
    lightbox.appendChild(closeButton);
    document.body.appendChild(lightbox);
    
    // Add click event to images
    images.forEach(function(img) {
        img.style.cursor = 'pointer';
        
        img.addEventListener('click', function() {
            lightboxImage.src = img.src;
            lightbox.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        });
    });
    
    // Close lightbox on button click
    closeButton.addEventListener('click', function() {
        lightbox.style.display = 'none';
        document.body.style.overflow = '';
    });
    
    // Close lightbox when clicking outside image
    lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox) {
            lightbox.style.display = 'none';
            document.body.style.overflow = '';
        }
    });
} 