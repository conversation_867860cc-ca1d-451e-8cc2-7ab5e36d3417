/* Global Styles */
:root {
    --primary-color: #10b981;
    --secondary-color: #059669;
    --accent-color: #ffbe0b;
    --text-color: #333;
    --text-light: #666;
    --bg-color: #f8f9fa;
    --sidebar-bg: #fff;
    --card-bg: #fff;
    --border-color: #e0e0e0;
    --success-color: #06d6a0;
    --warning-color: #ffd166;
    --danger-color: #ef476f;
    --code-bg: #f5f5f5;
    --sidebar-width: 280px;
    --header-height: 70px;
    --content-padding: 30px;
    --border-radius: 6px;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: var(--text-color);
}

h1 {
    font-size: 2.25rem;
}

h2 {
    font-size: 1.75rem;
    margin-top: 2rem;
}

h3 {
    font-size: 1.35rem;
    margin-top: 1.5rem;
}

h4 {
    font-size: 1.1rem;
}

p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

ul, ol {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

code {
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    background-color: var(--code-bg);
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.9em;
}

pre {
    background-color: var(--code-bg);
    padding: 1rem;
    border-radius: var(--border-radius);
    overflow-x: auto;
    margin-bottom: 1.5rem;
}

pre code {
    background-color: transparent;
    padding: 0;
}

button {
    cursor: pointer;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: var(--secondary-color);
}

/* Layout Styles */
.documentation-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 100;
    box-shadow: var(--box-shadow);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.sidebar-header h2 {
    margin-bottom: 0.25rem;
    color: var(--primary-color);
}

.sidebar-header p {
    margin-bottom: 0;
    font-size: 0.9rem;
    color: var(--text-light);
}

.sidebar-nav {
    flex-grow: 1;
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.sidebar-nav li {
    margin-bottom: 0.25rem;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar-nav a:hover {
    background-color: rgba(58, 134, 255, 0.08);
    color: var(--primary-color);
}

.sidebar-nav a.active {
    background-color: rgba(58, 134, 255, 0.12);
    color: var(--primary-color);
    border-left: 3px solid var(--primary-color);
}

.sidebar-nav i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 1.5rem;
    text-align: center;
}

.submenu {
    margin-left: 1.5rem;
    font-size: 0.9rem;
    display: none;
}

.sidebar-nav li:hover .submenu {
    display: block;
}

.submenu a {
    padding: 0.5rem 1.5rem;
}

.sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    font-size: 0.8rem;
    text-align: center;
}

/* Content Styles */
.content {
    flex-grow: 1;
    margin-left: var(--sidebar-width);
    overflow-y: auto;
}

.content-header {
    background-color: var(--card-bg);
    padding: 1.5rem var(--content-padding);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 90;
    box-shadow: var(--box-shadow);
}

.content-header h1 {
    margin-bottom: 0;
}

.search-container {
    display: flex;
    align-items: center;
}

.search-container input {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    min-width: 250px;
}

.search-container button {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    padding: 0.5rem 1rem;
}

.content-body {
    padding: var(--content-padding);
    max-width: 1200px;
    margin: 0 auto;
}

/* Introduction Section */
.introduction {
    margin-bottom: 3rem;
}

.video-container {
    margin: 2rem 0;
}

.video-container iframe {
    width: 100%;
    max-width: 760px;
    height: 425px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin: 1rem 0;
}

/* Features */
.feature-grid, .quick-link-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.feature-card, .quick-link-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover, .quick-link-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.feature-card i, .quick-link-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.quick-link-card {
    display: block;
    color: var(--text-color);
}

.quick-link-card:hover {
    color: var(--primary-color);
}

.quick-link-card p {
    margin-bottom: 0;
}

/* Installation Page Specific */
.installation-steps {
    margin: 2rem 0;
}

.step-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
}

.step-number {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    line-height: 2rem;
    text-align: center;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    margin-right: 0.75rem;
}

/* API Reference Styles */
.api-endpoint {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    overflow: hidden;
}

.endpoint-header {
    display: flex;
    padding: 1rem;
    background-color: var(--code-bg);
    border-bottom: 1px solid var(--border-color);
}

.http-method {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: bold;
    margin-right: 1rem;
}

.method-get {
    background-color: #61affe;
    color: white;
}

.method-post {
    background-color: #49cc90;
    color: white;
}

.method-put {
    background-color: #fca130;
    color: white;
}

.method-delete {
    background-color: #f93e3e;
    color: white;
}

.endpoint-url {
    font-family: monospace;
    font-weight: 500;
}

.endpoint-body {
    padding: 1rem;
}

/* Responsive Styles */
@media (max-width: 991px) {
    .content-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .search-container {
        margin-top: 1rem;
        width: 100%;
    }

    .search-container input {
        flex-grow: 1;
    }

    .feature-grid, .quick-link-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 767px) {
    :root {
        --sidebar-width: 240px;
        --content-padding: 20px;
    }

    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .content {
        margin-left: 0;
    }

    .menu-toggle {
        display: block;
        position: fixed;
        top: 1rem;
        left: 1rem;
        z-index: 110;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--box-shadow);
    }
  
    .content-header {
        padding-top: 4rem;
    }

    .video-container iframe {
        height: 300px;
    }
}

@media (max-width: 576px) {
    :root {
        --content-padding: 15px;
    }

    h1 {
        font-size: 1.75rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    h3 {
        font-size: 1.25rem;
    }

    .feature-grid, .quick-link-grid {
        grid-template-columns: 1fr;
    }

    .video-container iframe {
        height: 200px;
    }
} 