# AI Tool Finder

A modern web application for discovering, showcasing, and managing AI tools. Built with React, TypeScript, and Node.js.

![AI Tool Finder](screenshot.png)

## Features

- 🔍 **AI Tools Directory** - Browse, search, and filter AI tools by categories
- 👤 **User Accounts** - Registration, login, and personalized experience
- 📊 **User Dashboard** - Save favorite tools, track activity, and manage submissions
- ⭐ **Dynamic Reviews** - Rate and review AI tools with admin moderation
- 📱 **Responsive Design** - Optimized for desktop, tablet, and mobile
- 🔒 **Secure Authentication** - Powered by Clerk
- 🛠️ **Tool Submission** - Allow users to submit new AI tools
- 📰 **News Section** - Stay updated with the latest in AI
- 👑 **Admin Dashboard** - Moderate content and manage users
- 🖼️ **Dynamic Logos** - Configurable logos for light and dark modes

## New in Version 1.0.4

- **Enhanced Site Settings** - Improved admin control panel for site configuration
- **Robust Logo Management** - Upload and manage logos for default, light and dark modes
- **Improved Error Handling** - Better feedback and recovery from errors
- **Enhanced Security** - Improved authentication flow for admin operations
- **CORS Improvements** - Fixed cross-origin issues for smoother operation
- **Server Health Monitoring** - Added health check endpoints for better diagnostics
- **Fallback Mechanisms** - Added direct API fallbacks for more reliable saving

## Tech Stack

### Frontend
- React
- TypeScript
- Vite
- Tailwind CSS
- React Router
- React Query
- Clerk Authentication

### Backend
- Node.js
- Express
- MongoDB
- Mongoose
- TypeScript
- Zod (validation)
- Cloudinary (image hosting)

## Getting Started

### Prerequisites
- Node.js (v16+)
- npm or bun
- MongoDB

### Installation

1. **Clone the repository**
   ```
   git clone https://github.com/yourusername/ai-tool-finder.git
   cd ai-tool-finder
   ```

2. **Frontend Setup**
   ```
   cd Ai-Tool-Finder-Frontend
   npm install
   cp .env.example .env.local
   ```
   Edit `.env.local` with your configuration

3. **Backend Setup**
   ```
   cd ../Ai-Tool-Finder-Backend
   npm install
   cp .env.example .env
   ```
   Edit `.env` with your configuration, including Stripe and Cloudinary keys

4. **Start Development Servers**
   
   Frontend:
   ```
   cd Ai-Tool-Finder-Frontend
   npm run dev
   ```
   
   Backend:
   ```
   cd Ai-Tool-Finder-Backend
   npm run dev
   ```

## Environment Variables

### Backend
```
PORT=3005
MONGODB_URI=mongodb://localhost:27017/ai-tool-finder
CLERK_SECRET_KEY=your_clerk_secret_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
FRONTEND_URL=http://localhost:5173
```

### Frontend
```
VITE_API_URL=http://localhost:3005
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
```

## Documentation

For detailed documentation, please see the [Documentations](Documentations) directory included with this project.

## Changelog

See the [CHANGELOG.md](CHANGELOG.md) file for details on all changes in each version.

## License

This project is licensed under the [ThemeForest Standard License](https://themeforest.net/licenses/standard).

## Support

For support, please contact <NAME_EMAIL> or visit our support forum.

---

© 2024 AI Tool Finder. All rights reserved. 